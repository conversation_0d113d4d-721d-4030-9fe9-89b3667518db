<div class="card list-header"
     style="height: 70px; margin: 15px 30px 0;">
  <div class="header">
    <button class="{{activeTab === 'mining' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('mining')">
      A - Mining
    </button>
    <button class="{{activeTab === 'sgrinder' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('sgrinder')">
      B - Soul Grinder
    </button>
    <button class="{{activeTab === 'laboratory' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('laboratory')">
      C - Lab
    </button>
    <button class="{{activeTab === 'bparchive' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('bparchive')">
      D - Forge
    </button>
    <button class="{{activeTab === 'profanarium' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('profanarium')">
      E - WorkShop
    </button>
  </div>
</div>
<app-laboratory-generator *ngIf="activeTab === 'laboratory'">
</app-laboratory-generator>

<app-mining-generator *ngIf="activeTab === 'mining'"></app-mining-generator>

<app-blueprint-archive-generator *ngIf="activeTab === 'bparchive'">
</app-blueprint-archive-generator>

<app-souls-grinder-generator *ngIf="activeTab === 'sgrinder'">
</app-souls-grinder-generator>

<app-profanarium-generator *ngIf="activeTab === 'profanarium'">
</app-profanarium-generator>