import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Area, Character, Dialogue, Level, Marker, Mission, StoryBox } from 'src/app/lib/@bus-tier/models';
import { AreaService, CharacterService, ClassService, DialogueService, ItemService, LevelHelperService, MissionService, ReviewService } from 'src/app/services';
import { LevelService } from 'src/app/services/level.service';
import { MarkerService } from 'src/app/services/marker.service';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { getMarkerRequirementsByType, invalidRequirementsByMarkerTypes } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard/IMarker';
import { GameTypes, MarkerMessageType, MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { LocationPipe } from '../../../../../../../../../../pipes/location.pipe';

interface SpokePlace
{
  elementId: string,
  text: string,
  type?:number
}

interface LevelLocation {
  locationLevel: string,
  id: string,
}

@Component({
  selector: 'tr[app-marker]',
  templateUrl: './marker.component.html',
  styleUrls: ['./marker.component.scss']
})


export class MarkerComponent implements OnInit 
{
  types: RoadBlockType[] = [
    RoadBlockType.DEFEATED_BOSS,
    RoadBlockType.OBTAINED_ITEM,
    RoadBlockType.TALKED_TO_CHARACTER,
    RoadBlockType.COLLECTED_CLASS,
    RoadBlockType.SPOKE_IN,
    RoadBlockType.KARMIC_EQUILIBRIUM,
  ];

  typeNames: string[] = [
    "Defeated Boss",
    "Obtained Item",
    "Talked to Character",
    "Collected Class",
    "Spoke In",
    "Karmic Equilibrium",
  ];

  karmicTypes: string[] = 
  [
    ">=",
    "<=",
    "==",
    "=/="
  ];


  @Input() index: number;
  @Input() storyBox: StoryBox;
  @Input() markerId: string;
  @Input() toMove: (marker: Marker, transpose: number) => void;
  @Input() toRemove: (marker: Marker) => void;

  public marker: Marker;
  public preloadedMissions: Mission[];
  public preloadedLevels: Level[];
  public preloadedCharacters: Character[];
  public levelId: string;
  public isRequired: { [requirement: string]: boolean } = {};
  public preloadedMarkerTypes: MarkerType[];
  public unlockMarkerTypes: MarkerMessageType[];
  public spokePlaces: SpokePlace[] = [];
  public displaySpokePlaces: SpokePlace[] = [];
  listAreas: LevelLocation[] = [];
  itemListMessageContext = [];
  missions: Mission[] = [];
  isNameMessageType: string = '';
  isModalInfo = false;

  markerTitle : string = 'CHARACTER ID';
  displayList = [];
  isBossMarker: boolean = false;
  isCondition: boolean = false;
  isSpokeIn: boolean = false;
  oldMarkerType: number;
  typeLocation: number;
  currentLevel: Level;
  isDisabled = true; // O input começa desabilitado

  get MarkerType() 
  {
    return MarkerType;
  }

  constructor(
    private _markerService: MarkerService,
    private _levelService: LevelService,
    private _characterService: CharacterService,
    private _levelHelperService : LevelHelperService,
    private _classService : ClassService,
    private _itemService : ItemService,  
    private _dialogueService: DialogueService,
    private _locationPipe: LocationPipe,
    private _areaService: AreaService,
    private _reviewService: ReviewService,    
    private _missionService: MissionService,
    private _change: ChangeDetectorRef,  
    
   // private _markerMessageTypeNamePipe: MarkerMessageTypeNamePipe
    
  ) {}

  ngOnInit(): void 
  {
    this.marker = this._markerService.svcCloneById(this.markerId);  
    this.preloadedMarkerTypes = this._markerService.markerTypes;
    this.unlockMarkerTypes = this._markerService.markerMessageTypes;
    this.preloadedLevels = this._levelService.models;
    this._characterService.toFinishLoading();
    this.preloadedCharacters = this._characterService.models;
    this.levelId = Level.getSubIdFrom(this.marker.id);
    this.loadRequirements();

    if(this.marker?.unlockCondition)
      this.unlockCondition(this.marker?.unlockCondition);
    
    this.oldMarkerType = this.marker.type;
    this.separateBossMarker();
    this.currentLevel = this._levelService.svcFindById(this.levelId);

    setTimeout(()=>
    {
      //Initialize the array because it is empty at firt when the marker is already choosed
      if(this.marker.unlockCondition == 'Spoke In') this.unlockCondition('Spoke In');
    },1500)
    this.processAndSortLevels();
    this.getSelectedType();    
  }

  getSelectedType()
  {
    this.itemListMessageContext = [];
    this.isNameMessageType = '';
    if(this.marker.contextType == 'Character') {
      this.itemListMessageContext = this.preloadedCharacters;
      this.isNameMessageType = this.itemListMessageContext.find(character => character.id == this.marker.idMessageType).name;
    } 
    else if (this.marker.contextType == 'Mission') {
      this.itemListMessageContext = this._missionService.models;
      this.isNameMessageType = this.itemListMessageContext.find(mission => mission.id == this.marker.idMessageType).name;
    } else {
      this.isNameMessageType = this.marker.contextType = 'Empty';
      this._markerService.svcToModify(this.marker);
    }
  }

  //Look in documentation by the method name.
  separateBossMarker()
  {
    let temp: number[] = [];
    if(+this.marker.type == 4 || +this.marker.type == 8 || +this.marker.type == 12 || +this.marker.type == 0)
    {
      for(let i = 0; i < this.preloadedMarkerTypes.length; i++)
      {
        if(+this.preloadedMarkerTypes[i] == 4 || +this.preloadedMarkerTypes[i] == 8 || +this.preloadedMarkerTypes[i] == 12)
        {
          temp.push(this.preloadedMarkerTypes[i]);
        }
      }
      
      this.isBossMarker = true;
    }
    else
    {
      for(let i = 0; i < this.preloadedMarkerTypes.length; i++)
      {
        if(+this.preloadedMarkerTypes[i] != 4 && +this.preloadedMarkerTypes[i] != 8 && +this.preloadedMarkerTypes[i] != 12)
        {
          temp.push(this.preloadedMarkerTypes[i]);
        }
      }
      this.isBossMarker = false;
    }
    this.preloadedMarkerTypes = [];
    this.preloadedMarkerTypes = temp; 
  }

  public loadRequirements() 
  {
    this.isRequired = {};
    if (this.marker.type === undefined) return;
    
    getMarkerRequirementsByType(
      invalidRequirementsByMarkerTypes,
      this.marker.type
    ).forEach((param) => 
    {
      this.isRequired[param.key] = true;
    });
  }


  processAndSortLevels() {
    this.preloadedLevels.forEach(levels => {
      
      const dialogBlocks = this._dialogueService.models.filter(dialogue => dialogue.type == 3 && dialogue.boxIds.length > 0);

      if (levels.dialogueIds != undefined) {
   
       const hasMatchingDialogueId = levels.dialogueIds.some(dialogueId =>
         dialogBlocks.some(dialogBlock => dialogueId === dialogBlock.id)
       );
       
       if (hasMatchingDialogueId) {
         const level = this.getLocationArea(levels.id);        
   
         if (level.length > 0) {
          this.listAreas.push(...level);             
          }      
       }  
      }  
    });
    this.getOrderedLevels(); 
  }
  
  getLocationArea(variousIds) {

   const ids = [].concat(variousIds);
    const locations: LevelLocation[] = [];
    
    ids.forEach((id) => 
    {
      if (!id?.includes('ML')) 
      {
        const areaId = Area.getSubIdFrom(id);
        const area = this._areaService.svcFindById(areaId);
        let hierarchyCode = '';
        const levelId = Level.getSubIdFrom(id);    
        const level = this._levelService.svcFindById(levelId);
        const dialogueId = Dialogue.getSubIdFrom(id, 'PT-BR');
        const dialogue = this._dialogueService.svcFindById(dialogueId);
        
        hierarchyCode = this._areaService.svcFindById(areaId)?.hierarchyCode;

        if(this._reviewService.reviewResults[levelId]?.index)
        {
          const location = (hierarchyCode ? '[' + hierarchyCode + '] ' : '') +
          (level ? this._reviewService.reviewResults[levelId]?.index + ' ' +
          (level.name ? '"' + level.name + '" ' : '') + (dialogue ? 
          '(' + GameTypes.dialogueTypeDisplay[+dialogue.type] + ')' : '') : area.name);       

          locations.push({locationLevel: location, id: level.id });            
        }
      }     
    });
    return locations;   
  }
  // Habilita o input ao clicar ou passar o mouse
  enableSearch() {
    this.isDisabled = false;
  }
  
  // Desabilita o input se estiver vazio e o mouse não estiver sobre ele
  disableSearchIfEmpty() {
    if (!this.filterObtainedTextFromList) {
      this.isDisabled = true;
    }
  }

  getOrderedLevels() 
  {
    this.listAreas.sort((a, b) => {
      // Extrai o conteúdo dentro dos colchetes usando uma expressão regular
      const regex = /\[([^\]]+)\]/;
    
      const matchA = a.locationLevel.match(regex);
      const matchB = b.locationLevel.match(regex);
    
      // Se ambos possuem uma correspondência, compará-los alfabeticamente
      if (matchA && matchB) {
        return matchA[1].localeCompare(matchB[1]);
      }
      // Caso uma das strings não contenha colchetes, não alterar a ordem
      return 0;
    });
       
  }
  

  onChangeType() { 

    if(this.marker.contextType == 'Empty') {
      this.marker.contextType = 'Empty';
      this.marker.idMessageType = undefined;
      this._markerService.svcToModify(this.marker);
      return ;
    }
    else if (this.marker.contextType === "Character") {
     this.itemListMessageContext = this.preloadedCharacters;
    } 
    else  if (this.marker.contextType === "Mission") {
      this.itemListMessageContext = this._missionService.models;
    } else {
     this._markerService.svcToModify(this.marker);
      this._markerService.toSave(); 
    }
  }

  public async onChangeMessage(message: string) {

    if (!message || message.trim() === '') {
      this.marker.contextType = 'Empty';
      this.marker.idMessageType = undefined;
      return;
    } 
    else {
        if (this.marker.contextType === "Character") {
          this.marker.idMessageType = this._characterService.models.filter(character => character.name == message)[0]?.id;
        }
        else {//Mission
          this.marker.idMessageType = this._missionService.models.filter(mission => mission.name == message)[0]?.id;  
        } 
    }
    await this._markerService.svcToModify(this.marker);
    this._markerService.toSave(); 
  }

  public async onChange(selectedLevel?) 
  {
    if(+this.marker.type == 12)//Block_Grind
    {
      this.currentLevel.blockGrind = true;
      this.oldMarkerType = 12;
      await this._levelService.svcToModify(this.currentLevel);
    }
    else if(+this.oldMarkerType == 12)
    {
      this.oldMarkerType = +this.marker.type;
      //If there just one marker in this level with true then the BlockGrind should be false.
      if(this.verifyBlockGrindType() <= 1)
      {
        this.currentLevel.blockGrind = false;
        await this._levelService.svcToModify(this.currentLevel);
      }
    }
    if(+this.marker.type == 4)//Block Battle
    {
      this.marker.levelId = selectedLevel;
      this.typeLocation = this.getLocation(this.markerId);
      this.marker.origin =  this.typeLocation;
    } 
    await this._markerService.svcToModify(this.marker);
  }

  getLocation(id) {
    const storyId = this.truncateId(id);   
    const dialogue = this._dialogueService.svcFindById(storyId);  
    return dialogue?.type;
  }

    // Função para truncar o ID
   truncateId(id: string): string {
      // Divide a string pelo ponto
      const parts = id.split('.');
      // Remove os últimos dois elementos do array
      const truncatedParts = parts.slice(0, -2);
      // Junta o array de volta em uma string, com pontos como delimitadores
      return truncatedParts.join('.');
    }

  verifyBlockGrindType():number
  {
    let counter = 0; 
    for(let i = 0; i < this._markerService.models.length; i++)
    {
      if(+this._markerService.models[i].type == 12 && this._markerService.models[i]?.id?.split('.D')[0] == this.currentLevel?.id?.trim())
      {
        counter = counter + 1;
        if(counter >= 2) break;
      }
    }
    return counter;
  }

  async unlockCondition(selectedItem)
  {
    this.displayList = [];
    this.isCondition = true;
    if(selectedItem == 0 || selectedItem == "Defeated Boss")//display characters
    {
      this.markerTitle = 'CHARACTER ID';
      this.isSpokeIn = false;
      this._characterService.models.forEach(char =>
      {
        if(char.type == 3)
          this.displayList.push(char.name);
      });
    }
    else if(selectedItem == 1 || selectedItem == "Obtained Item")
    {
      this.markerTitle = 'ITEM REQUIREMENT';
      this.isSpokeIn = false;
      this._itemService.models.forEach(item => 
      {
          this.displayList.push(item.name);
      });
    }
    else if(selectedItem == 2 || selectedItem == "Talked to Character")
    {
      this.markerTitle = 'CHARACTER ID';
      this.isSpokeIn = false;
      this._characterService.models.forEach(char =>
        {
          this.displayList.push(char.name);
        });
    }
    else if(selectedItem == 4 || selectedItem == "Collected Class")
    {
      this.markerTitle = 'CLASS';
      this.isSpokeIn = false;
      this._classService.models.forEach(clas =>
      {
        this.displayList.push(clas.name);
      });
    }
    else if(selectedItem == 5 || selectedItem == "Spoke In")
    {
      this.markerTitle = 'PLACES';
      this.isSpokeIn = true;
      this._levelHelperService.models.forEach(level => 
      {
        this.spokePlaces.push(level);      
      });   
      this.displaySpokePlaces = this.spokePlaces;
    }
    else if(selectedItem == 6 || selectedItem == "Karmic Equilibrium")
    {
      this.markerTitle = 'KARMIC EQUILIBRIUM';
      this.isSpokeIn = false;
      this.displayList = this.karmicTypes;
    }
     this.marker.unlockCondition = selectedItem;
    await this._markerService.svcToModify(this.marker);
    await this._markerService.toSave();

  }

  async onChangeUnlockCondition(selectedItem)
  {
    this.marker.choosedCondition = selectedItem;
    await this._markerService.svcToModify(this.marker);
    await this._markerService.toSave();
  }

  //Search  
  filterObtainedTextFromList: string = '';
  filterSpokePlacesListItem(event)
  {  
    this.displaySpokePlaces = this._markerService.filterListItem(event, this.spokePlaces, 'text');
  } 

  async changeItemAmount(inputAmount)
  {
    this.marker.amountRequired = inputAmount;
    await this._markerService.svcToModify(this.marker);
    await this._markerService.toSave();
  }

    //modal  
closeAreaStatsPopup() {
    this.isModalInfo = false;
  }
  //modal
  onModalClick(): void {
    this.isModalInfo = !this.isModalInfo;
  }


  async conditionOperator(condition)
  {
    this.marker.conditionOperator = condition;
    await this._markerService.svcToModify(this.marker);
    await this._markerService.toSave();
  }
}
