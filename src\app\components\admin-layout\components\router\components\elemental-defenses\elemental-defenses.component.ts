import { Component } from '@angular/core';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { UserSettingsService } from 'src/app/services';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { ElementalDefenses } from 'src/app/lib/@bus-tier/models/ElementalDefenses';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-elemental-defenses',
  templateUrl: './elemental-defenses.component.html',
  styleUrls: ['./elemental-defenses.component.scss']
})
export class ElementalDefensesComponent  extends TranslatableListComponent<ElementalDefenses> 
{
  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _elementalDefensesService: ElementalDefensesService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _translationCheckService: TranslationCheckService,

  ) 
  {
    super(_elementalDefensesService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  description:string = '';
  public language: language = 'PT-BR';
  public elementalDefenses: ElementalDefenses[] = [];
  public eleDefensesList: ElementalDefenses[] = [];
  public elements:string[] = [];
  selectedElement:string = 'Defenses - Ailment';
  public queryValue: string;
 
  public readonly statusTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addStatus.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };
  
  override async ngOnInit ()
  {
    await this._elementalDefensesService.toFinishLoading(); 
    this.elements = this._elementalDefensesService.elements;    
    this.separateListByElemental();
  }

  changeElement(element: string)
  {
    this.selectedElement = element;
    this.separateListByElemental();
  }

  separateListByElemental()
  {
    this.elementalDefenses = [];   
    this.elementalDefenses = this._elementalDefensesService.models.filter((el) => el.selectDrop == this.selectedElement && el.defenses != "" && el.defenses != undefined);
    this.sortByDefenses();
    this.description = `Showing ${this.elementalDefenses.length} results`;
  }

  async addStatus()
  {
    let Defenses;

    try 
    {
      Defenses = await this._elementalDefensesService.svcPromptCreateNewElementalDefenses(this.selectedElement);
    } 
    catch (e) 
    {
      Alert.showError("This Defeneses already exists!");
      return
    }
    if(!Defenses) return;
    this.ngOnInit();
  }

  async removeElement(status : ElementalDefenses)
  {
    const confirm = await Alert.showRemoveAlert(status.defenses);  
    if (!confirm) return;
    
    this._elementalDefensesService.models = await this._elementalDefensesService.models.filter(s => s.id !== status.id);
    await this._elementalDefensesService.toSave();
    this.elementalDefenses = this.elementalDefenses.filter(s => s !== status);
    this.ngOnInit();
  }

  public getElementalDefensesOrtography(status: ElementalDefenses)
  {
    this._translationService.getElementalDefensesOrtography(status, true);
  }

  //The two below sort methods are here because it dosent work with the old code but the others parameters did worked.
  sortBySkillOrder = -1;
  sortByDefenses() 
  {
    this.sortBySkillOrder *= -1;
    this.elementalDefenses.sort((a, b) => 
    {
        return this.sortBySkillOrder *  a.defenses.localeCompare(b.defenses);
    });
    this.lstFetchLists();
  }
 
  sortByAcronymOrder = -1;
  sortByAcronym() 
  {
    this.sortByAcronymOrder *= -1;
    this.elementalDefenses.sort((a, b) => 
    {
        return this.sortByAcronymOrder *  a.acronym.localeCompare(b.acronym);
    });
    this.lstFetchLists();
  }

  search(searchWord:string) {
    searchWord =  searchWord.toLowerCase();    
    this.eleDefensesList = this.elementalDefenses;

    if(searchWord == '') return this.separateListByElemental();

    this.elementalDefenses = []
      
    for (let index = 0; index < this.eleDefensesList.length; index++) {
      if (this.eleDefensesList[index]?.defenses?.toLowerCase().includes(searchWord) || 
      this.eleDefensesList[index]?.acronym?.toLowerCase().includes(searchWord) ||
      this.eleDefensesList[index]?.description?.toLowerCase().includes(searchWord)) {
        this.elementalDefenses.push(this.eleDefensesList[index]);
      }      
    }   
    this.description = `Showing ${this.elementalDefenses.length} results`;
    return this.elementalDefenses;
  }

  defenseChange(elementalDefenses: ElementalDefenses, defenses: string, value: string) {
    elementalDefenses.isReviewedDefenses = false;
    elementalDefenses.revisionCounterDefensesAI = 0;
    this.lstOnChange(elementalDefenses, 'defenses', value);
  }

  descriptionChange(elementalDefenses: ElementalDefenses, description: string, value: string) {
    elementalDefenses.isReviewedDescription = false;
    elementalDefenses.revisionCounterDescriptionAI = 0;
    this.lstOnChange(elementalDefenses, 'description', value);
  }
}
