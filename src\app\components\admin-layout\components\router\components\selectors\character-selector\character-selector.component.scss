

.character
{
  background-color: rgb(226, 226, 226);
  margin-right: 10px;
  padding: 5px;
  border-radius: 3px;
}


.character-group
{
  overflow-wrap: break-word;
  min-height: 50px;
  border: 1px solid rgb(199, 199, 199);
  background-color: #ffffff;
  padding: 10px;
  line-height: 2.2;
  cursor: pointer;
}


.character-group:hover
{
  background-color: rgb(144, 160, 212);
}

.sticky th
{
  top: 130px!important;
}

.wrapper
{
  height: 90%;
  width: 110vh;
  margin: auto;
  margin-top: 50px;
  margin-bottom: 50px;
  min-height: 0px;
  overflow: overlay;
  overflow-x: hidden;
}
.popup.hide
{
  display: none;
}
.popup
{
    position: fixed;
    height: 100%;
    width: 100%;
    background-color: hsla(0, 0%, 15%, 0.24);
    z-index: 150;

    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;

    text-align: center;
}

.table-popup th
{
  top: 0px;
}

input[type='checkbox']
{
  width: 20px;
  height: 20px;
}
