<div class="col">
  <div class="card card_answer_choice" id="{{ answerBox?.id }}">
    <div *ngFor="let roadblock of this.roadBlocksUseds">
      <div id="{{roadblock.id}}">
        <app-roadblock [optionBox]="answerBox"
          *ngIf="answerBox?.id.includes(roadblock?.StoryBoxId) && roadblock?.StoryBoxId != optionBox.id"
          [toRemove]="toRemoveProcessConditionFunc" [currentRoadblock]="roadblock" [storyBoxId]="answerBox.id"
          class="center">
        </app-roadblock>
      </div>
    </div>

    <div *ngIf="this.countingRoadblocks > 1" class="componente_AndOr">
      <div style="font-weight: 900; font-size: 20px"
        [ngStyle]="{'opacity': option.AndOrCondition == 'AND' ? 1 : 0.3, 'color':option.AndOrCondition == 'AND' ? '#ff00aa' : 'black'}">
        AND</div>
      <label class="switch">
        <input [checked]="option.AndOrCondition == 'AND' ? false : true" (change)="this.chooseAndOrCondition($event)"
          type="checkbox">
        <span class="slider"></span>
      </label>
      <div style="font-weight: 900; font-size: 20px;"
        [ngStyle]="{'opacity': option.AndOrCondition == 'OR' ? 1 : 0.3, 'color': option.AndOrCondition == 'OR' ? 'blue' : 'black'}">
        OR</div>
    </div>

    <div class="table-responsive" style="height: 100%" id="{{ answerBox?.id }}">
      <div style="padding: 15px 15px 0; display: flex; justify-content: space-between;">
        <p class="category">
          {{ optionBox | typeName }} > {{ answerBox?.id }}
        </p>
        <ng-container *ngIf="answerBox?.id && answerBox?.choicePositive || answerBox?.investigationPositive">
          <div style="float: right; gap: 10px; display: flex; justify-content: end;" id="{{ answerBox?.id }}">
            <i class="pe-7s-like2" style="font-size: 30px; color: #87cb16;"></i>
            <p>{{option?.choicePositive}}</p>
          </div>
        </ng-container>
        <ng-container *ngIf="answerBox?.id && answerBox?.choiceNegative || answerBox?.investigationNegative">
          <div style="float: right; gap: 10px; display: flex; justify-content: end;" id="{{ answerBox?.id }}">
            <i class="pe-7s-like2" style="font-size: 30px; color: red; transform: scaleY(-1);"></i>
            <p>{{option?.choiceNegative}}</p>
          </div>
        </ng-container>
      </div>


      <!--List of Dialog Box Progresses-->
      <!--Before we used the answerBox, it was option (see option structure)-->
      <div>
        <input class="form-control form-short form-title component_label" style="font-size: 18px;" type="text"
          value="{{ !answerBox?.labelOption ? '<<Label for progress condition>>' : answerBox?.labelOption }}" #label
          (change)="changeLabel($event, answerBox)" />


        <div *ngIf="answerBox?.labelOption" style="width: 1px; float: right;">
          <i [ngStyle]="{'color': isUsedRoadblock(answerBox) ? '#00ff04' : '#000' }"
            class="pe-7s-key pe-5x pe-va key-icon">
            <div id="text-size" *ngIf="isUsedRoadblock(answerBox)"
              [ngStyle]="{'margin-right': isUsedRoad ? '-33px' : '' }" class="circle branch-circle positionCircle"
              tooltip="Used on: {{usedOnLevels | enumerateList}}" placement='top' delay='250' ttPadding="10px"
              ttWidth="{{usedOnLevels | textSize}}">
              <p style="text-align: center;">{{usedOnLevels?.length}}</p>
            </div>
          </i>
        </div>
      </div>

      <div style="display: flex; justify-content: center; text-align: center;">
        <div >
          <p *ngIf="option?.choiceAtributte">Teste {{option?.choiceAtributte}}</p>
          <p>{{option?.descriptionInvestigation}}</p>
        </div>
      </div>

      <!--DESCRIPTION CHOICE BOX-->
      <div style=" display: flex; justify-content: center;">
        <div *ngIf="answerBox?.choicePositive && answerBox.id"
          style="display: flex; align-items: center;">
          <span class="spanValue">≥</span>
          <p style="font-size: 62px;" id="dcValue">{{answerBox.id ? answerBox?.resultDCOption : ''}}</p>
        </div>
        <div *ngIf="answerBox?.choiceNegative && answerBox.id"
          style="display: flex; align-items: center;">
          <span class="spanValue">
            < </span>
              <p style="font-size: 62px;" id="dcValue">{{answerBox?.id ? answerBox?.resultDCOption : ''}}</p>
        </div>
      </div>
      <p style="text-align: center">{{answerBox?.descriptionDCGuideOption}}</p>
      <h5 style="text-align: center; font-weight: 700">{{answerBox?.subContextDescription}}</h5>


      <br />
      <!--icon play-->
      <i class="icon title {{ answerBox | typeIcon }}"></i>

      <div [innerHTML]="option?.message | rpgFormatting : true" class="form-control form-short form-title no-white">
      </div>
      <table class="table table-hover table-box table table-responsive speech-table">
        <tbody>
          <tr>
            <td>
              <span class="nu-icon pe-7s-info" data-tooltip="Line represents character introduction"></span>
            </td>
          </tr>
          <ng-container *ngFor="let storyProgressId of answerBox?.storyProgressIds let i = index;">
            <ng-container [ngSwitch]="(storyProgressId | storyProgress) | typeName">
              <!-- SPEECH -->
              <ng-container *ngSwitchCase="'Speech'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-speech [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [speechId]="storyProgressId" [index]="i"
                  [preloadedSpeakers]="preloadedSpeakers" [language]="language">
                </tr>
              </ng-container>
              <!-- EVENT -->
              <ng-container *ngSwitchCase="'Event'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-event
                  [preloadedMissionsOfArea]="preloadedMissionsOfArea" [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [eventId]="storyProgressId" [index]="i">
                </tr>
              </ng-container>
              <!-- MARKER -->
              <ng-container *ngSwitchCase="'Marker'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-marker [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [markerId]="storyProgressId" [index]="i">
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
      <div class="row">
        <div class="btn-group">
          <button id="add-event-button" class="btn btn-fill btn-sm btn-mi" (click)="toAddMissionEvent()">
            <i class="pe-7s-camera center"></i>
            Add Mission Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-warning btn-fill btn-item" (click)="toAddItemEvent()">
            <i class="pe-7s-camera center"></i>
            Add Item Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-warning btn-fill btn-boss" (click)="toAddBossEvent()">
            <i class="pe-7s-camera center"></i>
            Add Boss Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-fill btn-cinematic" (click)="toAddCinematicEvent()">
            <i class="pe-7s-camera center"></i>
            Add Cinematic Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-fill btn-loop" (click)="toAddLoopEvent()">
            <i class="pe-7s-camera center"></i>
            Add Loop Event
          </button>
        </div>
        <div class="pull-right">
          <button id="add-message-button" class="btn btn-sm btn-success btn-fill" (click)="toAddRoadblock()">
            <i class="pe-7s-shield center"></i>
            Add Progress Condition
          </button>
          <button id="add-message-button" class="btn btn-sm btn-primary btn-fill" (click)="toAddMarker()">
            <i class="pe-7s-ribbon center"></i>
            Add Marker
          </button>
          <button id="add-marker-button" class="btn btn-sm btn-success btn-fill" (click)="toAddSpeech()">
            <i class="pe-7s-comment center"></i>
            Add Speech
          </button>
        </div>
      </div>
    </div>
  </div>
</div>