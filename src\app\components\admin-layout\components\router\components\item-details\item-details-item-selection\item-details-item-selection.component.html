<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Weapon List'" [cardDescription]="listDescription">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="search($event)"
          (searchOptions)="searchFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortListById()">ID</th>
            <th class="th-clickable" (click)="sortListByWeapon()">Review</th>
            <th style="min-width: 150px;" class="th-clickable" (click)="sortListByName()">Name & Description</th>
            <th class="th-clickable" (click)="sortListByWeapon()">Weapon Record</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of itemList; let i = index; trackBy: trackByIndex">
            <tr id="{{ item.id }}">
              <td class="td-sort">{{ i+1 }}</td>
              <td class="td-id">{{ item.id }}</td>
              <td class="td-id">
                <ng-container *ngIf="!checkIfCompleted(item); else assigned">
                  <i class="pe-7s-attention warning" style="position: relative" placement='top' delay='250'
                    ttWidth="auto" ttAlign="center" ttPadding="15px" tooltip="The weapon record is incomplete"
                    placement="right" delay="100"></i>
                </ng-container>
                <ng-template #assigned>
                  <i class="pe-7s-check success"></i>
                </ng-template>
              </td>
              <td class="td-notes">
                <span style="background-color:rgb(189,189,189)" class="form-control form-short" type="text">
                  {{ (item | translation : lstLanguage : item.id : 'name') }}
                </span>
                <span style="background-color:rgb(189,189,189)" class="form-control" type="text">
                  {{ (item | translation : lstLanguage : item.id : 'description') }}
                </span>
              </td>
              <td class="td-actions">
                <div class="row middle" style="display: flex; width: 46px; margin-bottom: 2px;">
                  <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                    [style.background-color]="checkIfWeaponIsCompleted(item, 1)"></span>
                  <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                    [style.background-color]="checkIfWeaponIsCompleted(item, 2)"></span>
                  <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                    [style.background-color]="checkIfWeaponIsCompleted(item, 3)"></span>
                </div>
                <button class="btn btn-primary btn-fill btn-remove" (click)="selectWeapon(item)">
                  <i class="pe-7s-angle-right-circle"></i>
                </button>
              </td>
              <td class="td-actions">
                <button class="btn btn-gray btn-fill translation-button" (click)="downloadWeaponOrtography(item)">
                  <div class="mat-translate"></div>
                </button>
              </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>