<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[exportExcelButtonTemplate, addNewCharacter]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
        <div class="btn-group character-absolute-tabs" style="display: flex;">
          <h3 style="text-align: center" style="margin-right: 15px;">Groups</h3>
          <ng-container *ngFor="let group of characterGroups">
            <ng-container *ngIf="(lstIds | characters | characterTypeLength: group) > 0">
              <button
                ngClass="btn {{
                lstFilterValue['type'] == group ? (lstFilterValue['type'] | characterTypeButton) || 'btn-fill btn-primary' : 'btn-fill'}}"
                (click)="lstFilterValue['type'] = group; lstSaveFilters()">
                {{ group | characterTypeGroup | titleConvention: "firstCapitalLetter":"plural" }}
                <div style="font-size: 20px">
                  {{ lstIds | characters | characterTypeLength: group }}
                </div>
              </button>
            </ng-container>
          </ng-container>
        </div>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" style="padding-left: 5px;">Index</th>
            <th class="th-clickable" (click)="sortListByIdCharacter()">
              ID
            </th>
            <th class="th-clickable" (click)="sortListByParameter('isCollectible')">
              Collectible
            </th>
            <th class="th-clickable" style="width: 7%; text-align: center;"
              (click)="sortListByParameter('assignedAsBattleCharacter')">
              <div class="battle">Battle<br />Total</div>
              <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                ttPadding="10px" tooltip="The total number of levels where the character engages in battles."
                class="pe-7s-info batt" style="top: 8px !important;"></i>
            </th>
            <th class="th-clickable" style="width: 7%; text-align: center;"
              (click)="sortListByParameter('assignedAsBattleCharacterOnAssignedArea')">
              <div class="battle"> Battle<br />Area</div>
              <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                ttPadding="10px" tooltip="The number of levels where the character battles within its associated area."
                class="pe-7s-info batt" style="top: 8px !important;"></i>
            </th>
            <th class="th-clickable" style="width: 7%; text-align: center;"
              (click)="sortListByParameter('assignedAsSpeaker')">
              Speaker
              <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                ttPadding="10px" tooltip="The number of levels where the character has dialogues."
                class="pe-7s-info batt"></i>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('name')">
              Name & Title
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('description')">
              Description
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('birthyear')">
              Birthyear
            </th>
            <th class="th-clickable" (click)="sortListByParameter('age')">
              Age
            </th>
            <th class="th-clickable" (click)="sortListByParameter('height')">
              Height
            </th>
            <th class="th-clickable" (click)="sortListByParameter('gender')">
              Gender
            </th>
            <th class="th-clickable" (click)="sortListByParameter('class')">
              Class
              <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                ttPadding="10px" tooltip="The class or archetype the character belongs to." class="pe-7s-info batt"></i>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('areaId')">
              Area
              <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                ttPadding="10px" tooltip="The area to which the character is associated." class="pe-7s-info batt"></i>
              <!--Area filter dropdown-->
              <select class="dropdown filter-dropdown limited." name="areaIdFilter"
                [(ngModel)]="lstFilterValue['areaId']" (change)="lstOnChangeFilter()">
                <option value="ALL">All</option>
                <option *ngFor="let area of preloadedAreas" value="{{ area.id }}">
                  {{ area.hierarchyCode }}: {{ area.name }}
                </option>
              </select>
            </th>
            <th class="th-clickable" style="width: 7%;" (click)="sortListByParameter('type')">
              Group
              <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                ttPadding="10px" tooltip="The category to which the character is associated."
                class="pe-7s-info batt"></i>
            </th>
            <th title="Delete or translate character information.">Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let character of lstIds | characters; let i = index; trackBy: trackById">
            <tr *ngIf="character.type | filterCharacterByType: lstFilterValue['type']" id="{{ character.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id padId">{{ character.id }}</td>
              <td>
                <input class="form-check-input" type="checkbox" [(ngModel)]="character.isCollectible" (ngModelChange)="
                  lstOnChange( character, 'isCollectible', character.isCollectible )" />
              </td>
              <td>
                <ng-container
                  *ngIf="(character | review).asBattleCharacterCount !== (character | review).asBattleCharacterOnAssignedAreaCount">
                  <ng-container
                    *ngIf="(character | review).asBattleCharacterCount > 0; else notAssignedAsBattleCharacter">
                    <i *ngIf="(character | review).isBattleCharacter; else battleCharacterWarning"
                      class="pe-7s-check success" style="cursor: pointer;"
                      (click)="onModalClick(character, 'total')"></i>

                    <ng-template #battleCharacterWarning>
                      <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                        ttPadding="10px" tooltip="Assigned in battle, but not a battle character"
                        class="pe-7s-attention attention"></i>
                    </ng-template>
                    <div title="Total character appearances in all areas.">
                      {{ (character | review).asBattleCharacterCount }}
                    </div>
                    <!--Modal Battle Total-->
                    <ng-container *ngIf="isBattleTotal">
                      <div class="background-div handleOut" aria-hidden="true">
                        <div id="modal-close" @popup class="popup-report" (mouseleave)="isBattleTotal = false"
                          style="background-color: black;">
                          <div class="modal-header">
                            <ng-container>
                              <p class="modal-title">Assigned at {{(characterIndex | review).asBattleCharacter.length}}
                                {{(characterIndex | review).asBattleCharacter.length >1 ? 'Levels' : 'Level'}}
                                <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                                  data-dismiss="background-div" aria-label="Fechar">
                                  <span aria-hidden="true">&times;</span>
                                </button>
                              </p>
                            </ng-container>
                          </div>
                          <div class="contextBattle">
                            <span> {{((characterIndex | review).asBattleCharacter | location |
                              enumerateList).toString()}}</span>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                    <!--Fim do Modal-->

                  </ng-container>
                  <ng-template #notAssignedAsBattleCharacter>
                    <ng-container *ngIf="(character | review).isBattleCharacter">
                      <i style="position: relative" placement='top' delay='200' ttWidth="250px" ttAlign="left"
                        ttPadding="10px" tooltip="Not assigned in any battle" class="pe-7s-close-circle error"></i>
                      <div>
                        {{ (character | review).asBattleCharacterCount }}
                      </div>
                    </ng-container>
                  </ng-template>
                </ng-container>
              </td>

              <td>
                <ng-container
                  *ngIf=" (character | review).asBattleCharacterOnAssignedAreaCount > 0; else notAssignedAsBattleCharacter">
                  <i *ngIf="(character | review).isBattleCharacter; else battleCharacterWarning"
                    class="pe-7s-check success" style="cursor: pointer;" (click)="onModalClick(character, 'area')"></i>
                  <ng-template #battleCharacterWarning>
                    <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                      ttPadding="10px" tooltip="Assigned in battle, but not a battle character"
                      class="pe-7s-attention attention"></i>
                  </ng-template>
                  <div title="Total character appearances in associated area.">
                    {{(character | review).asBattleCharacterOnAssignedAreaCount}}
                  </div>

                  <!--Modal Batlle Area-->
                  <ng-container *ngIf="isBattleArea">
                    <div class="background-div handleOut" aria-hidden="true">
                      <div id="modal-close" @popup class="popup-report" (mouseleave)="isBattleArea = false"
                        style="background-color: black;">
                        <div class="modal-header">
                          <ng-container>
                            <p class="modal-title">Assigned at {{(characterIndex |
                              review).asBattleCharacterOnAssignedArea.length}} {{(characterIndex |
                              review).asBattleCharacterOnAssignedArea.length >1 ? 'Levels' : 'Level'}}
                              <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                                data-dismiss="background-div" aria-label="Fechar">
                                <span aria-hidden="true">&times;</span>
                              </button>
                            </p>
                          </ng-container>
                        </div>
                        <div class="contextBattle">
                          <span> {{((characterIndex | review).asBattleCharacterOnAssignedArea | location |
                            enumerateList).toString()}}</span>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                  <!--Fim do Modal-->

                </ng-container>
                <ng-template #notAssignedAsBattleCharacter>
                  <ng-container *ngIf="(character | review).isBattleCharacter">
                    <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                      ttPadding="10px" tooltip="Not assigned in any battle in assigned area"
                      class="pe-7s-close-circle error"></i>
                    <div>
                      {{(character | review).asBattleCharacterOnAssignedAreaCount}}
                    </div>
                  </ng-container>
                </ng-template>
              </td>
              <td>
                <ng-container *ngIf="(character | review).asSpeaker.length > 0; else notAssignedAsSpeaker">
                  <i (click)="onModalClick(character, 'speaker')" class="pe-7s-check success"
                    style="cursor: pointer;"></i>
                  <div title="The number of levels where the character has dialogues.">{{ (character |
                    review).asSpeaker.length }}</div>
                </ng-container>
                <ng-template #notAssignedAsSpeaker>
                  <ng-container *ngIf="!(character | review).isBattleCharacter">
                    <i style="position: relative" placement='top' delay='250' ttWidth="175px" ttAlign="center"
                      ttPadding="10px" tooltip="Not assigned as Speaker" class="pe-7s-close-circle error"></i>
                    <div>{{ (character | review).asSpeaker.length }}</div>
                  </ng-container>

                  <!--Modal Speaker-->
                  <ng-container *ngIf="isSpeaker">
                    <div class="background-div handleOut" aria-hidden="true">
                      <div id="modal-close" @popup class="popup-report" (mouseleave)="closeAreaStatsPopup()"
                        style="background-color: black;">
                        <div class="modal-header">
                          <ng-container>
                            <p class="modal-title">Assigned at {{(characterIndex | review).asSpeaker.length}}
                              {{(characterIndex | review).asSpeaker.length >1 ? 'Levels' : 'Level'}}
                              <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                                data-dismiss="background-div" aria-label="Fechar">
                                <span aria-hidden="true">&times;</span>
                              </button>
                            </p>
                          </ng-container>
                        </div>
                        <div class="contextBattle">
                          <span> {{((characterIndex | review).asSpeaker | location | enumerateList).toString()}}</span>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                  <!--Fim do Modal-->
                </ng-template>
              </td>
              <td style="width: 20%">
                <input class="form-control form-short" type="text" name="characterName{{ i }}"
                  value="{{ (character | translation : lstLanguage : character?.id : 'name') }}" #name
                  (change)="this.changeCharacterSelectionWhenEditingName(character, name.value); changeName(character, 'name', name.value); " />
                <input class="form-control form-short background-input-table-color" style="width: 100%" type="text"
                  value="{{ (character | translation : lstLanguage : character?.id : 'title') }}" #title
                  (change)="changeTitle(character, 'title', title.value)" />
              </td>
              <td style="width: 35%; height: 80%">
                <textarea class="form-control background-input-table-color" type="text" style="width: 100%"
                  value="{{ (character | translation : lstLanguage : character?.id : 'description') }}" #description
                  (change)="changeDescription(character, 'description', description.value)">
                </textarea>
              </td>
              <td style="width: 60px">
                <input class="form-control form-short background-input-table-color" type="text"
                  value="{{ character?.birthyear }}" #birthyear
                  (change)="lstOnChange(character, 'birthyear', birthyear.value)" />
              </td>
              <td>
                <div *ngIf="character.birthyear">
                  {{ +character?.birthyear | calculateAge }}
                </div>
              </td>
              <td class="td-8">
                <input class="form-control form-short background-input-table-color" type="text"
                  value="{{ character?.height }}" #height (change)="lstOnChange(character, 'height', height.value)" />
              </td>
              <td>
                <button ngClass="btn {{ character.gender | genderButton }}"
                  (click)="toPromptSelectCharacterGender(character)">
                  {{ (character?.gender | genderName) || "Undefined" }}
                </button>
              </td>
              <td>
                <button [ngClass]="getCharactername(character?.classId) ? 'btn btn-info btn-fill' : 'btn'"
                  (click)="toPromptSelectCharacterClass(character)">
                  {{ (character?.classId | klass)?.name || "Undefined" }}
                  <!-- {{getCharactername(character?.classId)}}-->
                </button>
              </td>
              <td>
                <button [ngClass]="(character?.areaId | area) ? 'btn btn-info btn-fill': 'btn'"
                  (click)="toPromptSelectCharacterBindArea(character)">
                  {{ (character?.areaId | area)?.name || "Undefined" }}
                </button>
              </td>
              <td>
                <button ngClass="btn btn-fill {{ character?.type | characterTypeButton }}"
                  (click)="toPromptChangeCharacterType(character)">
                  {{ (character?.type | characterTypeName) || "Undefined" }}
                </button>
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove" (click)="lstPromptRemove(character)">
                  <i class="pe-7s-close"></i>
                </button>
                <button class="btn btn-gray btn-fill translation-button"
                  (click)="downloadCharacterOrtography(character)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>

          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- Spinner-->
  <ng-container *ngIf="loadingSpinner">
    <app-loading-spinner></app-loading-spinner>
  </ng-container>

</div>