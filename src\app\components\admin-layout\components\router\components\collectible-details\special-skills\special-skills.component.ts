import { ChangeDetectorRef, Component, HostListener, Input } from '@angular/core';
import { AilmentTable, BoostTable, Category, Character, DefensiveTable, DispelTable, HealingTable, NegativeTable, SpecialSkills } from 'src/app/lib/@bus-tier/models';
import { HybridTable } from 'src/app/lib/@bus-tier/models/HybridTable';
import { AilmentIdBlockservice, AilmentTableService, BoostIdBlockservice, BoostTableService, CategoryStatusEffectService, CharacterService, DefensiveIdBlockservice, DefensiveTableService, DispelIdBlockservice, DispelTableService, HealingIdBlockservice, HealingTableService, HybridTableService, NegativeIdBlockservice, RepetitionStatusEffectService, SpecialSkillsService, StatusInfoService, UserSettingsService } from 'src/app/services';
import { NegativeTableService } from '../../../../../../../services/negative-table.service';
import { ActivatedRoute, Router } from '@angular/router';


interface StatusSkill {
  id: string;
  name: string;
  description: string;
  positionID: string;
  skillUser?: string;
  idBoost?: string;
  idNegative?: string;
}

interface Skill {
  category?: string,
  idCategory?: string,
  relationCategory?: string,
  idStatusEffect?: string,
  nameStatusEffect?: string,
  descriptionStatusEffect?: string,
  positionIDStatusEffect?: string,
  positionCategory?: string,
  indexStatusEffect?: number,
  skillUserStatusEffect?: string
}


@Component({
  selector: 'app-special-skills',
  templateUrl: './special-skills.component.html',
  styleUrls: ['./special-skills.component.scss']
})
export class SpecialSkillsComponent {

  @Input() character = '';
  itemCharacter: Character;
  listRepetitions = [];
  listCountRarity = "";
  position = [];
  getPosition = [];
  listCategory: Category[] = [];
  descriptionCatergory: string;
  itemSelectedCategory: string;
  listStringElementalAffinities = [];
  listIds = [];
  listIdBlocks = [];
  listIdHealing = [];
  listIdDefensive = [];
  listIdNegative = [];
  listIdAilment = [];
  listIdDispel = [];
  listIdHybrid = [];
  idsListHibrids = [];
  listDescriptionStatusEffect = [];
  boostTable: BoostTable[] = []
  healingTable: HealingTable[] = []
  defensiveTable: DefensiveTable[] = [];
  negativeTable: NegativeTable[] = [];
  ailmentTable: AilmentTable[] = [];
  dispelTable: DispelTable[] = [];
  hybridTable: HybridTable[] = [];
  indexPosition: number;
  isModalInfo = false;
  listRemainingUnits: { position: number; amount: number }[] = [];

  dropdownOpen = false;
  selectedStatus: StatusSkill;
  listSpecialSkills: SpecialSkills;
  selectedCategory: Category;
  isStatusSelected = false;
  IsMaxCatgeory = false;
  indexCategory: string;
  categoryCount: number;
  isPulsing = false;
  listCalculatedHybrid: number;
  listCalculatedPhysical: number;


  constructor(
    private _characterService: CharacterService,
    private _categoryStatusEffectService: CategoryStatusEffectService,
    private _repetitionStatusEffectService: RepetitionStatusEffectService,
    private _elementalAffinities: StatusInfoService,
    private _specialSkillsService: SpecialSkillsService,
    private ref: ChangeDetectorRef,
    protected _router: Router,
    _userSettingsService: UserSettingsService,
    _activatedRoute: ActivatedRoute,

    //Id Blocks
    private _boostIdBlockservice: BoostIdBlockservice,
    private _healingtIdBlockservice: HealingIdBlockservice,
    private _defensiveIdBlockservice: DefensiveIdBlockservice,
    private _negativeIdBlockservice: NegativeIdBlockservice,
    private _ailmentIdBlockservice: AilmentIdBlockservice,
    private _dispelIdBlockservice: DispelIdBlockservice,

    // Status EEFECT TABLE
    private _boostTableService: BoostTableService,
    private _healingTableService: HealingTableService,
    private _defensiveTableService: DefensiveTableService,
    private _negativeTableService: NegativeTableService,
    private _ailmentTableService: AilmentTableService,
    private _dispelTableService: DispelTableService,
    private _hybridTableService: HybridTableService

  ) { }

  async ngOnInit(): Promise<void> {

    this._characterService.toFinishLoading();
    this.itemCharacter = this._characterService.models.find((character) => character.id === this.character);

    this._repetitionStatusEffectService.toFinishLoading();
    this.listRepetitions = this._repetitionStatusEffectService.models;
    this.ref.detectChanges();

    setTimeout(async () => {
      this.getRarityCount();

      this._categoryStatusEffectService.toFinishLoading();
      this.listCategory = this._categoryStatusEffectService.models;

      this._specialSkillsService.toFinishLoading();
      this.listSpecialSkills = this._specialSkillsService.models.find(skill => skill.idChacracter === this.itemCharacter.id);

      this.calculateRemainingUnits();
      this.calculateHybrid();
      this.calculatePhysical();

      if (this.listSpecialSkills == undefined) {
        this.listSpecialSkills = await this._specialSkillsService.createNewSpecialSkills(this.itemCharacter);
        this.listSpecialSkills.listSpecialSkills = [] as Skill[];
      }
      else {
        if (this.listSpecialSkills.listSpecialSkills.length > 0) {

          this.checkCategoryLimit();
          this.sortSpecialSkillsByCategory();
        }
      }
      this.removeRemainingUnits();
      this.removePhisicalRemainingUnits();
    }, 500);

  }

  removeRemainingUnits() {
    this._specialSkillsService.models.forEach((x) => {
      x.listSpecialSkills.forEach((y) => {
        if (!y.idStatusEffect.includes('HYBRID')) {
          const position = parseInt(y.positionIDStatusEffect) - 1;
          this.listRemainingUnits[position].amount -= 1;
          this.ref.detectChanges();
        }
      });
    });
  }


  removePhisicalRemainingUnits() {
    this._specialSkillsService.models.forEach((x) => {
      if (x.listSpecialSkills.length > 0) {
        x.listSpecialSkills.forEach((y) => {
          if (y?.skillUserStatusEffect.toLowerCase().includes('físico')) {
            this.listCalculatedPhysical -= 1;
            this.ref.detectChanges();
          }
        });
      }
    });

  }

  getRarityCount() {
    this.position = []

    if (!this.listRepetitions || this.listRepetitions.length === 0) {
      console.error("Repetitions list is empty or undefined.");
      return;
    }

    if (!this.itemCharacter || !this.itemCharacter.rarity) {
      console.error("ItemCharacter or rarity is undefined.");
      return;
    }

    // Pegar a posição do Rarity
    this.listRepetitions.forEach(repetition => {
      repetition.positionNameRarity.forEach((rarity, index) => {
        if (rarity === this.itemCharacter.rarity) {
          this.position.push(index);
        }
      });
    });

    // Formata o resultado a posição
    this.getPosition = [];
    this.position.forEach((x) => {
      this.getPosition.push(this.getPositionRarity(x));
    });

    this.listCountRarity = this.getPosition.join(' - ');
  }

  getPositionRarity(index: number) {
    let position = '';

    if (index === 0) {
      position = '1';
    }
    else if (index === 1) {
      position = '2';
    }
    else if (index === 2) {
      position = '3';
    }
    else if (index === 3) {
      position = '4';
    }
    else if (index === 4) {
      position = '5';
    } else {
      position = '6';
    }
    return position;
  }
  //Repetition - Remaining units
  calculateRemainingUnits() {
    this.listRemainingUnits = [];

    this._boostIdBlockservice.toFinishLoading();
    const boostID = this._boostIdBlockservice.models;
    this._healingtIdBlockservice.toFinishLoading();
    const healingID = this._healingtIdBlockservice.models;
    this._defensiveIdBlockservice.toFinishLoading();
    const defensiveID = this._defensiveIdBlockservice.models;
    this._negativeIdBlockservice.toFinishLoading();
    const negativeID = this._negativeIdBlockservice.models;
    this._ailmentIdBlockservice.toFinishLoading();
    const ailmentID = this._ailmentIdBlockservice.models;
    this._dispelIdBlockservice.toFinishLoading();
    const dispelID = this._dispelIdBlockservice.models;

    // CALCULA BOOST ID
    // Inicializa um array para acumular as somas das 6 posições
    const totals = Array(6).fill(0);
    boostID.forEach((boost) => {
      boost.positionNameBoosts.forEach((item, index) => {
        if (item !== "") {
          totals[index]++;
        }
      });
    });

    // Transforma os totais calculados no formato desejado
    this.listRemainingUnits = totals.map((amount, index) => ({
      position: index + 1, // Posições começam em 1 (não 0)
      amount,
    }));

    // CALCULA HEALING ID
    const totalsHealing = Array(6).fill(0);
    healingID.forEach((healing) => {
      healing.positionNameHealing.forEach((item, index) => {
        if (item !== "") {
          totalsHealing[index]++;
        }
      });
    });

    // Soma listRemainingUnits + Healing Id
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsHealing[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });


    // CALCULA DEFENSIVE ID
    const totalsDefensive = Array(6).fill(0);
    defensiveID.forEach((defensive) => {
      defensive.positionNameDefensive.forEach((item, index) => {
        if (item !== "") {
          totalsDefensive[index]++;
        }
      });
    });
    // Soma listRemainingUnits + Defensive Id
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsDefensive[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA NEGATIVE ID
    const totalsNegative = Array(6).fill(0);
    negativeID.forEach((negative) => {
      negative.positionNameNegative.forEach((item, index) => {
        if (item !== "") {
          totalsNegative[index]++;
        }
      });
    });

    // Soma listRemainingUnits + Negative Id
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsNegative[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA AILMENT ID
    const totalsAilment = Array(6).fill(0);
    ailmentID.forEach((ailment) => {
      ailment.positionNameAiment.forEach((item, index) => {
        if (item !== "") {
          totalsAilment[index]++;
        }
      });
    });

    // Soma listRemainingUnits + Ailment Id
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsAilment[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA DISPEL ID
    const totalsDispel = Array(6).fill(0);
    dispelID.forEach((dispel) => {
      dispel.positionNameDispel.forEach((item, index) => {
        if (item !== "") {
          totalsDispel[index]++;
        }
      });
    });
    // Soma Boost Id + Dispel Id
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsDispel[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });
  }


  calculateHybrid() {
    this._hybridTableService.toFinishLoading();
    this.hybridTable = this._hybridTableService.models;

    // Criar uma nova lista contendo apenas os itens que não foram usados
    const filteredHybrid = this.hybridTable.filter(hybridItem => {
      // Verificar se o item NÃO está presente em `listSpecialSkills`
      return !this._specialSkillsService.models.some(specialSkill =>
        specialSkill.listSpecialSkills.some(specialSkillItem =>
          specialSkillItem.idStatusEffect === hybridItem.idHybrid
        )
      );
    });

    this.listCalculatedHybrid = filteredHybrid.filter(hybridItem => hybridItem.idHybrid !== "").length;
  }

  calculatePhysical() {
    // Inicializar a variável
    this.listCalculatedPhysical = 0;

    this._boostTableService.models.forEach((x) => {
      if (x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    this._healingTableService.models.forEach((x) => {
      if (x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    this._defensiveTableService.models.forEach((x) => {
      if (x.skillWeakUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    this._negativeTableService.models.forEach((x) => {
      if (x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    this._ailmentTableService.models.forEach((x) => {
      if (x.statusEffectName.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    this._dispelTableService.models.forEach((x) => {
      if (x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    this._hybridTableService.models.forEach((x) => {
      if (x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });
  }

  reset(character) {
    this.character = character;
    this.listCategory = [];
    this.itemSelectedCategory = null;
    this.descriptionCatergory = null;
    this.ref.detectChanges();
    this.ngOnInit();
  }
  closeAreaStatsPopup() {
    this.isModalInfo = false;
  }
  //modal
  onModalClick(): void {
    this.isModalInfo = !this.isModalInfo;
  }

  selectCategoryItem(event: Event) {

    this.listStringElementalAffinities = [];
    this.indexCategory = null;

    const selectElement = event.target as HTMLSelectElement;
    this.indexCategory = selectElement.value;
    if (this.indexCategory === 'default') {
      this.descriptionCatergory = undefined;
      return;
    }
    this.itemSelectedCategory = this.listCategory[selectElement.value].category;
    this.descriptionCatergory = this.listCategory[selectElement.value].description;
    this.selectedCategory = this.listCategory[selectElement.value]
  
    // Pega dados do Elemental Affinities - Resist ou Weak
    if (this.listCategory[selectElement.value].relation === "Resist") {

      this._elementalAffinities.models.forEach(elemental => {
        if (elemental.character === this.character && elemental.ascensionOrder != null) {
          this.listStringElementalAffinities.push(elemental.status);
        }
      });
    }
    else {
      // Weak
      this._elementalAffinities.models.forEach(elemental => {
        if (elemental.character === this.character && elemental.weakeness != null) {
          this.listStringElementalAffinities.push(elemental.status);
        }
      });
    }

    this.listStringElementalAffinities.push('físico');

    // Verifica se a categoria está dentro do limite permitido
    if (!this.checkCategoryLimit()) {
      return;
    }
    else {
      this.getCatergoryItem(this.listCategory[selectElement.value].category);
      this.isStatusSelected = false;
    }
  }

  getCatergoryItem(category: string) {

    if (category === 'Boost') {
      this.getIdBlocksDescription();
    }
    else if (category === 'Healing') {
      this.getIdHealingDescription();
    }
    else if (category === 'Defensive') {
      this.getIdDefensiveDescription();
    }
    else if (category === 'Negative') {
      this.getIdNegativeDescription();
    }
    else if (category === 'Ailment') {
      this.getIdAilmentDescription();
    }
    else if (category === 'Dispel') {
      this.getIdDispelDescription();
    } else {
      this.getIdHybridDescription();
    }

  }

  getIdBlocksDescription() {
    this.listDescriptionStatusEffect = [];
    this.listIdBlocks = [];
    this.listIds = [];

    //Pega os Ids da tabela ID BLOCKS
    this._boostIdBlockservice.toFinishLoading();
    this._boostIdBlockservice.models.forEach(boost => {
      for (let index = 0; index < this.position.length; index++) {
        if (boost.positionNameBoosts[this.position[index]]) {
          this.listIdBlocks.push({
            id: boost.positionNameBoosts[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdBlocks;


    // Remove ids duplicados
    this.listIdBlocks = this.listIdBlocks.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );
    
    //Transforma as posições em números inteiros
    this.listIdBlocks.forEach(idBlock => {
      idBlock.position = this.getPositionRarity(idBlock.position);
    });

    this._boostTableService.toFinishLoading();
    this.boostTable = this._boostTableService.models;

    // Iterar pela lista de IDs de blocos //Pega a descrição da tabela Status Effect Table/ Boost Table
    this.listIdBlocks.forEach((idBlock) => {
      // Procurar por correspondência no array boostTable
      this.boostTable.forEach((boost) => {
        if (boost.idBoost === idBlock.id) {
          // Transformar ambas as strings para letras minúsculas antes de comparar
          const skillResistUserLowerCase = boost.skillResistUser.toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            // Adicionar os campos no array listDescriptionStatusEffect
            this.listDescriptionStatusEffect.push({
              id: boost.idBoost,
              name: boost.statusEffectName,
              description: boost.description,
              positionID: idBlock.position,
              skillUser: boost.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdHealingDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdHealing = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Healing
    this._healingtIdBlockservice.toFinishLoading();
    this._healingtIdBlockservice.models.forEach(healing => {
      for (let index = 0; index < this.position.length; index++) {
        if (healing.positionNameHealing[this.position[index]]) {
          this.listIdHealing.push({
            id: healing.positionNameHealing[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });

    this.listIds = this.listIdHealing;

    // Remove ids duplicados
    this.listIdHealing = this.listIdHealing.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    //Transforma as posições em números inteiros
    this.listIdHealing.forEach(idHealing => {
      idHealing.position = this.getPositionRarity(idHealing.position);
    });

    this._healingTableService.toFinishLoading();
    this.healingTable = this._healingTableService.models;

    // Iterar pela lista de IDs de blocos //Pega a descrição da tabela Status Effect Table/ Healing Table
    this.listIdHealing.forEach((idHealing) => {
      // Procurar por correspondência no array healingTable   
      this.healingTable.forEach((healing) => {
        if (healing.idHealing === idHealing.id) {
          // Transformar ambas as strings para letras minúsculas antes de comparar
          const skillResistUserLowerCase = healing.skillResistUser.toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            // Adicionar os campos no array listDescriptionStatusEffect
            this.listDescriptionStatusEffect.push({
              id: healing.idHealing,
              name: healing.statusEffectName,
              description: healing.description,
              positionID: idHealing.position,
              skillUser: healing.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdDefensiveDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdDefensive = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Defensive
    this._defensiveIdBlockservice.toFinishLoading();
    this._defensiveIdBlockservice.models.forEach(defensive => {
      for (let index = 0; index < this.position.length; index++) {
        if (defensive.positionNameDefensive[this.position[index]]) {
          this.listIdDefensive.push({
            id: defensive.positionNameDefensive[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdDefensive;

    this.listIdDefensive = this.listIdDefensive.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdDefensive.forEach(idDefensive => {
      idDefensive.position = this.getPositionRarity(idDefensive.position);
    });

    this._defensiveTableService.toFinishLoading();
    this.defensiveTable = this._defensiveTableService.models;

    this.listIdDefensive.forEach((idDefensive) => {

      this.defensiveTable.forEach((defensive) => {
        if (defensive.idDefensive === idDefensive.id) {
          const skillResistUserLowerCase = defensive.skillWeakUser.toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            this.listDescriptionStatusEffect.push({
              id: defensive.idDefensive,
              name: defensive.statusEffectName,
              description: defensive.description,
              positionID: idDefensive.position,
              skillUser: defensive.skillWeakUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdNegativeDescription() {
    this.listDescriptionStatusEffect = [];
    this.listIdNegative = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Defensive
    this._negativeIdBlockservice.toFinishLoading();
    this._negativeIdBlockservice.models.forEach(defensive => {
      for (let index = 0; index < this.position.length; index++) {
        if (defensive.positionNameNegative[this.position[index]]) {
          this.listIdNegative.push({
            id: defensive.positionNameNegative[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });

    this.listIds = this.listIdNegative;

    //Remove elementos duplicados
    this.listIdNegative = this.listIdNegative.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdNegative.forEach(idNegative => {
      idNegative.position = this.getPositionRarity(idNegative.position);
    });

    this._negativeTableService.toFinishLoading();
    this.negativeTable = this._negativeTableService.models;

    this.listIdNegative.forEach((idNegative) => {

      this.negativeTable.forEach((negative) => {
        if (negative.idNegative === idNegative.id) {
          const skillResistUserLowerCase = negative.skillResistUser.toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            this.listDescriptionStatusEffect.push({
              id: negative.idNegative,
              name: negative.statusEffectName,
              description: negative.description,
              positionID: idNegative.position,
              skillUser: negative.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdAilmentDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdAilment = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Ailemnt
    this._ailmentIdBlockservice.toFinishLoading();
    this._ailmentIdBlockservice.models.forEach(ailment => {
      for (let index = 0; index < this.position.length; index++) {
        if (ailment.positionNameAiment[this.position[index]]) {
          this.listIdAilment.push({
            id: ailment.positionNameAiment[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdAilment;
    this.listIdAilment = this.listIdAilment.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdAilment.forEach(idAilment => {
      idAilment.position = this.getPositionRarity(idAilment.position);
    });

    this._ailmentTableService.toFinishLoading();
    this.ailmentTable = this._ailmentTableService.models;

    this.listIdAilment.forEach((idAilment) => {

      this.ailmentTable.forEach((ailment) => {
        if (ailment.idAilment === idAilment.id) {
          this.listDescriptionStatusEffect.push({
            id: ailment.idAilment,
            name: ailment.statusEffectName,
            description: ailment.description,
            positionID: idAilment.position,
            skillUser: ailment.statusEffectName
          });
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdDispelDescription() {
    this.listDescriptionStatusEffect = [];
    this.listIdDispel = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Dispel
    this._dispelIdBlockservice.toFinishLoading();
    this._dispelIdBlockservice.models.forEach(dispel => {
      for (let index = 0; index < this.position.length; index++) {
        if (dispel.positionNameDispel[this.position[index]]) {
          this.listIdDispel.push({
            id: dispel.positionNameDispel[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });

    this.listIds = this.listIdDispel;

    this.listIdDispel = this.listIdDispel.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdDispel.forEach(idDispel => {
      idDispel.position = this.getPositionRarity(idDispel.position);
    });

    this._dispelTableService.toFinishLoading();
    this.dispelTable = this._dispelTableService.models;

    this.listIdDispel.forEach((idDispel) => {

      this.dispelTable.forEach((dispel) => {
        if (dispel.idDispel === idDispel.id) {
          const skillResistUserLowerCase = dispel.skillResistUser.toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            this.listDescriptionStatusEffect.push({
              id: dispel.idDispel,
              name: dispel.statusEffectName,
              description: dispel.description,
              positionID: idDispel.position,
              skillUser: dispel.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdHybridDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdHybrid = [];

    this.hybridTable.forEach((hybrid) => {
      const skillResistUserLowerCase = hybrid.skillResistUser.toLowerCase();
      const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

      if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
        this.listDescriptionStatusEffect.push({
          id: hybrid.idHybrid,
          name: hybrid.statusEffectName,
          description: hybrid.description,
          positionID: 1,
          skillUser: hybrid.skillResistUser,
          idBoost: hybrid.idBoost,
          idNegative: hybrid.idNegative
        });
      }
    });

    this.removeStatusSelected();
  }

  removeStatusSelected() {

    if (this.selectedCategory.typology !== "Hybrid") {
      this.listSpecialSkills.listSpecialSkills.forEach((specialSkill) => {
        this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((statusEffect) => {
          return specialSkill.idStatusEffect !== statusEffect.id;
        });
      });

      // Remove os itens que já foram usados de acordo a regra de repetição	
      this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((statusEffect) => {
        const qtdIds = this.listIds.filter(id => id.id === statusEffect.id).length;

        let usesSkill = 0;

        this._specialSkillsService.models.forEach((model) => {
          if (model.listSpecialSkills.length > 0) {
            usesSkill += model.listSpecialSkills.filter(skill => skill.idStatusEffect === statusEffect.id).length;
          }
        });

        return (qtdIds - usesSkill) !== 0;
      });

      //Se o Special Skill selecionado for do tipo Hibrid deve remover da selação no Status Effect o id do Boost ou Negative
      //para o personagem atual  
      if (this.selectedCategory.category === "Boost" || this.selectedCategory.category === "Negative") {
        this.hybridTable.forEach((hybrid) => {
          this.listSpecialSkills.listSpecialSkills.forEach((specialSkill) => {
            if (specialSkill.idStatusEffect === hybrid.idHybrid) {
              this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((list) =>
                !(list.id === hybrid.idBoost || list.id === hybrid.idNegative)
              )
            }
          });
        });
      }


    }
    else {
      //Se o Special Skill selecionado for do tipo Boost ou Negative deve remover da selação no Status Effect o id do Hibrid
      //para o personagem atual
      this._specialSkillsService.models.forEach((model) => {
        model.listSpecialSkills.forEach((specialSkill) => {
          this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter(
            (x) => !(x.id === specialSkill.idStatusEffect ||
              x.idBoost === specialSkill.idStatusEffect ||
              x.idNegative === specialSkill.idStatusEffect)
          );
        });
      });

    }

  }
  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  // Fecha o dropdown ao clicar fora
  @HostListener('document:click', ['$event'])
  closeDropdown(event: Event) {
    this.dropdownOpen = false; // Fecha sempre que houver clique fora
  }

  // Evita fechar ao clicar dentro do dropdown
  stopPropagation(event: Event) {
    event.stopPropagation();
  }

  async selectStatusEffect(index: number) {
    // Obtem o status selecionado
    this.selectedStatus = this.listDescriptionStatusEffect[index];

    if (this.listSpecialSkills.listSpecialSkills.length < 5) {

      this.listSpecialSkills.listSpecialSkills.push({
        category: this.itemSelectedCategory,
        idCategory: this.selectedCategory.id,
        indexStatusEffect: index,
        relationCategory: this.selectedCategory.relation,
        idStatusEffect: this.selectedStatus.id,
        nameStatusEffect: this.selectedStatus.name,
        descriptionStatusEffect: this.selectedStatus.description,
        positionIDStatusEffect: this.selectedStatus.positionID,
        positionCategory: this.indexCategory + 1,
        skillUserStatusEffect: this.selectedStatus.skillUser
      });

      //Remove da listDescriptionStatusEffect
      this.removeSkillList(index);

      await this._specialSkillsService.svcToModify(this.listSpecialSkills);
      await this._specialSkillsService.toSave();
      this.ref.detectChanges();
  
      this.dropdownOpen = false;

    }
    else {
      console.warn('Lista de skills cheia. Não é possível adicionar mais itens.');
      this.isStatusSelected = true;
      this.IsMaxCatgeory = true;
      return; // Não realiza mais nenhuma ação
    }
    this.ngOnInit();
  }

  getTotalPosition(status: any): number | string {
    // Quantidade de vezes que o ID aparece em `this.listIds`
    const total = this.listIds.filter(id => id.id === status.id).length;
    return total;
  }

  getAmountPosition(status: any): number | string {

    const qtdIds = this.listIds.filter(id => id.id === status.id).length;

    // Se o `listSpecialSkills` contiver dados, calcular quantas vezes o `status.id` foi usado
    let usesSkill = 0;

    this._specialSkillsService.models.forEach((model) => {
      if (model.listSpecialSkills.length > 0) {
        usesSkill += model.listSpecialSkills.filter(skill => skill.idStatusEffect === status.id).length;
      }
    });

    // Se `usesSkill` for maior que 0, retornar a diferença, senão, retornar `status.positionID`
    return usesSkill > 0 ? qtdIds - usesSkill : status.positionID;
  }


  checkCategoryLimit(): boolean {
    // Verifica se a lista de skills está definida
    if (!this.listSpecialSkills || !this.listSpecialSkills.listSpecialSkills) {
      console.error('Lista de skills não encontrada.');
      return false;
    }

    // Conta quantas vezes a string itemSelectedCategory aparece no campo category
    const idCategoryCount = this.listSpecialSkills.listSpecialSkills.filter((skill) => skill.idCategory === this.selectedCategory?.id).length;

    // Verifica se a categoria excede o limite de 2
    if (idCategoryCount >= 2) {
      console.error(`A categoria '${this.itemSelectedCategory}' já foi adicionada mais de 2 vezes.`);

      this.isPulsing = true;
      setTimeout(() => {
        this.isPulsing = false;
      }, 400);

      this.isStatusSelected = true;
      this.ref.detectChanges();
      return false;
    }
    // Se estiver dentro do limite, retorna true
    return true;
  }

  removeSkillList(index: number) {
    this.listDescriptionStatusEffect.splice(index, 1);
    this.ref.detectChanges();
  }

  returnSkillList(index: number) {
    this.listSpecialSkills.listSpecialSkills.splice(index, 1);
    this._specialSkillsService.svcToModify(this.listSpecialSkills);
    this._specialSkillsService.toSave();

    this.isPulsing = true;
    setTimeout(() => {
      this.isPulsing = false;
    }, 400);

    this.listCategory = [];
    this.ref.detectChanges();
    this.ngOnInit();
  }

  getCategoryCount(category: string, index: number): number {
    const occurrences = this.listSpecialSkills.listSpecialSkills
      .slice(0, index + 1) // Considera apenas os elementos até o índice atual
      .filter(item => item.category === category).length; // Filtra os itens com a mesma categoria
    return occurrences;
  }

  public onBack() {
    this._router.navigate(['others']);
  }

  sortSpecialSkillsByCategory(): void {
    this.listSpecialSkills.listSpecialSkills.sort((a, b) => {
      const categoryA = a.category.toLowerCase();
      const categoryB = b.category.toLowerCase();
      if (categoryA < categoryB) {
        return -1;
      }
      if (categoryA > categoryB) {
        return 1;
      }
      return 0;
    });
  }

}