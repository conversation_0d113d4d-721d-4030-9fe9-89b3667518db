.cardContent {
    padding-top: 10px;
    display: flex;
    justify-content: center;
}

.filter-dropdown.limited {
  max-width: 650px;
}

/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}

.selectedCategory {
  display: inline-block; 
  margin: 10px; 
  margin-bottom: 15px; 
  width: 300px !important; 
  cursor: pointer;
}

.container {
    margin-top: 30px;
    width: 100%;
    display: flex;
   // justify-content: center;
}
  
.content .skill {
    flex: 3;
  }

  .content {
    width: 25%; 
    margin-left: 10px;
    flex: 1;
  }

h3, .h3 {
    font-size: 28px;
    margin: 10px 0 10px !important;
}

h4 {
    margin: 0 !important;
}

.titlecard{
    width: 135px;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    border-radius: 5px;
}

.titleCategory{
    display: flex; 
    justify-content: center; 
    font-weight: 600; 
    width: 200px; 
    padding-left: 100px;
}

.titleEffect {
    font-weight: 600; 
    padding-left: 10px;
    padding-right: 15px;
    display: flex;
    justify-content: space-between;
}

.listRepetition {
  margin-left: 13px;
  display: flex;
  justify-content: space-between;
  width: 555px;
  margin-top: 14px;
}


#customers {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

#customers td, #customers th {
  border: 1px solid #ddd;
  padding: 8px;
}

#customers tr:nth-child(even){background-color: #f2f2f2;}

#customers tr:hover {background-color: #ddd;}

#customers th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04AA6D;
  color: white;
}


  /* Wrapper do dropdown */
  .dropdown-toggle {
    width: 100%; 
    font-size: 14px;
    text-align: left;
    border: 1px solid #ccc;
    border-radius: .25rem;
    background-color: #fff;
    cursor: pointer;
    height: 4vh;
    display: flex;
    align-items: center;
    margin-top: 10px;
  }

  .dropdown-wrapper {
    position: relative;
    overflow: visible; /* Garante que o dropdown possa ser exibido */
    z-index: 10; /* Torna o dropdown acessível em camadas */
}

.dropdown-menu {
    position: absolute;
    top: 90%; /* Exibe o menu logo abaixo do botão */
    left: 0;
    width: 100%; /* Largura alinhada ao botão */
    z-index: 100; /* Torna o menu visível acima de outros elementos */
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 0px;
    list-style: none;
    padding: 0;
    display: block;
    max-height: 300px;
    overflow-y: auto;
}

.dropdown-menu {
  visibility: visible !important;
  margin: 0;
  padding: 0;
  display: block;
  z-index: 9000;
  position: absolute;
  opacity: 100;
  filter: alpha(opacity = 0);
  box-shadow: 1px 2px 3px #00000020;
}

.dropdown-menu li {
  display: flex;
  align-items: center;
  padding: 5px 5px;
  cursor: pointer;
  justify-content: space-between;
}

.dropdown-menu li:hover {
  background-color: #0070c0;
  color: white;
  .circle {
    background-color: white;
  }
}

.isDisabled-Status {
  border: 1px solid #ced4da;
  color: #aaaaaa;
  line-height: 1.5;
  cursor: default;
}
.circle {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none !important;
  background-color: #d9d9d9;
  color: #000;
  font-size: 14px;
  font-weight: bold;
}

.circle:hover {
  background-color: white;
}

.spanTitle {
  padding-top: 7px;
}

.fontWeight {
  font-weight: bold;
}

.repRemain {
  display: flex;
  justify-content: space-between;
  padding-left: 15px;
  padding-right: 15px;
}

.remaing {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: none !important;
  background-color: #d9d9d9;
  color: #000;
  font-size: 15px;
  font-weight: bold;
}

/*Modal do sistema */
.iconInter {
  text-align: center; 
  font-size: 20px !important; 
  margin-top: 1px;
  margin-left: 3px;
}
i:hover{
  color: red;
  cursor: pointer;
}

.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: rgb(230, 230, 230);
    z-index: 150;
}

.popup-report
{ 
  border-radius: 8px;
  width: 850px;
  position: fixed;
 // left: 35%;
  padding: 24px;
  top: 23%;
  transform: translate(-20%, -15%);
  z-index: 1000;
  opacity: 1;  

}
.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    font-weight: 600 !important;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: white !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextInfo {
  color: white;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: white black;
  padding-top: 10px;
}

.p-text {
  color:azure !important; 
  text-align: left; 
  text-transform: none !important;
}

.span-text {
  font-weight: 700; 
  color: white;
}

.background-div {	
  position: relative;
  display: flex;
  justify-content: center;
  z-index: 9999;
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	

// FIM DO MODAL

.pulse-red {
  border: 1px solid #ff0000;
  animation: pulse 0.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
