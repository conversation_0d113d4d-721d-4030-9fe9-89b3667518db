import { Component, EventEmitter, Output } from '@angular/core';
import { CtrEvMax } from 'src/app/lib/@bus-tier/models';
import { CTREVMAXService } from 'src/app/services';

@Component({
  selector: 'app-ctr-ev-max',
  templateUrl: './ctr-ev-max.component.html',
  styleUrls: ['./ctr-ev-max.component.scss']
})
export class CtrEvMaxComponent {

   @Output() clickBtnMax = new EventEmitter<boolean>();
   cardTitle: string;
    listValueCtrEv_Max: CtrEvMax;
    nameMax = ['CTR-MAX', 'EV-MAX'];
    

    constructor(
      private _cTREVMAXService: CTREVMAXService,
    ) { }
  
 async ngOnInit(): Promise<void> { 

  setTimeout(async () => {
    this._cTREVMAXService.toFinishLoading();
    this.cardTitle = 'CTR-MAX and EV-MAX';
   
   this.listValueCtrEv_Max = this._cTREVMAXService.models[0];
    if (this.listValueCtrEv_Max === undefined) {
      this.listValueCtrEv_Max = await this._cTREVMAXService.createNewCTREVMAX();
    } 
   }, 200);      
  
 }   

    addValue(name: string, value: string){
      if (name == 'CTR-MAX') {
        this.listValueCtrEv_Max.ctr_max = +value;
      } else if (name == 'EV-MAX') {
        this.listValueCtrEv_Max.ev_max = +value;
      }  
      this._cTREVMAXService.models[0] = this.listValueCtrEv_Max
      this._cTREVMAXService.svcToModify(this._cTREVMAXService.models[0]);
      this._cTREVMAXService.toSave();
    } 

    btnClickContext() {
      this.clickBtnMax.emit(true);
    }  

}
