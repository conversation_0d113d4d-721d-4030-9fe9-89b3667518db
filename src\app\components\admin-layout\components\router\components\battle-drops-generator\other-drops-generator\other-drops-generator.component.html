<div class="main-content" style="margin-top: 15px;">

    <div *ngIf="activeTab === 'otherDrops'" class="card list-header"
      style="height: 70px; margin-bottom: 0px; display: flex; justify-content: space-between; align-items: center; padding-left: 20px; padding-right: 20px;">
      <div>
        <button (click)="goToParticleDrop('particleDrop')"
          class="{{activeTab3 === 'particleDrop' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
          Particle Drop
        </button>
        <button (click)="goToParticleDrop('ingredientDrop')"
          class="{{activeTab3 === 'ingredientDrop' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
          Ingredient Drop
        </button>
      </div>
      <div>
        <button (click)="goToParticleDrop('areaDrop', '')"
          class="{{activeTab3 === 'areaDrop' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
          Area Drops
        </button>
        <button (click)="goToParticleDrop('varianceA', 'A')"
          class="{{activeTab3 === 'varianceA' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
          Area Drops (Variance A)
        </button>
        <button (click)="goToParticleDrop('varianceB', 'B')"
          class="{{activeTab3 === 'varianceB' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
          Area Drops (Variance B)
        </button>
      </div>
    </div>
  </div>

  <app-particle-drop *ngIf="activeTab3 == 'particleDrop'"></app-particle-drop>
  <app-ingredient-drop *ngIf="activeTab3 == 'ingredientDrop'"></app-ingredient-drop>

  <!--Link dos botões-->
  <app-particle-variance (activeTab2)="receiveActiveTab($event)" [ingredientType]="ingredientType"
    *ngIf="(activeTab3 == 'areaDrop' || activeTab3 == 'varianceA' || activeTab3 == 'varianceB') && activeTab2 == 'Particles'">
  </app-particle-variance>

  <app-ingredient-variance (activeTab2)="receiveActiveTab($event)" [ingredientType]="ingredientType"
    *ngIf="(activeTab3 == 'areaDrop' || activeTab3 == 'varianceA' || activeTab3 == 'varianceB') && activeTab2 == 'Ingredients'">
  </app-ingredient-variance>

