<div class="card list-header" style="margin: 15px 29px !important; height: 140px !important;">
        <div class="header" style="display: flex; flex-direction: row; gap: 10px 2px; flex-wrap: wrap;">
                <button class="{{activeTab === 'keywords' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('keywords')">
                        Keywords
                </button>
                <button class="{{activeTab === 'tutorial' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('tutorial')">
                        Tutorial
                </button>
                <button class="{{activeTab === 'minigames' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('minigames')">
                        Minigames
                </button>
                <button class="{{activeTab === 'videos' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('videos')">
                        Videos
                </button>
                <!--
                <button class="{{activeTab === 'passives' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('passives')">
                        Passives
                </button>
                -->

                <button class="{{activeTab === 'passiveAtributes' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('passiveAtributes')">
                        Passive Atributes
                </button>
                <button style="margin-right: 20px;"
                        class="{{activeTab === 'effects' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('effects')">
                        Effects
                </button>
                <button class="{{activeTab === 'charrarity' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('charrarity')">
                        Character Rarity
                </button>
                <button class="{{activeTab === 'collectableCosts' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('collectableCosts')">
                        Collectible Costs
                </button>
                <!-- 
                <button style="margin-right: 20px;" class="{{activeTab === 'minionStats' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('minionStats')">
                        Minion Stats
                </button>            
                -->

                <button class="{{activeTab === 'skills' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('skills')">
                        Skills
                </button>
                <button class="{{activeTab === 'modifier' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('modifier')">
                        Modifier
                </button>
                <button class="{{activeTab === 'chest' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('chest')">
                        Chest
                </button>
                <button class="{{activeTab === 'tierList' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('tierList')">
                        Tier List
                </button>
                <button class="{{activeTab === 'atributte' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('atributte')">
                        Atributte
                </button>
                <button class="{{activeTab === 'knowledge' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('knowledge')">
                        Knowledge
                 </button>
                <button class="{{activeTab === 'dice-Frustration' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('dice-Frustration')">
                        Dice Frustration
                </button>
                <button class="{{activeTab === 'ElementalDefenses' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('ElementalDefenses')">
                        Elemental Defenses
                </button>
                <button style="margin-right: 20px;"
                        class="{{activeTab === 'ailment' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('ailment')">
                        Ailment
                </button>
                <button class="{{activeTab === 'mastery' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('mastery')">
                        Mastery
                </button>
                <button class="{{activeTab === 'silicatos' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('silicatos')">
                        Silicatos
                </button>
                <button class="{{activeTab === 'maps' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('maps')">
                        Maps
                </button>
                <button class="{{activeTab === 'storyExpansionPack' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('storyExpansionPack')">
                        Story Expansion Pack
                </button>
                <button class="{{activeTab === 'gacha' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('gacha')">
                        Gacha
                </button>
                <button class="{{activeTab === 'lootBox' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('lootBox')">
                        LootBox
                </button>
                <button class="{{activeTab === 'statusEffect' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('statusEffect')">
                        Status Effect
                </button>
                <button style="margin-left: 20px;"
                        class="{{activeTab === 'mahankara' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                        (click)="switchToTab('mahankara')">
                        Mahankara Dialogs
                </button>
        </div>
</div>

<app-keywords-list *ngIf="activeTab === 'keywords'"> </app-keywords-list>
<app-tutorial-list *ngIf="activeTab === 'tutorial'"> </app-tutorial-list>
<app-minigame *ngIf="activeTab === 'minigames'"> </app-minigame>
<app-video-list *ngIf="activeTab === 'videos'"> </app-video-list>
<app-passive-generator *ngIf="activeTab === 'passives'"> </app-passive-generator>
<app-passive-atributes *ngIf="activeTab === 'passiveAtributes'"></app-passive-atributes>
<app-effect-generator *ngIf="activeTab === 'effects'"> </app-effect-generator>
<app-collectible-rarity *ngIf="activeTab === 'charrarity'"> </app-collectible-rarity>
<app-collectible-costs *ngIf="activeTab === 'collectableCosts'"> </app-collectible-costs>
<app-minion-stats *ngIf="activeTab === 'minionStats'"> </app-minion-stats>
<app-status-list *ngIf="activeTab === 'skills'"> </app-status-list>
<app-modifier-list *ngIf="activeTab === 'modifier'"> </app-modifier-list>
<app-mastery *ngIf="activeTab === 'mastery'"> </app-mastery>
<app-silicatos *ngIf="activeTab === 'silicatos'"></app-silicatos>
<app-maps *ngIf="activeTab === 'maps'"></app-maps>
<app-chest-list *ngIf="activeTab === 'chest'"></app-chest-list>
<app-status-effect *ngIf="activeTab === 'statusEffect'"></app-status-effect>
<app-tier-list *ngIf="activeTab === 'tierList'"></app-tier-list>
<app-atributte *ngIf="activeTab === 'atributte'"></app-atributte>
<app-knowledge *ngIf="activeTab === 'knowledge'"></app-knowledge>
<app-failure-levels *ngIf="activeTab === 'dice-Frustration'"></app-failure-levels>
<app-elemental-defenses *ngIf="activeTab === 'ElementalDefenses'"></app-elemental-defenses>
<app-ailment *ngIf="activeTab === 'ailment'"></app-ailment>
<app-story-expansion-pack *ngIf="activeTab === 'storyExpansionPack'"></app-story-expansion-pack>
<app-mahankara-dialogs *ngIf="activeTab === 'mahankara'"></app-mahankara-dialogs>