<div class="col">
  <div class="card bc-color" id="{{ answerDilBox?.id }}">
    <div *ngFor="let roadblock of this.roadBlocksUseds">
      <div id="{{roadblock.id}}">
        <app-roadblock [optionBox]="answerDilBox"
        *ngIf="answerDilBox?.id.includes(roadblock?.StoryBoxId) && roadblock?.StoryBoxId != optionBox.id"
        [toRemove]="toRemoveProcessConditionFunc" [currentRoadblock]="roadblock" [storyBoxId]="answerDilBox.id"
        class="center">
      </app-roadblock>
      </div>
    </div>

    <div *ngIf="this.countingRoadblocks > 1" class="componente_AndOr">
      <div style="font-weight: 900; font-size: 20px" class="color-grey-light"
        [ngStyle]="{'opacity': answerDilBox.AndOrCondition == 'AND' ? 1 : 0.3, 'color':answerDilBox.AndOrCondition == 'AND' ? '#ff00aa' : 'black'}">
        AND</div>
      <label class="switch">
        <input [checked]="answerDilBox.AndOrCondition == 'AND' ? false : true"
          (change)="this.chooseAndOrCondition($event)" type="checkbox">
        <span class="slider"></span>
      </label>
      <div style="font-weight: 900; font-size: 20px;" class="color-grey-light"
        [ngStyle]="{'opacity': answerDilBox.AndOrCondition == 'OR' ? 1 : 0.3, 'color': answerDilBox.AndOrCondition == 'OR' ? 'blue' : 'black'}">
        OR</div>
    </div>

    <div class="table-responsive" style="height: 100%">
      <div style="padding: 15px 15px 0; display: flex; justify-content: space-between;">
        <p class="category color-grey-light">
          Dilemma Box > {{ answerDilBox?.id }}
        </p>
      </div>

      <div>
        <input class="form-control form-short form-title color-grey-light component_label" style="font-size: 18px;" type="text"
          value="{{ !answerDilBox?.label ? '<<Label for progress condition>>' : answerDilBox?.label }}" #label
          (change)="changeLabel($event, answerDilBox)" />

        <div *ngIf="answerDilBox?.label" style="width: 100px; float: right;">
          <i [ngStyle]="{'color': isUsedRoadblock(answerDilBox) ? '#00ff04' : '#d3d3d3' }"
            class="pe-7s-key pe-5x pe-va key-icon">
            <div id="text-size" *ngIf="isUsedRoadblock(answerDilBox)"
              [ngStyle]="{'margin-right': isUsedRoad ? '-33px' : '' }" class="circle branch-circle margIcon"
              tooltip="Used on: {{this.usedOnLevels | enumerateList}}" placement='top' delay='250' ttPadding="10px"
              ttWidth="{{this.usedOnLevels | textSize}}">
              <p style="text-align: center;" class="color-grey-light">{{this.usedRoadBlocks?.length}}</p>
            </div>
          </i>
        </div>
      </div>

      <br />
      <i class="icon title pe-7s-play color-grey-light"></i>
      <div [innerHTML]="option?.message | rpgFormatting : true"
        class="form-control form-short form-title no-white color-grey-light">
      </div>
      <table class="table table-hover table-box table table-responsive speech-table">
        <tbody>
          <tr>
            <td>
              <span class="nu-icon pe-7s-info" data-tooltip="Line represents character introduction"></span>
            </td>
          </tr>
          <ng-container *ngFor="let storyProgressId of answerDilBox?.storyProgressIds let i = index;">
            <ng-container [ngSwitch]="(storyProgressId | storyProgress) | typeName">

              <ng-container *ngSwitchCase="'Answer Dilemma Box'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-speech [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [speechId]="storyProgressId" [index]="i"
                  [preloadedSpeakers]="preloadedSpeakers" [language]="language">
                </tr>
              </ng-container>

              <ng-container *ngSwitchCase="'Speech'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-speech [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [speechId]="storyProgressId" [index]="i"
                  [preloadedSpeakers]="preloadedSpeakers" [language]="language">
                </tr>
              </ng-container>

              <ng-container *ngSwitchCase="'Event'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-event
                  [preloadedMissionsOfArea]="preloadedMissionsOfArea" [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [eventId]="storyProgressId" [index]="i">
                </tr>
              </ng-container>
              <ng-container *ngSwitchCase="'Marker'">
                <tr class="storybox-row" id="{{ storyProgressId }}" app-marker [toMove]="toMoveStoryProgressFunc"
                  [toRemove]="toRemoveStoryProgressFunc" [markerId]="storyProgressId" [index]="i">
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
      <div class="row">
        <div class="btn-group">
          <button id="add-event-button" class="btn btn-fill btn-sm btn-mi" (click)="toAddMissionEvent()">
            <i class="pe-7s-camera center"></i>
            Add Mission Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-warning btn-fill btn-item" (click)="toAddItemEvent()">
            <i class="pe-7s-camera center"></i>
            Add Item Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-warning btn-fill btn-boss" (click)="toAddBossEvent()">
            <i class="pe-7s-camera center"></i>
            Add Boss Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-fill btn-cinematic" (click)="toAddCinematicEvent()">
            <i class="pe-7s-camera center"></i>
            Add Cinematic Event
          </button>
          <button id="add-event-button" class="btn btn-sm btn-fill btn-loop" (click)="toAddLoopEvent()">
            <i class="pe-7s-camera center"></i>
            Add Loop Event
          </button>
        </div>
        <div class="pull-right">
          <button id="add-message-button" class="btn btn-sm btn-success btn-fill" (click)="toAddRoadblock()">
            <i class="pe-7s-shield center"></i>
            Add Progress Condition
          </button>
          <button id="add-message-button" class="btn btn-sm btn-primary btn-fill" (click)="toAddMarker()">
            <i class="pe-7s-ribbon center"></i>
            Add Marker
          </button>
          <button id="add-marker-button" class="btn btn-sm btn-success btn-fill" (click)="toAddSpeech()">
            <i class="pe-7s-comment center"></i>
            Add Speech
          </button>
        </div>
      </div>
    </div>
  </div>
</div>