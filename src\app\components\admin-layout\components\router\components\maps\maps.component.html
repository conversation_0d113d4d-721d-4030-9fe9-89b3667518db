<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="description"
          [rightButtonTemplates]="[addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)">
        </app-header-search>
      </div>
    </div>
    <!--List-->

    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" (click)="sortListById()">ID</th>
            <th class="th-clickable" (click)="sortListByArea()">Area</th>
            <th class="th-clickable" (click)="sortListByAlphabeticalOrder('name')">Name
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByAlphabeticalOrder('description')">Description
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByAlphabeticalOrder('note')">Note</th>
            <th class="th-clickable" (click)="sortListByName(keywordsToSearch, 'classification')">Classification</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let map of keywordsToSearch ">
            <tr id="{{ map.id }}">
              <td>{{ map.id }}</td>
              <td>
                <select #a (change)="lstOnChange(map, 'area', a.value)">
                  <option *ngFor="let area of areasList" [selected]="area.hierarchyCode +' : ' +area.name === map.area">
                    {{area.hierarchyCode +' : ' +area.name}}</option>
                </select>
              </td>
              <td>
                <input placeholder=" " class="form-control form-short background-input-table-color" type="text" #name
                  value="{{ (map | translation : lstLanguage : map.id : 'name')}}"
                  (change)="nameChange(map, 'name', name.value)" />
              </td>
              <td>
                <textarea placeholder=" " style="width:100%;" class="background-input-table-color" type="text"
                  #description value="{{ (map | translation : lstLanguage : map.id : 'description')}}"
                  (change)="descriptionChange(map, 'description', description.value)"></textarea>
              </td>
              <td>
                <textarea placeholder=" " style="width:100%" class="background-input-table-color" type="text" #note
                  value="{{ (map | translation : lstLanguage : map.id : 'note') }}"
                  (change)="lstOnChange(map, 'note', note.value)"></textarea>
              </td>

              <!-- CLASSIFICATION SECTION -->
              <td class="td-classification">
                <button *ngIf="map.classification === 'image'" class="btn btn-green  btn-fill translation-button"
                  style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                  ttPadding="10px"
                  tooltip="O mapa apresenta ícones que representam locais, objetos ou elementos do jogo. Ao interagir com esses ícones, os jogadores recebem informações textuais relevantes, como nome [NAME] e descrições ou dicas sobre esses elementos [DESCRIPTION]."
                  placement='top' delay='250' ttWidth="250px" (click)="lstOnChange(map, 'classification', 'text')">
                  <i class="pe-7s-photo pe-4x pe-va"></i>
                </button>
                <button *ngIf="map.classification === 'text'" class="btn btn-blue btn-fill translation-button"
                  style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                  ttPadding="10px"
                  tooltip="O mapa mostra apenas informações em formato de texto, sem ícones ou elementos gráficos associados. Ao interagir com o texto [NAME], os jogadores recebem informações textuais relevantes como, descrições ou dicas sobre esses elementos [DESCRIPTION]."
                  placement='top' delay='250' ttWidth="250px" (click)="lstOnChange(map, 'classification', 'image')">
                  <i class="pe-7s-comment pe-4x pe-va"></i>
                </button>
              </td>

              <!-- ACTION SECTION -->
              <td class="td-actions">
                <!--  <p (click)="lstOnChange(map, 'classification', 'image')"><i class="pe-7s-comment pe-4x pe-va"></i></p>   -->
                <button class="btn btn-danger btn-fill btn-remove" (click)="removeElement(map)">
                  <i class="pe-7s-close pe-4x pe-va"></i>

                </button>

                <br>
                <button class="btn btn-gray btn-fill translation-button" (click)="downloadMapsOrtography(map)">

                  <div class="mat-translate"></div>
                </button>
              </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>