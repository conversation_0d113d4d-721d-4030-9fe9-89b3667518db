.key-icon
{
  font-weight: bold !important;
  font-size: 150% !important;
   float: right !important; 
   margin-right: 70px !important;
   margin-bottom: 120px !important;
  display: flex !important;
  align-items:flex-end !important;
   align-content:flex-end !important;

}
.p_endInvest {
  height: 39px;
}

.bc-color {
  background-color: #1E1E1E !important;
}
.categoryText {
  font-size: 14px;
  font-weight: 400;
  color: #D3D3D3;
  margin-bottom: 0;
}

/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}


.form-title:hover {
  color: black !important;
}
.color-grey-light {
  color: #D3D3D3;
}
.color-white {
  color: white !important;
}
//Modal

.background-div {	
  position: relative !important;	
}	
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: white;
    z-index: 150;
}

.popup-report
{
    position: fixed;
    height: 70%;
    background-color: white;
    transform: translate(-0%, 20%);
    z-index: 9999;
    top: 7%;
    margin: auto;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;
}

.c-title {
    display: flex; 
    align-items: center; 
    width: 100%;
}

.ptextItem {
    text-decoration: underline;
    font-weight: 900;
    margin-top: 30px;
}

.height-cap
{
    max-height: 1000px;
}

.submit-button
{
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    margin-top: 250px;
}

.total-modal {
    background-color: white; 
    border-radius: 5px; 
    border: 2px solid #555; 
    padding-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
}

.total-content {
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;    
    max-height: 690px;
}

.c-content-item {
    margin-top: 20px; 
    margin-bottom: 20px; 
    max-height: 230px; 
    display: contents;
}
.scrollable-div
{
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;
    max-height: 50%;
 }

.list-header-row
{
  margin-bottom: -20px;
}
.requested-not-assigned	
{	
    vertical-align: middle;	
    width: 35px;	
    height: 35px;	
}

.box {
  width: 180px;
  margin: 20px 0;
  border: 1px solid #ccc; 
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  height: fit-content;
}

.box h3, .box h4 {
  margin: 0;
  padding: 0;
  font-weight: 700;
}

ul {
  list-style-type: none;
  padding: 0;
}

ul li {
  padding: 8px;
  cursor: pointer;
  background-color: white;
  margin-bottom: 5px;
}

ul li:hover {
  background-color: #e6e6e6;
}

.buttons {
  margin-top: 20px;
}

#dcValue {
  font-weight: bold;
  text-align: center;
  padding-bottom: 50px;
}

.ative {
  background-color: #e6e6e6 !important;
}

h1 {
  font-size: 60px;
}

///MODAL

.component_roadblock {
  display:flex; 
  flex-direction: row; 
  justify-content: flex-start; 
  gap: 10px; 
  align-content: center;
  align-items: center; 
  align-self: center; 
  margin-left: 10px;
}

.component_label {
  display:flex; 
  flex-direction: row; 
  justify-content:space-between; 
  align-items:center
}

.dilemma-container {
  width: 600px;
  margin: auto;
  text-align: center;
}

.threshold-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.threshold-container label {
  font-weight: bold;
}

.threshold-container select {
  width: 90px;
  margin-left: 10px;  
  text-align: end;
  padding: 10px;
  option{
    text-align: center;
  }

}
.attSelect {
  width: 90px;
  text-align: end;
  padding: 10px;
}

select option {
  text-align: center;
  padding-right: 20px;
}

.brain-button {
  width: 100px;
  height: 50px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
 // background-image: url('assets/img/brain.png');
  border-radius: 8px;
  border: none;
  cursor: pointer;
}
.iconBrain {
  width: 53px;
  height: 45px; 
 // background-image: url('assets/img/icon_dilemma.png');
  background-size: cover; 
  background-position: center; 
  background-repeat: no-repeat;
  border: none; 
  cursor: pointer; 
}

.brain-button:hover {
  box-shadow: 1px 1px 1px 4px lightblue;
}

.attributes-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.attribute-box {
  text-align: center;
}

.attribute-box label {
  display: block;
  margin-bottom: 5px;
  font-size: 17px;
  font-weight: normal;
}

.description-area {
  width: 100%;
  height: 80px;
  margin-top: 20px;
  padding: 8px;
}

.save-button {
  background-color: #3472f7;
  color: white;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  margin-top: 20px;
  border-radius: 6px;
}

.gray-color-th, th { 
  color: #D3D3D3 !important;
}
.icon-css {
  font-weight: bold; 
  font-size: 145%; 
  float: right; 
  margin-right: 70px; 
  display: flex;                              
  align-items:flex-end; 
  align-content:flex-end;                
}

.margIcon {
  margin: auto;
}