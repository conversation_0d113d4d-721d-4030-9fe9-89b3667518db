<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div style="display: flex; flex-direction: row; justify-content: space-between;">
      <div class="card list-header-row" style="width: 200vw;">
        <app-header-with-buttons [isBackButtonEnabled]="true"
                               (cardBackButtonClick)="redirectToEmotionList()"
                               [cardTitle]="listName"
                               [cardDescription]=""
                               [rightButtonTemplates]="[uploadButtonTemplate]"></app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead style="top: 0px">
          <tr>
            <th>Index</th>
            <th></th>
            <th class="th-clickable"
                (click)="sortListByParameter('id')">
              ID
            </th>
            <th class="th-clickable"
                (click)="sortListByName(this.audioList, 'name')">Name</th>
            <th class="th-clickable"
                (click)="sortListByParameter('voices')">Voices</th>
            <th class="th-clickable"
                (click)="sortListByParameter('emotionId')">
              Emotion
            </th>
            <th class="th-clickable"
                (click)="sortListByParameter('gender')">Gender</th>
            <th class="th-clickable"
                (click)="sortListByParameter('assigned')">Assigned</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let audioFile of audioList;
                let i = index;
                trackBy: trackById
              ">
            <tr *ngIf="audioFile" id="{{ audioFile.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td>
                <app-audio-listener [audioId]="audioFile.id"></app-audio-listener>
              </td>
              <td class="td-id">{{ audioFile.id }}</td>
              <td>
                <input class="form-control form-title form-short"
                       type="text"
                       value="{{audioFile.name}}"
                       #name
                       (change)="lstOnChange(audioFile, 'name', name.value)" />
              </td>
              <td>
                <button ngClass="btn {{ audioFile.voices | voiceButton }}"
                        (click)="toPromptSelectVoice(audioFile)">
                  {{ (audioFile.voices | voiceName) }}
                </button>
              </td>
              <td>
                <button *ngIf="audioFile.voices | hasVoice : Voices.HUMAN"
                        ngClass="btn {{ audioFile?.emotionId ? ' btn-fill' : '' }}"
                        [ngStyle]="{
                    'background-color': ((audioFile?.emotionId | emotion) | information)?.hex,
                      'border-color': (
                        audioFile?.emotionId
                        | emotion
                        | information
                      )?.hex
                    }"
                        (click)="toPromptSelectEmotion(audioFile)">
                  {{ (audioFile?.emotionId | emotion)?.name || 'Undefined'}}
                </button>
              </td>
              <td>
                <button *ngIf="audioFile.voices | hasVoice : Voices.HUMAN"
                        ngClass="btn {{ audioFile.gender | genderButton }}"
                        (click)="toPromptSelectGender(audioFile)">
                  {{ (audioFile.gender | genderName) }}
                </button>
              </td>
              <td [title]="(audioFile | review).assignedAt | location">{{ (audioFile | review).assignedAt.length }}
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove btn-sm"
                        (click)="lstPromptRemove(audioFile)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  </div>
