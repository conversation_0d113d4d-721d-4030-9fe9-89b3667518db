<!-- Baldur's Gate 3 Style Dice Roll Overlay - Full screen modal with 3D dice -->
<div class="dice-overlay" *ngIf="overlayState.isVisible" (click)="onOverlayClick($event)">
  <div class="dice-frame">
    <!-- Decorative fantasy-themed border frame -->
    <div class="frame-border">
      <div class="frame-corner top-left"></div>
      <div class="frame-corner top-right"></div>
      <div class="frame-corner bottom-left"></div>
      <div class="frame-corner bottom-right"></div>
      <div class="frame-edge top"></div>
      <div class="frame-edge bottom"></div>
      <div class="frame-edge left"></div>
      <div class="frame-edge right"></div>
    </div>

    <!-- Main content area within the ornate frame -->
    <div class="dice-content">
      <!-- Header displaying the difficulty class for the roll -->
      <div class="dice-header">
        <div class="difficulty-label">DIFFICULTY CLASS</div>
        <div class="dc-number">{{overlayState.dc}}</div>
      </div>

      <!-- Interactive dice area with 3D d20 -->
      <div class="dice-area">
        <div class="dice-container" [class.rolling]="isRolling" [class.rolled]="hasRolled">
          <!-- 3D dice element with click interaction -->
          <div class="dice-3d"
               [class.rolling]="isRolling"
               [attr.data-face]="hasRolled && overlayState.result?.roll ? overlayState.result.roll : null"
               (click)="onDiceClick($event)">
            <!-- All 20 faces of the icosahedral d20 dice -->
            <!-- Each face represents a number from 1-20 -->
            <figure class="face face-1"></figure>
            <figure class="face face-2"></figure>
            <figure class="face face-3"></figure>
            <figure class="face face-4"></figure>
            <figure class="face face-5"></figure>
            <figure class="face face-6"></figure>
            <figure class="face face-7"></figure>
            <figure class="face face-8"></figure>
            <figure class="face face-9"></figure>
            <figure class="face face-10"></figure>
            <figure class="face face-11"></figure>
            <figure class="face face-12"></figure>
            <figure class="face face-13"></figure>
            <figure class="face face-14"></figure>
            <figure class="face face-15"></figure>
            <figure class="face face-16"></figure>
            <figure class="face face-17"></figure>
            <figure class="face face-18"></figure>
            <figure class="face face-19"></figure>
            <figure class="face face-20"></figure>
          </div>
        </div>

        <!-- User instruction label (shows before rolling) -->
        <div class="dice-label" *ngIf="!hasRolled && !isRolling">
          Click dice to roll
        </div>
      </div>

      <!-- Result display area (shows after rolling) -->
      <div class="dice-action">
        <div class="result-display" *ngIf="hasRolled && overlayState.result">
          <!-- Success/failure outcome with special handling for critical rolls -->
          <div class="outcome"
               [class.success]="overlayState.result.success"
               [class.failure]="!overlayState.result.success">
            <!-- Natural 20 = Critical Success -->
            <ng-container *ngIf="overlayState.result.roll === 20">CRITICAL SUCCESS</ng-container>
            <!-- Natural 1 = Critical Failure -->
            <ng-container *ngIf="overlayState.result.roll === 1">CRITICAL FAILURE</ng-container>
            <!-- Regular success/failure based on DC -->
            <ng-container *ngIf="overlayState.result.roll !== 20 && overlayState.result.roll !== 1">
              {{overlayState.result.success ? 'SUCCESS' : 'FAILURE'}}
            </ng-container>
          </div>
          <!-- Instruction for dismissing the overlay -->
          <div class="continue-prompt">Click anywhere to continue</div>
        </div>
      </div>
    </div>
  </div>
</div>
