<div class="btn-group" style="display: flex; padding-top: 6px;">
  <ng-container *ngFor="let buttonTemplate of buttonTemplates">
    <button [title]="buttonTemplate.title" class="btn" [ngClass]="buttonTemplate.btnClass | buttonClassFormat"
      (click)="buttonTemplate.onClick(null)">
      {{ buttonTemplate.text }}
      <i [class]="buttonTemplate.iconClass"></i>
    </button>
  </ng-container>
</div>