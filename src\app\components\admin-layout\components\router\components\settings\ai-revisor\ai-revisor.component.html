 <!-- Modal INFO-->
 <ng-container *ngIf="isModalInfo">
    <div class="background-div handleOut" aria-hidden="true">     
        <div class="modal-backdrop" *ngIf="isModalInfo"></div>           
        <div id="modal-close" @popup class="popup-report"  (mouseleave)="isModalInfo = false"
            style="background-color: black;">
            <div class="modal-header">
                <div style="display: flex; justify-content: space-between;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">Informações</p>
                    <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                        data-dismiss="background-div" aria-label="Fechar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
            <div class="contextInfo">
                <p class="modal-title title p-text">
                    1 - <span class="span-text">Aprovado: </span> Faz a modificação e sai da lista de revisão.
                </p>
                <p class="modal-title title p-text">
                    2 - <span class="span-text">Reprovado: </span> Não faz a modificação e sai da lista de revisão.
                </p>
                <p class="modal-title title p-text">
                    3 - <span class="span-text">Sem sugestões: </span> Após X verificações, sai da lista de revisão.
                </p>
                <p class="modal-title title p-text">
                    4 - <span class="span-text">Apresentado, mas sem decisão do utilizador: </span> Continua com o contador de verificações zerado.
                </p>
            </div> 
       
        </div>
    </div>
</ng-container>
<!--Fim do Modal INFO-->

    
    <div class="card">
        <div style="display: contents; position: fixed;">
            <div class="col-md-12" style="display: contents"> 
                <div class="header card-header-wrapper" style="height: 100px;"  [ngStyle]="{'height': sendingBatch == undefined ? '100px' : ''}">
                  <div style="position: relative; z-index: 10;">
                    <app-back-button (click)="redirectToSettings()">
                    </app-back-button>
                  </div>

                  <h3 class="title" style="padding-left: 100px;">{{ title }}
                  <i ngClass="iconInter" (click)="onModalClick()" class="pe-7s-info batt"></i>
                </h3>
                  <ng-container *ngIf="sendingBatch !== undefined">
                    <div class="card-send">
                        <div class="card-send-database">
                         <span>Database: </span>
                         <p class="card-send-text">{{nameDB}} </p> {{completedAnalysis ? ' - ' + completedAnalysis : ' - revisão em andamento.'}}
                        </div>
                        <div class="card-send-api">
                            <span style="font-size: 17px;">API: </span>{{' '}}
                             {{environment.model}} {{'  -  '}}  <span>Temperature: {{' '}} </span> {{environment.temperature}} {{'  -  '}}  <span> Limit: {{' '}} </span> {{environment.limitForReview}}
                           </div>  
                        <div class="card-send-cancel">
                          <button *ngIf="listRevisor && !completedAnalysis" (click)="cancelRequest()" class="btn btn-danger btn-fill btn-height">
                            Cancel Review Request
                          </button>
                        </div>                       
                     </div>
                </ng-container>     
                </div>
            </div>
        </div>
    </div>



    <div class="content-holder">            
    <div class="card db-itens">
        <div class="div-menu">
            <table id="customers" style="width: 100%;">
                <tr *ngFor="let item of counterItems; let i = index" [ngClass]="{'selected': selectedItem === item.name}">
                    <td (click)="databaseToReview(item)" style="cursor: pointer;">
                    <span class="div-menu-span">
                        {{ item?.name }}
                    </span>
                    <p>
                        <span class="span-font-weight">Base size: </span> {{item?.baseSize}}
                    </p>
                    <p>
                       <span class="span-font-weight">Total fields: </span> {{item?.totalFields}} 
                    </p>
                    <ng-container *ngIf="item?.totalEmptyFields">
                        <p>
                            <span class="span-font-weight">Total empty fields: </span> {{item.totalEmptyFields ? item.totalEmptyFields : 0}}
                    </ng-container>
                    <ng-container *ngIf="item?.unreviewedItems">
                        <p>
                            <span class="span-font-weight" [ngStyle]="{'color': item?.unreviewedItems != 0 ? 'red': ''}">Fields to be reviewed: </span> {{item?.unreviewedItems}}
                        </p>
                    </ng-container>
                    <ng-container *ngIf="item?.revisedItems">
                        <p>
                            <span class="span-font-weight" style="color: green;">Manually approved reviews: </span> {{item?.revisedItems}}
                        </p> 
                    </ng-container>
           
                    <ng-container *ngIf="selectedItem === item.name">
                        <p>
                            <span class="span-font-weight">Suggestions: </span> {{item?.suggestions ? item?.suggestions : 0}}
                        </p> 
                        <p>
                          <span class="span-font-weight">Suggestions rejected: </span> {{item?.suggestionsRejected ? item?.suggestionsRejected : 0}}    
                      </p> 
                        <p *ngIf="completedAnalysis && item?.totalNoSuggestions">
                            <span class="span-font-weight">Total without suggestions: </span> {{item?.totalNoSuggestions ? item?.totalNoSuggestions : 0}}    
                        </p>      
                    </ng-container>   
                    
                    </td>
                </tr>
                </table>
        </div>

    </div>

    <ng-container *ngIf="!isLotesObeject">
        <div class="card-container myDiv">
            <div id="highlight-element" class="card cards-group container-card" [ngStyle]="{'height': listRevisor.length === 0 ? '400px' : 'auto'}">
                <div *ngFor="let context of listRevisor; let i = index" class="group-together" >               
                      
                    <ng-container *ngIf="(context.newName || context.newDescription || context.newTitle || context.newMessage || context.newWord) && !isLotesObeject ">
                        <div class="card" style="background-color: white;">                       
                            <ng-container *ngIf="context?.newName">
                                <div id="highlight-name-{{ i }}" class="card-div-name">
                                  <h3 class="h3-number">{{ i + 1 }}</h3>
                                    <div class="text-holder">                                                                       
                                        <div class="originalText" >
                                            <span>Name:</span> <br>
                                            <p [innerHTML]="context.name | textDiff: context.newName">{{ context.name }}</p>
                               
                                        </div>                                  
                                            <div class="card text-card">
                                                <div style="width: 100%;">
                                                    <div class="text-to-be-revised">
                                                        <span>Suggestion:</span><br>
                                                        <p [innerHTML]="context.newName | highlightChanges: context.name">{{ context.newName }}</p>                                               
                                                     </div>
                                                </div>
                                                <div class="div-buttom">
                                                    <button (click)="approveText(context.id, context.newName, 'isReviewedName', 'name', i)">
                                                        <i class="pe-7s-check buttomApprove" ></i>
                                                    </button>
                                                    <button (click)="rejectText(context.id,'isReviewedName', 'name', i)">
                                                        <i class="pe-7s-close-circle buttom-reject"></i>
                                                    </button>
                                                </div>
                                            </div>                            
                                        </div>
                                </div> 
                            </ng-container>              
      
                          <ng-container *ngIf="context?.newTitle">
                            <div id="highlight-title-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText">
                                        <span>Title:</span><br>                    
                                        <p [innerHTML]="context.title | textDiff: context?.newTitle" >{{ context.title }}</p>                                 
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>
                                                   <p [innerHTML]="context?.newTitle | highlightChanges: context.title" >{{ context?.newTitle }}</p>                                                                                      
                                                </div>
                                            </div>
                                            <div class="div-buttom">
                                                <button title="Approve" (click)="approveText(context.id, context.title, 'isReviewedTitle', 'title', i)">
                                                    <i class="pe-7s-check buttomApprove"></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedTitle','title', i)">
                                                    <i class="pe-7s-close-circle buttom-reject" ></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div> 
                          </ng-container>

                          <ng-container *ngIf="context?.newDescription">
                            <div id="highlight-description-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText" >
                                        <span>Description:</span><br>                         
                                        <p [innerHTML]="context.description | textDiff: context?.newDescription">{{ context.description }}</p>                                     
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>                                              
                                                    <p [innerHTML]="context?.newDescription | highlightChanges: context.description">{{ context?.newDescription }}</p>
                                                </div>
                                            </div>
                                            <div class="div-buttom">
                                                <button title="Approve" (click)="approveText(context.id, context.newDescription, 'isReviewedDescription', 'description', i)">
                                                    <i class="pe-7s-check buttomApprove" ></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedDescription', 'description', i)">
                                                    <i class="pe-7s-close-circle buttom-reject"></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div>
                          </ng-container>

                          <ng-container *ngIf="context?.newMessage">
                            <div id="highlight-message-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText" >
                                        <span>Message:</span><br>                         
                                        <p [innerHTML]="context.message | textDiff: context?.newMessage">{{ context.message }}</p>                                     
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>                                              
                                                    <p [innerHTML]="context?.newMessage | highlightChanges: context.message">{{ context?.newMessage }}</p>
                                                </div>
                                            </div>
                                            <div class="div-buttom">
                                                <button title="Approve" (click)="approveText(context.id, context.newMessage, 'isReviewedMessage', 'message', i)">
                                                    <i class="pe-7s-check buttomApprove"></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedMessage', 'message', i)">
                                                    <i class="pe-7s-close-circle buttom-reject"></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div>
                          </ng-container>

                          <ng-container *ngIf="context?.newWord">
                            <div id="highlight-word-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText" >
                                        <span>Word:</span><br>                         
                                        <p [innerHTML]="context.word | textDiff: context?.newWord">{{ context.word }}</p>                                     
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>                                              
                                                    <p [innerHTML]="context?.newWord | highlightChanges: context.word">{{ context?.newWord }}</p>
                                                </div>
                                            </div>
                                            <div class="div-buttom">
                                                <button title="Approve" (click)="approveText(context.id, context.newWord, 'isReviewedWord', 'word', i)">
                                                    <i class="pe-7s-check buttomApprove"></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedWord', 'word', i)">
                                                    <i class="pe-7s-close-circle buttom-reject"></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div>
                          </ng-container>
                        </div>
                        </ng-container>                                        
                </div> 

                   <ng-container *ngIf="isLoadingLotes && isSelectedItem">
                    <div class="div-loading" style="margin-top: 20px;">
                        <div class="content-refresh">
                            <i class="icon pe-7s-refresh-2 icon-refresh-text"></i>                      
                        </div> 
                          <h4 style="margin-bottom: 0 !important;">Loading, please wait.</h4> 
                          <p *ngIf="sendingBatch" class="p-sendinhg">{{sendingBatch}}</p>               
                            <div class="loading-dots">                        
                                <div class="dot"></div>
                                <div class="dot"></div>
                                 <div class="dot"></div>
                            </div>   
                    </div>                                             
                  </ng-container>

                  <ng-container *ngIf="!isSelectedItem && listRevisor.length === 0">
                    <div class="div-loading">
                        <h4>Select a database to review.</h4>
                    </div>
                  </ng-container>

                  <ng-container *ngIf="isSelectedItem && listRevisor.length === 0 && !isLotesObeject">
                    <div class="div-loading">
                        <h4>No suggestions.</h4>
                    </div>
                  </ng-container>
            </div>
            <ng-container *ngIf="completedAnalysis && listRevisor.length > 0">
                <div class="div-buttons-general">
                    <button title="Approve all" class="btn btn-fill btn-green-approved bt-width m-right" (click)="approveAll()">
                        Approve all
                        <i class="pe-7s-check i-color-font"></i>
                    </button>
                    <button title="Reject All" class="btn btn-fill btn-danger bt-width" (click)="rejectAll()">
                        Reject All
                        <i class="pe-7s-close-circle i-color-font"></i>
                    </button>
                </div>
              </ng-container>
        </div> 
    </ng-container>

    <!-- DICE FRUSTRATION -->
    <ng-container *ngIf="isLotesObeject">
            <div class="card-container myDiv">
                <div id="highlight-element" class="card cards-group container-card" [ngStyle]="{'height': listObjectRevisor.length === 0 ? '400px' : 'auto'}">                  
                      
                        <div *ngFor="let context of listObjectRevisor; let i = index" class="group-together" >  
                            <ng-container *ngIf="context?.newLight">                             
                                    <div class="card" style="background-color: white;">
                                      <div id="highlight-light-{{ i }}" class="card-div-name">
                                          <h3 class="h3-number">{{ i + 1  }}</h3>
                                            <div class="text-holder">                                                                       
                                                <div class="originalText" >
                                                    <span>Light:</span> <br>
                                                    <p [innerHTML]="context.light | textDiff: context.newLight">{{ context.light }}</p>
                                       
                                                </div>                                  
                                                    <div class="card text-card">
                                                        <div style="width: 100%;">
                                                            <div class="text-to-be-revised">
                                                                <span>Suggestion:</span><br>
                                                                <p [innerHTML]="context.newLight | highlightChanges: context.light">{{ context.newLight }}</p>                                               
                                                             </div>
                                                        </div>
                                                        <div class="div-buttom">
                                                            <button (click)="approveObejectText(context, 'light', i)">
                                                                <i class="pe-7s-check buttomApprove" ></i>
                                                            </button>
                                                            <button (click)="rejectTextObject(context, 'light', i)">
                                                                <i class="pe-7s-close-circle buttom-reject"></i>
                                                            </button>
                                                        </div>
                                                    </div>                            
                                                </div>
                                        
                                              </div> 
                                      </div>                           
                           </ng-container>    
                        <ng-container *ngIf="context?.newModerate">                        
                                        <div class="card" style="background-color: white;">
                                        <div id="highlight-moderate-{{ i }}" class="card-div-name">
                                            <h3 class="h3-number">{{ i + 1  }}</h3>
                                              <div class="text-holder">                                                                       
                                                  <div class="originalText" >
                                                      <span>Moderate</span> <br>
                                                      <p [innerHTML]="context.moderate | textDiff: context.newModerate">{{ context.moderate }}</p>
                                         
                                                  </div>                                  
                                                      <div class="card text-card">
                                                          <div style="width: 100%;">
                                                              <div class="text-to-be-revised">
                                                                  <span>Suggestion:</span><br>
                                                                  <p [innerHTML]="context.newModerate | highlightChanges: context.moderate">{{ context.newModerate }}</p>                                               
                                                               </div>
                                                          </div>
                                                          <div class="div-buttom">
                                                              <button (click)="approveObejectText(context, 'moderate', i)">
                                                                  <i class="pe-7s-check buttomApprove" ></i>
                                                              </button>
                                                              <button (click)="rejectTextObject(context, 'moderate', i)">
                                                                  <i class="pe-7s-close-circle buttom-reject"></i>
                                                              </button>
                                                          </div>
                                                      </div>                            
                                                  </div>
                                            </div> 
                                       </div>                              
                        </ng-container>
                        <ng-container *ngIf="context?.newCritical">
                                        <div class="card" style="background-color: white;">
                                        <div id="highlight-critical-{{ i  }}" class="card-div-name">
                                            <h3 class="h3-number">{{ i + 1 }}</h3>
                                              <div class="text-holder">                                                                       
                                                  <div class="originalText" >
                                                      <span>Critical:</span> <br>
                                                      <p [innerHTML]="context.critical | textDiff: context.newCritical">{{ context.critical }}</p>
                                         
                                                  </div>                                  
                                                      <div class="card text-card">
                                                          <div style="width: 100%;">
                                                              <div class="text-to-be-revised">
                                                                  <span>Suggestion:</span><br>
                                                                  <p [innerHTML]="context.newCritical | highlightChanges: context.critical">{{ context.newCritical }}</p>                                               
                                                               </div>
                                                          </div>
                                                          <div class="div-buttom">
                                                              <button (click)="approveObejectText(context, 'critical', i)">
                                                                  <i class="pe-7s-check buttomApprove" ></i>
                                                              </button>
                                                              <button (click)="rejectTextObject(context, 'critical', i)">
                                                                  <i class="pe-7s-close-circle buttom-reject"></i>
                                                              </button>
                                                          </div>
                                                      </div>                            
                                                  </div>
                                            </div> 
                                            </div>            
                        </ng-container>                      
                                           
                    </div> 
    
             
                       <ng-container *ngIf="isLoadingLotes && isSelectedItem">
                        <div class="div-loading" style="margin-top: 20px;">
                            <div class="content-refresh">
                                <i class="icon pe-7s-refresh-2 icon-refresh-text"></i>                      
                            </div> 
                              <h4 style="margin-bottom: 0 !important;">Loading, please wait.</h4> 
                              <p *ngIf="sendingBatch" class="p-sendinhg">{{sendingBatch}}</p>               
                                <div class="loading-dots">                        
                                    <div class="dot"></div>
                                    <div class="dot"></div>
                                     <div class="dot"></div>
                                </div>   
                        </div>                                             
                      </ng-container>
    
                      <ng-container *ngIf="!isSelectedItem && listObjectRevisor.length === 0">
                        <div class="div-loading">
                            <h4>Select a database to review.</h4>
                        </div>
                      </ng-container>
    
                      <ng-container *ngIf="isSelectedItem && listObjectRevisor.length === 0 && !isLotesObeject">
                        <div class="div-loading">
                            <h4>No suggestions.</h4>
                        </div>
                      </ng-container>
                </div>
                <ng-container *ngIf="completedAnalysis && listObjectRevisor.length > 0">
                    <div class="div-buttons-general">
                        <button title="Approve all" class="btn btn-fill btn-green-approved bt-width m-right" (click)="approveAll()">
                            Approve all
                            <i class="pe-7s-check i-color-font"></i>
                        </button>
                        <button title="Reject All" class="btn btn-fill btn-danger bt-width" (click)="rejectAll()">
                            Reject All
                            <i class="pe-7s-close-circle i-color-font"></i>
                        </button>
                    </div>
                  </ng-container>
            </div> 

    </ng-container>

    </div>

