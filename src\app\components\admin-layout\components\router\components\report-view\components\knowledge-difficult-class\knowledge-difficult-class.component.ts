import { Component, OnInit } from '@angular/core';
import { OptionService, UserSettingsService } from 'src/app/services';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SortingService } from 'src/app/services/sorting.service';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { Option } from 'src/app/lib/@bus-tier/models';
import { comparable } from 'src/lib/others';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-knowledge-difficult-class',
  templateUrl: './knowledge-difficult-class.component.html',
  styleUrls: ['./knowledge-difficult-class.component.scss']
})
export class KnowledgeDifficultClassComponent extends SortableListComponent<Option> implements OnInit{


    investigation: Option[] = [];
    reverseLocation: boolean = false;
  
    constructor(private _optionService: OptionService,
      private _roadblockService: RoadBlockService,
      public readonly router: Router,
      private _sorting : SortingService,
      _activatedRoute: ActivatedRoute,
      _userSettingsService: UserSettingsService,
    ) {
      super(_optionService, _activatedRoute, _userSettingsService, 'name');
    }
  
    override async ngOnInit() {
  
      this._optionService.toFinishLoading();
      this.investigation = this._optionService.models.filter((option) => option.investigaDifficulty !== undefined && option.choiceDifficulty === undefined);   ;
    }
  
    access(id: string) {
      let levelId = this._roadblockService.getLevelId(id);
      let dialogueId = this._roadblockService.getDialogueId(id);
  
      if(id.split(".")[0].includes("ML"))
      {
        this.router.navigate([
          'microloops/' + levelId +
          '/dialogues/' + dialogueId
        ],
        {fragment: id});
      }
      else
      {
        this.router.navigate([
          'levels/' + levelId +
          '/dialogues/' + dialogueId
        ],
        {fragment: id});
      }
    }
    sortArrayByType(array: Option[]) {	
    }	
  
    sortArrayByLocation(array: Option[]){  
     array.sort((a, b) => {	
        return this._sorting.sortArrayByLevelId(this.reverseLocation, a.id, b.id)	
      });	
      this.reverseLocation = !this.reverseLocation;	
    }
      
    private isSortedByMessage: boolean = false;
  
    sortArrayByChoice(array: Option[]) {
      array.sort((a, b) => {
        let messageA = comparable(a.message);
        let messageB = comparable(b.message);
  
        if (!messageA || !messageB) return 0;
  
        if (messageA > messageB) return this.isSortedByMessage ? 1 : -1;
        else if (messageB > messageA) return this.isSortedByMessage ? -1 : 1;
  
        return 0;
      });
  
      this.isSortedByMessage = !this.isSortedByMessage; 
    }
  
    private isAscendingOrder: boolean = true;
    sortArrayByDifficult(array: Option[]){    
    
      array.sort((a, b) => { 
        const aValue = a.resultDC ?? 0;
        const bValue = b.resultDC ?? 0;
  
        if (this.isAscendingOrder) {
            return aValue - bValue; // Ordem crescente
        } else {
            return bValue - aValue; // Ordem decrescente
        }
    });
    this.isAscendingOrder = !this.isAscendingOrder;
  
    }

}
