import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { Item } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Tag } from 'src/app/lib/@bus-tier/models/Tag';
import { Button } from 'src/app/lib/@pres-tier/data';
import { TranslationPipe } from 'src/app/pipes/translation.pipe';
import {
  AreaService,
  ChestService,
  EventService,
  IngredientVarianceService,
  ItemService,
  ItemsSelectorService,
  ParticleVarianceService,
  PopupService,
  ReviewService, UserSettingsService
} from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LanguageService } from 'src/app/services/language.service';
import { SortingService } from 'src/app/services/sorting.service';
import { TagService } from 'src/app/services/tag.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert, Popup } from 'src/lib/darkcloud';
import { Area } from 'src/lib/darkcloud/angular/dsadmin/v8';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ItemProperty, ItemType } from 'src/lib/darkcloud/dialogue-system';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { comparable, comparableString, extractInt, HighlightElement, removeAccents } from 'src/lib/others';
import * as XLSX from 'xlsx';
import { fadeIn, popup } from './bound-list.component.animations';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-bound-item-list',
  templateUrl: './bound-item-list.component.html',
  styleUrls: ['./bound-item-list.component.scss'],
  animations: [fadeIn, popup]
})
export class BoundItemListComponent extends TranslatableListComponent<Item> implements OnDestroy
{
  ItemType = ItemType;
  language: language = 'PT-BR';
  ingredients;
  itemClass: ItemClass;
  itemIds: string[] = [];
  currencyItems: Item[];
  keyItems: Item[];
  potionItems: Item[];
  problemItems: Item[];
  undefinedItems: Item[];
  proxyIds: string[] = [];
  generatedListName: string;
  tags: Tag[];
  custom: Custom; 
  preloadedAreas: Area[];
  timeout:any;
  timeout2:any;
  timeout3:any;
  timeout4:any;
  popupReport: boolean = false;
  popupItem: Item;
  public query: string;
  invertSorting: boolean = true;
  indexTagSorting = 0;
  invertAreaSorting: boolean = false;
  invertAssignedSorting: boolean = true;
  showItemList: boolean = false;
  invertIndexSorting: boolean = true;
  itemClassName: string;
  itemClassId: string;


  constructor(
    private _itemsSelectorService: ItemsSelectorService,
    private activatedRoute: ActivatedRoute,
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _areaService: AreaService,
    protected _particleVarianceService: ParticleVarianceService,
    protected _ingredientVarianceService: IngredientVarianceService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _popupService: PopupService,
    private _tagService: TagService,
    private _reviewService: ReviewService,
    private _customService: CustomService,
    private _eventService: EventService,
    protected override _languageService: LanguageService,
    private _sortingService: SortingService,
    protected override _translationService: TranslationService,
    private _chestListService: ChestService,
    private pipeTranslater: TranslationPipe,
  ) {
    super(_itemService, activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  
  public override readonly addButtonTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.getItems.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  public readonly createItemButtonTemplate: Button.Templateable = 
  {
    title: 'Create a new Item object',
    onClick: this.createItem.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN
  }

  public readonly exportExcelButtonTemplate: Button.Templateable = 
  {
    btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
    title: 'Export to Excel',
    onClick: this.downloadAsExcel.bind(this),
    iconClass: 'pe-7s-cloud-download',
  };

  override async ngOnInit()
  {
    await this._itemClassService.toFinishLoading();
    await this._areaService.toFinishLoading();
    let id = this.activatedRoute.snapshot.paramMap.get('id');
    this.custom = await this._customService.svcGetInstance();
    if(!this.custom.weaponClassItem) this._customService.svcToModify(this.custom);  
    this.itemClass = this._itemClassService.svcFindById(id);  
    this.generatedListName = "'" + this.itemClass?.name + "' Items List";
    await this.initializeItems(this.currencyItems, 'CURRENCY');
    await this.initializeItems(this.keyItems, 'KEY');
    await this.initializeItems(this.potionItems, 'POTION');
    await this.initializeItems(this.problemItems, 'PROBLEM');
    await this.initializeItems(this.undefinedItems, 'undefined');

    this.tags = this._tagService.models;
    this.showItemList = false;

    this.lstFilterValue['areaId'] = 'ALL';
    this.preloadedAreas = this._areaService.models;
    this.timeout = setTimeout(() => this.test(), 600);
    this.timeout2 = setTimeout(() => this.test(), 1000);
    console.log('Lista de itens',this._itemService.models); 
    console.log('Lista ItemsSelectorService: ',this._itemsSelectorService.models);   
  }

 
  refreshList(scroll?: boolean)
  {
    let listProxy = this.itemClass.itemIds;
    this.itemClass.itemIds = [];
    this.timeout3 = setTimeout(() => 
    {
      this.itemClass.itemIds = listProxy;
      this._itemService.svcReviewAll();
    }, 1);
  }

  sortListByXP(list: string[]) {
    let itemList = this._itemService.svcCloneByIds(list);
  
    itemList.sort((a, b) => {
      // Primeiro, verificamos se algum dos valores de gateXP é undefined
      if (a.gateXP === undefined && b.gateXP === undefined) {
        return 0; // Ambos são undefined, então são considerados iguais
      }
  
      if (a.gateXP === undefined) {
        return 1;
      }  

      if (b.gateXP === undefined) {
        return -1;
      }
  
      const itemNameA = comparable(extractInt(a.gateXP.toString()));
      const itemNameB = comparable(extractInt(b.gateXP.toString()));
  
      // Lógica de ordenação: crescente ou decrescente baseado em invertIndexSorting
      if (itemNameA > itemNameB) {
        return this.invertIndexSorting ? 1 : -1;
      } else if (itemNameA < itemNameB) {
        return this.invertIndexSorting ? -1 : 1;
      } else {
        return 0; // São iguais
      }
    });
  
    this.invertIndexSorting = !this.invertIndexSorting;

    let sortedList = itemList.map(x => x.id);
    this.itemClass.itemIds = [];
    this.itemClass.itemIds = sortedList;
  }
  

  sortListById(list: string[])
  {
    let itemList = this._itemService.svcCloneByIds(list);

    itemList.sort((a,b) => 
    {
      let itemNameA = comparable(extractInt(a.id));
      let itemNameB = comparable(extractInt(b.id));

      if(itemNameA > itemNameB) return this.invertIndexSorting? 1 : -1;
      
      else if(itemNameB > itemNameA) return this.invertIndexSorting? -1 : 1;      
      else return 0;
    });

    this.invertIndexSorting = !this.invertIndexSorting;
    let sortedList = itemList.map(x => x.id);
    this.itemClass.itemIds = [];
    this.itemClass.itemIds = sortedList;
  }

  sortListByAssigned(list: string[])
  {
    let itemList = this._itemService.svcCloneByIds(list);

    itemList.sort((a,b) => 
    {
      let itemNameA = this._reviewService.objsReviews.filter(review => review.modelId === a.id).length;
      let itemNameB = this._reviewService.objsReviews.filter(review => review.modelId === b.id).length;

      if(itemNameA > itemNameB) return this.invertAssignedSorting? 1 : -1;      
      else if(itemNameB > itemNameA) return this.invertAssignedSorting? -1 : 1;      
      else return 0;
    });

    this.invertAssignedSorting = !this.invertAssignedSorting;
    let sortedList = itemList.map(x => x.id);
    this.itemClass.itemIds = [];
    this.itemClass.itemIds = sortedList;
  }

  override sortListByName(list: string[])
  {
    let itemList = this._itemService.svcCloneByIds(list);

    itemList.sort((a,b) => 
    {
      let itemNameA = comparable(a.name);
      let itemNameB = comparable(b.name);

      if(itemNameA > itemNameB) return this.invertSorting? 1 : -1;      
      else if(itemNameB > itemNameA) return this.invertSorting? -1 : 1;      
      else return 0;
    });

    this.invertSorting = !this.invertSorting;
    let sortedList = itemList.map(x => x.id);
    this.itemClass.itemIds = [];
    this.itemClass.itemIds = sortedList;
  }

  sortListByArea(list: string[])
  {
    if(this.lstFilterValue['areaId'] !== 'ALL') return;
    let sortedList = list.sort((a, b) => 
    {
      let itemA = this._itemService.svcFindById(a);
      let itemB = this._itemService.svcFindById(b);

      let areasA = this._itemService.svcGetAllAreasAssigned(itemA.id);
      let areasB = this._itemService.svcGetAllAreasAssigned(itemB.id);

      const sortedArrayList = this._sortingService.sortArrayByAlphanumericValue(
        this.invertAreaSorting, 
        areasA.map(area => area.hard.id), 
        areasB.map(area => area.hard.id));
        
      return sortedArrayList;
    });

    this.invertAreaSorting = !this.invertAreaSorting;
    this.itemClass.itemIds = [];
    this.itemClass.itemIds = sortedList;
  }

  sortListByTags(list: string[])
  {
    let itemList = this._itemService.svcCloneByIds(list);
    let tagsIds = this._itemService.svcGetTagsUsed(itemList);

    if(tagsIds.length != 0)
    {
      let searchTagIds = [];
      for(let i = 0; i < tagsIds.length; i++)
      {
        let index = i + this.indexTagSorting;
        if (index >= tagsIds.length) index -= tagsIds.length;
        searchTagIds.push(tagsIds[index]);
      }
      itemList = itemList.map(item => 
      {
          let firstTag = 99999;
          if (item.tagIds)
          {
            for (let tagIndex = 0; tagIndex < searchTagIds.length; tagIndex++)
            {
              if(item.tagIds.includes(searchTagIds[tagIndex]))
              {
                firstTag = tagIndex;
                tagIndex = searchTagIds.length;
              }
            }
          }
          item["firsTag"] = firstTag;
          return item
        }).sort((a, b) => 
        {
          if(a["firsTag"] > b["firsTag"]) return 1;
          else if(a["firsTag"] < b["firsTag"]) return -1;
          return 0;
        })

      this.indexTagSorting++;
      if(this.indexTagSorting >= tagsIds.length) this.indexTagSorting = 0;      
    }

    let sortedList = itemList.map(x => x.id);
    this.itemClass.itemIds = [];
    this.itemClass.itemIds = sortedList;
  }

  async initializeItems(items:Item[], type:string)
  {
    await this._itemService.toFinishLoading();

    items = this._itemService.models.filter(x => +x.type == ItemType[type]);
    items = items.filter( x => 
    {
      return !this.itemClass.itemIds.includes(x.id) &&  !this._itemClassService.getAllBoundItemIds().includes(x.id);
    });
  }

  getItems()
  {
    this.showItemList = !this.showItemList;
  }

  toggleProxyId(id: string)
  {
    if(!this.proxyIds.includes(id)) this.proxyIds.push(id);    
    else
    {
      let index = this.proxyIds.indexOf(id);
      this.proxyIds.splice(index, 1);
    }
  }

  submitItemIds()
  {
    this.itemClass.itemIds = this.itemClass.itemIds.concat(this.proxyIds);
    this._itemClassService.svcToModify(this.itemClass);

    this.proxyIds = [];

    this.showItemList = false;
    this.initializeItems(this.currencyItems, 'CURRENCY');
    this.initializeItems(this.keyItems, 'KEY');
    this.initializeItems(this.potionItems, 'POTION');
    this.initializeItems(this.problemItems, 'PROBLEM');
    this.initializeItems(this.undefinedItems, 'undefined');
  }

  async removeItemId(item:Item)
  {
    const confirm = await Alert.showConfirm('Are you sure?', '', 'Yes');
    if(!confirm) return;

    for(let i = 0; i < this._itemsSelectorService.models.length; i++)
    {
      if(this._itemsSelectorService.models[i].itemName == item.name)
      {
        await this._itemsSelectorService.svcToRemove(this._itemsSelectorService.models[i].id);
        break;
      }
    }

    for(let i = 0; i < this._itemService.models.length; i++)
    {
      if(this._itemService.models[i].name == item.name)
      {
        await this._itemService.svcToRemove(this._itemService.models[i].id);
        break;
      }
    }

    item.tagIds = item.tagIds?.filter(i=>i !== 'tg19');//remove common ingredient 

    this.itemClass.itemIds = this.itemClass.itemIds.filter(x => 
    {
      return (x != item.id);
    });
    this._itemClassService.svcToModify(this.itemClass);
    this._itemClassService.toSave();

    this.initializeItems(this.currencyItems, 'CURRENCY');
    this.initializeItems(this.keyItems, 'KEY');
    this.initializeItems(this.potionItems, 'POTION');
    this.initializeItems(this.problemItems, 'PROBLEM');
    this.initializeItems(this.undefinedItems, 'undefined');
    this.deleteItemFromChest(item.name);
  }

  async deleteItemFromChest(itemName:string)
  {
    for(let i = 0; i < this._chestListService.models.length; i++)
    {
      if(this._chestListService.models[i].chestName == itemName) 
      {
        await this._chestListService.svcToRemove(this._chestListService.models[i].id);
        break;
      }
    }
  }

  async openWeaponDetailsId(id: string)
  {
    this._router.navigate(['itemDetails/' + id]);
  }

  redirectToItemClasses()
  {
    this._router.navigate(['itemClass']);
  }

  public async toPromptSelectItemType(item: Item) 
  {
    const selectedItemTypeButton = await this._popupService.fire<ItemType, ItemType>
    (
      new Popup.Interface(
        {
          title: 'Select Item Type',
          actionsClass: 'column',
        },
        Popup.itemTypeButtons,
        {
          hideButton: { value: item.type },
        }
      )
    );

    if (!selectedItemTypeButton) return;
    
    item.type = selectedItemTypeButton.value;
    await this._itemService.svcToModify(item);
  }

  public async toPromptMoveItem(item: Item)
  {
    let buttons: Popup.Button<ItemClass>[] = [];
    this._itemClassService.models.forEach(x => 
    {
      let button = new Popup.Button<ItemClass>(x.name, x, ' btn-ItemClass btn-success btn');
      buttons.push(button); // Cache to class variable on init
    });

    const targetItemClass = await this._popupService.fire<ItemClass, ItemClass>(
      new Popup.Interface(
        {
          title: 'Select Item Class',
          actionsClass: 'column'
        },
        buttons
      )
    );

    this._itemClassService.moveItemId(this.itemClass, targetItemClass.value, item.id);
    let itemIds = this.itemClass.itemIds;
    this.itemClass.itemIds = [];

   this.timeout4 = setTimeout(() => 
   {
      this.itemClass.itemIds = itemIds;
      this._itemService.svcReviewAll();
    }, 1);

  }

  public async toPromptSelectItemProperty(item: Item) 
  {
    const selectedItemPropertyButton = await this._popupService.fire< ItemProperty, ItemProperty>(
      new Popup.Interface(
        {
          title: 'Select Item Property',
          actionsClass: 'column',
        },
        Popup.itemPropertyButtons,
        {
          hideButton: { value: item.property },
        }
      )
    );

    if (!selectedItemPropertyButton) return;
    
    item.property = selectedItemPropertyButton.value;
    await this._itemService.svcToModify(item);
  }

  public async addTagWithPopup(item: Item)
  {
    let buttons: Popup.Button<Tag>[] = [];
    this.tags.forEach(x => 
    {
      buttons.push(new Popup.Button<Tag>(x.name, x, 'btn btn-fill'));
    });

    const selectedTag = await this._popupService.fire< Tag, Tag>(
      new Popup.Interface(
        {
          title: 'Select Tag',
          actionsClass: 'column'
        },
        buttons
      )
    );

    if(!selectedTag) return;

    if(item.tagIds)
      item.tagIds.push(selectedTag.value.id);
    else
    {
      item.tagIds = [];
      item.tagIds.push(selectedTag?.value?.id);
    }

   await this._itemService.svcToModify(item);
   await this._itemService.toSave()
  }

  addItemVarianceIgredients(value: string, item: Item) {
   
    if(value === "Ingrediente Comum") {
      this._ingredientVarianceService.createNewParticleDrop('', item.name);
    } else {
      return;
    }

  }

  addTag(item: Item, tagId: string)
  {
    if(tagId == "") return;

    if(item.tagIds)
      item.tagIds.push(tagId);
    else
    {
      item.tagIds = [];
      item.tagIds.push(tagId);
    }

    this._itemService.svcToModify(item);
    this._itemService.svcReviewAll()
  }

  async changeTag(item: Item, tagId: string, index: number)
  {
    if(tagId == "") item.tagIds.splice(index, 1);    
    else
    {
      item.tagIds[index] = tagId;
    }
    
    await this._itemService.svcToModify(item);
    await this._itemService.toSave()
  }

  closeItemsList()
  {
    this.showItemList = false;
    this.proxyIds = [];
  }

  levelReferencePopup(item:Item)
  {  
    this.popupItem = item;
    this.ngOnInit();
    this.popupReport = true;
  }

  handleOutsideMouseClick(event: MouseEvent)	
  {	
    if(!this.popupReport) return;	
    	
    if(event.clientX < 639 || event.clientX > 1915 || event.clientY > 934 || event.clientY < 240)	
    {	
        this.closeLevelReferencePopup()	
    }	
  }	

  closeLevelReferencePopup()
  {
    this.popupReport = false;
    this.ngOnInit();
  }

  public async createItemSelector( item) {

    let itemSelector = this._itemsSelectorService.createNewItemsSelector()
    itemSelector.itemName = item.name;
    itemSelector.itemsSection = this.itemClass.hard.name;
    itemSelector.concept = false;
    itemSelector.finish = false;
    itemSelector.description = undefined;
    itemSelector.ic_id = item.id;
  }

//Create a new class item
  public async createItem()
  {
    let newItem;
     try 
    {
      newItem = await this._itemService.svcPromptCreateNew(undefined);
    } 
    catch (e) 
    {
      Alert.showError("Item já existe!");
    }
    if(!newItem) return;

    //Creates a new Selection Section item
    let itemSelector = this._itemsSelectorService.createNewItemsSelector()
    itemSelector.itemName = newItem.name;
    itemSelector.itemsSection = this.itemClass.hard.name;
    itemSelector.concept = false;
    itemSelector.finish = false;
    itemSelector.description = undefined;
    itemSelector.ic_id = newItem.id;

    this._particleVarianceService.createNewParticleDrop('', newItem.name);
    this._particleVarianceService.createNewParticleDrop('A', newItem.name);
    this._particleVarianceService.createNewParticleDrop('B', newItem.name);

    this._itemsSelectorService.svcToModify(itemSelector);
    this._itemsSelectorService.toSave();

    this._itemService.srvAdd(newItem);
    this.itemClass.itemIds.push(newItem.id);
    this._itemClassService.svcToModify(this.itemClass);
    this._itemClassService.toSave();

    this.refreshList();
    HighlightElement(newItem.id, 500, true);
  }

  public filter(queryString: string)
  {
    this.query = queryString;
  }

  public filterList(ids: string[])
  {
    let filteredIds = [];
    ids.forEach(id =>
    {
      let addItem:boolean = true;
      let item = this._itemService.svcFindById(id);     
      if(this.lstFilterValue['areaId'] !== 'ALL')
      {
        let areas = this._itemService.svcGetAllAreasAssigned(item.id).map(area => area.id);     
        if(!areas.includes(this.lstFilterValue['areaId'].toString())) addItem = false;        
      }
      if(this.query)
      {
        if(!(comparableString(item.name, this.lstSearchOptions).includes(comparableString(this.query, this.lstSearchOptions))) &&
          !(comparableString(item.description, this.lstSearchOptions)?.includes(comparableString(this.query, this.lstSearchOptions)))) addItem = false;
      }
      if(addItem) filteredIds.push(item.id);      
    })
  
    return filteredIds;
  }

  public changeGateXp(item: Item, value: number)
  {
    item.gateXP = +value;
    this._itemService.svcToModify(item);
  }

  public downloadAsExcel() 
  {
    const characterTable = document.createElement('table');
    const tHead = characterTable.createTHead();
    const tHeadRow = tHead.insertRow();
    tHeadRow.insertCell().innerText = 'ID';
    tHeadRow.insertCell().innerText = 'Name';
    tHeadRow.insertCell().innerText = 'Description';
    tHeadRow.insertCell().innerText = 'Tags';
    const tBody = characterTable.createTBody();
   const lisItem = this._itemService.models.filter(item => this.filterList(this.itemClass.itemIds).includes(item.id)).forEach((item) => 
    {
      const tBodyRow = tBody.insertRow();
      tBodyRow.insertCell().innerText = item.id;
      tBodyRow.insertCell().innerText = item.name;
      tBodyRow.insertCell().innerText = item.description;
      let tags = "";
      item.tagIds?.forEach(tagId => tags += this._tagService.svcFindById(tagId).name + " ");
      tBodyRow.insertCell().innerText = tags;     
    });
   
    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(characterTable);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Item List from Class');

    const now = new Date();
    XLSX.writeFile(wb, now.toLocaleDateString() + '_' + now.toLocaleTimeString() +
        ' DSAdmin Item List from Class ' + removeAccents(this.itemClass.name) + '.xlsx'
    );
  }

  async changeItemName(item:Item, newName:string)
  {
    if(this.isItemAlreadyCreated(newName))
    {
      Alert.showError(`The item: ${item.name} already exists!`);
      return;
    }
    else
    {
      await this.changeItemselectorName(item.name, newName);
      item.name = newName;
      item.isReviewedName = false;
      item.revisionCounterNameAI = 0;
      await this._itemService.svcToModify(item);
    }   
  }
  descriptionChange(item:Item, description: string, value: string) {
    item.isReviewedDescription = false;
    item.revisionCounterDescriptionAI = 0;
    this.lstOnChange(item, 'description', value);
  }

  isItemAlreadyCreated(newName:string):boolean
  {
    for(let i = 0; i < this._itemService.models.length; i++)
    {
      if(this._itemService.models[i].name == newName) return true;     
    }
    return false;
  }

  async changeItemselectorName(itemName:string, newName:string)
  {
    for(let i = 0; i < this._itemsSelectorService.models.length; i++)
    {
      if(this._itemsSelectorService.models[i]?.itemName.trim() == itemName.trim())
      {
        this._itemsSelectorService.models[i].itemName = newName;
        await this._itemsSelectorService.svcToModify(this._itemsSelectorService.models[i]);
        break;
      }
    }
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout);
    clearInterval(this.timeout2);
    clearInterval(this.timeout3);
    clearInterval(this.timeout4);
  }
}
