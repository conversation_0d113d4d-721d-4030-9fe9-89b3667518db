<div class="main-content">
  <div class="container-fluid">
      <div class="list-header-row update">
        <div class="card">
          <div style="display: flex; justify-content: space-between;">
            <div class="card-header-content">
              <h3 class="title">Situational Modifier (SM)</h3>
              <p style="width:60vw;" class="category">{{ description}}</p>
            </div>
            <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
              <ng-container>
                <!--BUTTON EXCEL-->
                <div id="button" style="position: absolute; margin-top: 60px;">
                  <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
                    [buttonTemplates]="[excelButtonTemplate]">
                  </app-button-group>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
  
      <ng-container *ngIf="isSituational">
        <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
          <table class="table table-list borderList">
            <thead>
              <tr>
                <th *ngFor="let title of titles"> {{title}}</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let item of listSituationalModifier; let i = index">
                <tr *ngFor="let sm of item.situationalmodifier; let j = index">
                  <!-- Exibir knowledge apenas na primeira linha -->
                  <td *ngIf="j === 0" class="td-id aligTitle" [attr.rowspan]="item.situationalmodifier.length">
                    {{ item.knowledge }}
                  </td>
                  <td class="td-id" style="width: 250px !important;">
                    <input
                      class="background-input-table-color form-control form-short text-center"
                      placeholder=" "
                      type="number"
                      [(ngModel)]="item.situationalmodifier[j]"
                      (change)="changeSituationalModifierValue(i, j, 'situationalmodifier', item.situationalmodifier[j])"
                    />
                  </td>
                  <td class="td-id">
                    <input
                      class="background-input-table-color form-control form-short"
                      placeholder=" "
                      type="text"
                      [(ngModel)]="item.factor[j]"
                      (change)="changeSituationalModifierValue(i, j, 'factor', item.factor[j])"
                    />
                  </td>
                  <td class="td-id">
                    <input
                      class="background-input-table-color form-control form-short"
                      placeholder=" "
                      type="text"
                      [(ngModel)]="item.description[j]"
                      (change)="changeSituationalModifierValue(i, j, 'description', item.description[j])"
                    />
                  </td>
                </tr>
              </ng-container>
            </tbody>
            
          </table>
        </div>
      </ng-container>
  
      <ng-container *ngIf="!isSituational">
        <div class="card" style="text-align: center; padding: 20px;">
          <h3>Empty list. Click to create the list.</h3>
        </div>
      </ng-container>