<div style="overflow-x: auto;">
  <br />
  <!--Header-->
  <div class="list-header-row update">
    <div class="card">
      <app-header-with-buttons 
        [cardTitle]="'Improve Information'"
        [cardDescription]="description"
        [rightButtonTemplates]="[excelButtonTemplate]"
        [isBackButtonEnabled]="false">
      </app-header-with-buttons>
    </div>
  </div>
  <!--List-->
    <div class="card list-header-row">
      <table class="table table-list">
        <thead >
          <tr>
            <th rowspan="2">Level (WL)</th>
            <th rowspan="2">Rarity (RW)</th>
            <th rowspan="2">ATKw</th>
            <th >Cooldown</th>
            <th rowspan="2">Shots</th>
            <th rowspan="2">SPDBoost</th>
            <th class="gray-color2">cost REFIT</th>
            <th colspan="2">cost UPGRADE</th>
            <th class="gray-color2" colspan="6">cost ENHANCEMENT</th>
          </tr>
          <tr>
            <th class="time-color">seconds</th>
            <th class="gold-color">Gold</th>
            <th class="gold-color">Gold</th>
            <th class="ichor-color">Ichor</th>
            <th class="souls-color">Souls</th>
            <th class="time-color">minutes</th>
            <th class="rubies-color">Rubies</th>
            <th>Titanium</th>
            <th>Adamantio</th>
            <th>Hellnium</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let weaponUpgrade of weaponUpgrades">
        <!--<ng-container> -->
            <tr id="{{ weaponUpgrade?.id }}">
              <td class="td-id">{{ weaponUpgrade?.level }}</td>
              <td [ngStyle]="{'background-color': weaponUpgrade.rarityName | tierColor : 'Weapon Rarity', 'color':'#fff'}">
                {{weaponUpgrade.rarityName}}
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #atkw [value]="weaponUpgrade?.atkw"
                       (change)="changeItemValue(weaponUpgrade, atkw.value == '' ? undefined : +atkw.value, 'atkw')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #cooldown [value]="weaponUpgrade?.cooldown"
                       (change)="changeItemValue(weaponUpgrade, cooldown.value == '' ? undefined : +cooldown.value, 'cooldown')" />
              </td> 
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #shots [value]="weaponUpgrade?.shots"
                       (change)="changeItemValue(weaponUpgrade, shots.value == '' ? undefined : +shots.value, 'shots')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #spdBoost [value]="weaponUpgrade?.spdBoost"
                       (change)="changeItemValue(weaponUpgrade, spdBoost.value == '' ? undefined : +spdBoost.value, 'spdBoost')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #gold [value]="weaponUpgrade?.gold"
                       (change)="changeItemValue(weaponUpgrade, gold.value == '' ? undefined : +gold.value, 'gold')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #goldUp [value]="weaponUpgrade?.goldUp"
                       (change)="changeItemValue(weaponUpgrade, goldUp.value == '' ? undefined : +goldUp.value, 'goldUp')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #ichor [value]="weaponUpgrade?.ichor"
                       (change)="changeItemValue(weaponUpgrade, ichor.value == '' ? undefined : +ichor.value, 'ichor')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #souls [value]="weaponUpgrade?.souls"
                       (change)="changeItemValue(weaponUpgrade, souls.value== '' ? undefined : +souls.value, 'souls')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #time [value]="weaponUpgrade?.time"
                       (change)="changeItemValue(weaponUpgrade, time.value == '' ? undefined : +time.value, 'time')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #rubies [value]="weaponUpgrade?.rubies"
                       (change)="changeItemValue(weaponUpgrade, rubies.value == '' ? undefined : +rubies.value, 'rubies')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #titanium [value]="weaponUpgrade?.titanium"
                       (change)="changeItemValue(weaponUpgrade, titanium.value == '' ? undefined : +titanium.value, 'titanium')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #adamantium
                       [value]="weaponUpgrade?.adamantium"
                       (change)="changeItemValue(weaponUpgrade, adamantium.value == '' ? undefined : +adamantium.value, 'adamantium')" />
              </td>
              <td class="td-id">
                <input placeholder=" " class="background-input-table-color"
                       type="number" #hellnium [value]="weaponUpgrade?.hellnium"
                       (change)="changeItemValue(weaponUpgrade, hellnium.value == '' ? undefined : +hellnium.value, 'hellnium')" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>

    </div>
  </div>