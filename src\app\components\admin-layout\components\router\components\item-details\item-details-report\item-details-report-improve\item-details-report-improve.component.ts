import { ChangeDetectorRef, Component, Input, OnInit, OnDestroy } from '@angular/core';
import { TierList, WeaponUpgrade } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { UserSettingsService, WeaponUpgradeService, WeaponService, ItemService, TierService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Alert } from 'src/lib/darkcloud';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { Router } from '@angular/router';

@Component({
  selector: 'app-item-details-report-improve',
  templateUrl: './item-details-report-improve.component.html',
  styleUrls: ['./item-details-report-improve.component.scss'],
})
export class ItemDetailsReportImproveComponent implements OnInit, ICollectibleDetails, OnDestroy
{
  custom: Custom;
  weaponUpgrades: WeaponUpgrade[];
  weaponUpgrade: WeaponUpgrade;
  tierList: TierList[] = []
  weaponUpgradeFields = [];
  @Input() weaponId = '';
  description: string;
  timeout:any;
  itemId:string;
  constructor(
    protected _customService: CustomService,
    protected _weaponUpgradeService: WeaponUpgradeService,
    readonly userSettingsService: UserSettingsService,
    private ref: ChangeDetectorRef,
    private _tierService: TierService,
    private _weaponService : WeaponService,
    private _itemService: ItemService,
    private _router: Router
  )
  {}

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  async ngOnInit()
  {
    this._weaponUpgradeService.toFinishLoading();
    this._customService.toFinishLoading();
    this.custom = await this._customService.svcGetInstance();
    this._tierService.toFinishLoading();
    this.tierList = this._tierService.models.filter((x) => x.selectDrop === 'Weapon Rarity' && x.name !== undefined);
    
    this.itemId = this._customService.currentSelectedItem;
    if(!this.itemId)
      this.weaponUpgrades = this._weaponUpgradeService.models.filter(wu => wu.itemId === this.custom.selectedWeaponId);
    else
      this.weaponUpgrades = this._weaponUpgradeService.models.filter(wu => wu.itemId === this.itemId);
   
    if(this.weaponUpgrades.length == 0)
    {
      this.timeout = setTimeout(()=>
      {
        this.weaponUpgrades = this._weaponUpgradeService.models.filter(wu => wu.itemId === this.custom.selectedWeaponId);
        if(this.weaponUpgrades.length == 0)
        {
          for(let i = 1; i <= 20; i++)
          {
            let wu = new WeaponUpgrade(this._weaponUpgradeService.svcNextIndex(), this.custom.selectedWeaponId, i, this.userSettingsService);
            this._weaponUpgradeService.srvAdd(wu);
          }
          this._weaponUpgradeService.toSave();
          this.weaponUpgrades = this._weaponUpgradeService.models.filter(wu => wu.itemId === this.custom.selectedWeaponId);
        }
      }, 800);      
    }
    this.getAddRarityName();

    this.description = `Showing ${this.weaponUpgrades.length} results`;
    
  }

  getAddRarityName() {
  }

  async createWeaponupgradeForNewItem(weaponId)
  {
    if(!this.isItemInsideWeaponupgrades(weaponId))
    {
      for(let i = 1; i <= 20; i++)
      {
        let wu = new WeaponUpgrade(this._weaponUpgradeService.svcNextIndex(), weaponId, i, this.userSettingsService);
        this._weaponUpgradeService.srvAdd(wu);
      }
      this._weaponUpgradeService.toSave();
    }
    this.weaponUpgrades = [];
    this.weaponUpgrades = this._weaponUpgradeService.models.filter(wu => wu.itemId === weaponId);
    this.ngOnInit();
  }

  isItemInsideWeaponupgrades(itemId:string)
  {
    for(let j = 0; j < this._weaponUpgradeService.models.length; j++)
    {
      if(this._weaponUpgradeService.models[j].itemId == itemId)
      {
        return true;
      }
    }
    return false;
  }

  currentWeapon()
  {
    for(let i = 0; i < this._weaponUpgradeService.models.length; i++)
    {
      if(this._weaponUpgradeService.models[i].itemId == this.weaponId)
      {
        this.weaponUpgrade = this._weaponUpgradeService.models[i];
        break;
      }
    }
  }

  reset(character)
  {
    this.weaponId = character;
    this.itemId = character;
    this.custom.selectedWeaponId = character;
    this.createWeaponupgradeForNewItem(this.weaponId);
    this.currentWeapon();
  }
  
  async changeItemValue(weaponUpgrades: WeaponUpgrade, value: number, fieldName:string)
  {
    weaponUpgrades[fieldName] = value;
    await this._weaponUpgradeService.svcToModify(weaponUpgrades);
    await this._weaponUpgradeService.toSave();
  }

 
  async onExcelPaste(): Promise<void> {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/);

     this.weaponUpgradeFields = ['atkw', 'cooldown', 'shots', 'spdBoost', 'gold', 'goldUp', 'ichor', 'souls', 'time', 'rubies', 'titanium', 'adamantium', 'hellnium'];
  
    if (this.DisplayErrors(lines)) return;
  
    let lastTitanium = undefined;
    let lastAdamantium = undefined;
    let lastHellnium = undefined;
  
    // Primeiro loop: validação de todos os nomes na 2ª coluna
    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let rows = line.split(/\t/);
  
      if (l == 6 || l == 12 || l == 16) {
        lastTitanium = undefined;
        lastAdamantium = undefined;
        lastHellnium = undefined;
      }
  
      // Verifica se o nome na 2ª coluna (rows[1]) está presente no array this.tierList
      const nameInSecondColumn = rows[1]?.trim(); 
      if(nameInSecondColumn != undefined) {
        const isNameValid = this.tierList.find((x) => x?.name.toLowerCase().includes(nameInSecondColumn.toLowerCase()));
        
      // Se o nome não for válido, emitir erro e interromper o fluxo
      if (!isNameValid) {
        Alert.showError(`The name "${nameInSecondColumn}" in row ${l + 1} is not valid.`);
        return; // Interrompe o fluxo, nada será salvo
      }
      } else {
        continue;
      }      
  
    }
  
    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let rows = line.split(/\t/);
  
      if (l == 6 || l == 12 || l == 16) {
        lastTitanium = undefined;
        lastAdamantium = undefined;
        lastHellnium = undefined;
      }
  
      // Nome já validado, agora atribuímos ao campo rarityName
      const nameInSecondColumn = rows[1]?.trim();
      if(nameInSecondColumn != undefined) {
        this.weaponUpgrades[l].rarityName = nameInSecondColumn;      
    
        for (let i = 0; i < this.weaponUpgradeFields.length; i++) {
          if (rows[i + 2]?.trim()) {
            this.weaponUpgrades[l][this.weaponUpgradeFields[i]] = +(rows[i + 2].split('.').join("").split(',').join(""));
          } else {
            this.weaponUpgrades[l][this.weaponUpgradeFields[i]] = '';
          }
        }
      } else {
        continue;
      }
    }
  
    // Salva todas as mudanças de uma vez após o processamento
    for (let l = 0; l < this.weaponUpgrades.length; l++) {
      this._weaponUpgradeService.svcToModify(this.weaponUpgrades[l]);
    }
  
    this._weaponUpgradeService.toSave();
    Alert.ShowSuccess('Weapon Upgrades imported successfully!');
  }
  

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < this.weaponUpgradeFields.length)
    {
      Alert.showError("Copy the LEVEL column values too!");
      return true;
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout); 
  }
}
