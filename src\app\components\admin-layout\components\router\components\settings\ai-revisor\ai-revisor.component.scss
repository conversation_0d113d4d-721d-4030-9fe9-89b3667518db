.card-container.card-container 
{
    display: flex;
    flex-direction: column;
    margin-left: 30px;
    height: 1000px;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 10px;
      margin: 5px;
     // width: 60vw;
      min-width: min-content;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
      padding-top: 0!important;
    }
}

.text-card
{
    padding: 5px !important;
    padding-left: 15px !important;
    display: flex; 
    width: 100%; 
    flex-direction: column;
}

p
{
    margin: 0;
}

button
{
    background-color: transparent;
    border: none;
}

.btn-height
{
  height: 40px !important;
}

.card-send {
  position: relative; 
  width: 100%; 
  margin: auto; 
  top: -20px; z-index: 1;
}

.card-send-database {
  text-align: center; 
  font-size: 25px; 
  display: flex; 
  justify-content: center;
}

.card-send-text {
  text-decoration: underline; 
  color: #3472f7; 
  font-size: 25px;
  margin-left: 7px; 
  margin-right: 5px; 
  font-weight: 700;
}

.card-send-api {
  text-align: center; 
  font-size: 17px; 
  display: flex; 
  justify-content: center; 
  margin-left: 30px;
}

.card-send-cancel {
  position: relative; 
  float: right; 
  top: -45px;
}

#customers {
  font-family: <PERSON><PERSON>, <PERSON>lvetica, sans-serif;
  border-collapse: collapse;
  width: 55vw;
}

#customers td, #customers th {
  border: 1px solid #ddd;
  padding: 8px;
}

.h3-number {
  padding-left: 5px;
  padding-right: 10px;
}
#customers tr:nth-child(even){background-color: #f2f2f2;}

#customers tr:hover {background-color: #ddd;}

#customers th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04AA6D;
  color: white;
}

.db-itens {
    display: flex;
    align-content: center;
    flex-direction: column;
    flex-wrap: wrap;
    padding-bottom: 10px; 
    padding-top: 10px; 
    padding-left: 10px; 
    margin: 0; 
    height: 1000px; 
    display: flex; 
}
.div-menu {
  overflow-y: auto; 
  overflow-x: hidden;
  width: 280px;
}

.div-menu-span {
  font-weight: 600; 
  text-decoration: underline; 
  font-size: 20px;
}

.container-card {
  width: 60vw; 
  background-color: #f5f5f5; 
 // height: 85vmin; 
  overflow-y: auto;
  overflow-x: hidden;
}

.text-holder
{
    display: flex; 
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    justify-content: space-around;
   // width: 60vw;
}

.card-div-name {
  display: flex; 
  flex-direction: row; 
  justify-content: space-around; 
  align-items: center;
}

span {
    font-weight: 700;
}

.span-font-weight {
    font-weight: normal !important;
}

.text-to-be-revised
{
    font-style: normal !important;
    font-size: 15px !important;
    text-align: justify;
    margin: 0px;  
    padding: 5px;
}

.div-buttom {
  display: flex; 
  width: 100%; 
  justify-content: center;
  margin-top: 10px;
}

.buttomApprove {
  color: green; 
  font-weight: 600; 
  font-size: 30px;
}

.buttom-reject {
  color: red; 
  font-weight: 600; 
  font-size: 30px;
}

.div-highlight {
  display: flex; 
  flex-direction: row; 
  justify-content: space-around; 
  align-items: center;
}

.originalText
{
    font-style: normal !important;
    font-size: 15px !important;
    text-align: justify;
    width: 60vw;
    height: auto;
    border-radius: 4px;
    padding: 10px;
    padding-left: 20px;
    margin-top: 10px;
    margin-bottom: 0px;
    background-color: #f2f2f2;
    color: #333;
}

.p-sendinhg {
  font-weight: 700; 
  font-size: 25px;
}

.content-holder
{
    display: flex;
  /*  flex-direction: row;
    justify-content: space-around;
    */
    width: max-content;
    margin-left: 50px;
    margin-right: 50px;
}

.score-cards
{
    display: flex;
    width: 7vw;
    height: 50px;
    align-content: center;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.score
{
 font-size: 20px;
}

.original-text 
{
    white-space: pre-wrap;
}
  
.insert 
{
    background-color: #89ff89; /* Light green for insertions */
}
  
.delete 
{
    background-color: #fbaaaa; /* Light red for deletions */
}

li
{
    list-style: none;
}

i
{
    font-style: normal;
}

.dif-holder
{
    background-color: #dfdfdf40;
    box-shadow: 0 1px 2px #0000000d, 0 0 0 1px #3f3f441a;
    border-radius: 4px;

    padding: 10px;
    padding-left: 22px;
    margin: 10px;

    width: 48vw;
}

.dif
{
    width: auto;
    overflow: hidden;
    white-space: wrap;
    font-size: 20px;
}

.group-together
{
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: flex-start;
}

.cards-group
{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.no-contents-message
{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 20vw;
    color: grey;
}

.content-refresh {
    margin: auto;
    display: flex;
    max-height: 200vh;
    align-self: center;
    align-items: center;
  }

  .icon-refresh-text {
    font-size: 50px !important;
    animation: rotate 2s linear infinite;
  }
  
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .icon	
{	
    font-size: 30px;	
    color: #999;	
    position: relative;	
    transition: transform 0.3s;	
    transform-origin: center;	
}	
.icon:hover	
{	
    font-size: 30px;	
    color: #555;	
    transform: translate(-2.5%) rotate(180deg);	
}	

  .loading-dots {
    display: flex;
    width: 60px;
    height: 20px;
    position: relative;
    justify-self: center;
    justify-content: center;
    margin: auto;
  }
  
  .loading-dots .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #333;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    animation: move 2s linear infinite;
  }
  
  .loading-dots .dot:nth-child(1) {
    animation-delay: 0s;
  }
  
  .loading-dots .dot:nth-child(2) {
    animation-delay: 0.5s;
  }
  
  .loading-dots .dot:nth-child(3) {
    animation-delay: 1s;
  }
  
  @keyframes move {
    0% {
      left: 0;
    }
    50% {
      left: 40px;
    }
    100% {
      left: 0;
    }
  }

.div-loading {
    display: grid; 
    align-self: center; 
    margin: auto; 
    padding-top: 10px; 
    padding-bottom: 10px;
    text-align: center;
  }
  
  .bt-width {
    width: 130px;
  }

  .m-right {
    margin-right: 5px;
  }
  .btn-green-approved {
    background-color: green !important; 
  }

  .selected {
    background-color: #AFDDFF !important;
  }
  .added {
    color: green;
  }
  
  .removed {
    color: red;
    text-decoration: line-through;
  }


  .div-buttons-general {
    display: flex; 
    justify-content: center; 
    margin-top: 15px; 
    width: 100%; 
    margin-bottom: 20px;
  }

  .i-color-font {
    color: white; 
    font-size: 25px;
  }

/*Modal do sistema */

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}

.iconInter {
  text-align: center; 
  font-size: 20px !important; 
  margin-top: 1px;
  margin-left: 3px;
  position: relative;
  z-index: 12;
}
i:hover{
  color: red;
  cursor: pointer;
}

.popup-report
{ 
  border-radius: 8px;
  width: 850px;
  position: fixed;
  padding: 24px;
  top: 5%;
  transform: translate(35%, -5%);
  z-index: 1000;
  opacity: 1;  

}

.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    font-weight: 600 !important;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: white !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextInfo {
  color: white;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: white black;
  padding-top: 10px;
}

.p-text {
  color:azure !important; 
  text-align: left; 
  text-transform: none !important;
}

.span-text {
  font-weight: 700; 
  color: white;
}

.background-div {	
  position: relative;
  display: flex;
  justify-content: left;
  z-index: 9999;
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	

// FIM DO MODAL