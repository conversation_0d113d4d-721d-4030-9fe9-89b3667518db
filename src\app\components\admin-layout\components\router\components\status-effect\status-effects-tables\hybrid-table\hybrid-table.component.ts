import { Component, EventEmitter, Output } from '@angular/core';
import { HybridTable } from 'src/app/lib/@bus-tier/models/HybridTable';
import { Button } from 'src/app/lib/@pres-tier/data';
import { HybridTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-hybrid-table',
  templateUrl: './hybrid-table.component.html',
  styleUrls: ['./hybrid-table.component.scss']
})
export class HybridTableComponent {

  
  
  titles = ['ID HYBRID', 'CATEGORY', 'ID BOOST', 'ID NEGATIVE', 'ID AFFLICTION','SKILL RESIST USER','SKILL RESIST ENEMY',
    'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'DURATION (TURNS)'];
    activeLanguage = 'PTBR';
    listHybridTable: HybridTable[] = [];

    @Output() activeTab2 = new EventEmitter<string>();
    isListHybridEmpty: boolean;
   
    public readonly excelButtonTemplate: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
    constructor(
      private _hybridTableService: HybridTableService
    ){}
   
   
    async ngOnInit(): Promise<void>{
      
        this.removeEmptyItems();
         this.listHybridTable = this._hybridTableService.models;
         this.isListHybridEmpty = this.listHybridTable.length === 0;   
   
      }

      removeEmptyItems() {
        this._hybridTableService.toFinishLoading();
        this._hybridTableService.models = this._hybridTableService.models.filter(hybridItem => hybridItem.idHybrid !== "");
        this._hybridTableService.toSave();
      }
   
      async onExcelPaste() {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter(line => line);    
        const processedData: string[][] = [];
      
        if (lines.length > 0) {
          lines.forEach(line => {
            // Divide cada linha em colunas
            const values = line.split("\t").map(value => value.trim()).slice(1);      
            processedData.push(values);
          });
      
          // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
          const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
      
          if (!isColumnCountValid) {
            Alert.showError('Invalid number of columns');
            return;
          }
    
          this._hybridTableService.models = [];
          this._hybridTableService.toSave();
      
          for (let index = 0; index < processedData.length; index++) {
            this._hybridTableService.createNewHybridTable(processedData[index]);
          }    
   
          Alert.ShowSuccess('Hibrid Table imported successfully!');
          this.activeTab2.emit('hybridTable');
          this.ngOnInit();
        }
      }
      
   
      changeHybrid(rowIndex: number, name: string, newValue: string){
   
        if (name === 'idHybrid') {
         this.listHybridTable[rowIndex].idHybrid = newValue;        
        }
        else if (name === 'category') {
          this.listHybridTable[rowIndex].category = newValue;        
         }
         else if (name === 'idBoost') {
          this.listHybridTable[rowIndex].idBoost = newValue;        
         }
         else if (name === 'idNegative') {
          this.listHybridTable[rowIndex].idNegative = newValue;        
         }
         else if (name === 'idAffliction') {
          this.listHybridTable[rowIndex].idAffliction = newValue;        
         }
        else if (name === 'skillResistUser') {
            this.listHybridTable[rowIndex].skillResistUser = newValue;        
            }
          else if (name === 'skillResistEnemy') {
              this.listHybridTable[rowIndex].skillResistEnemy = newValue;        
          }    
         else if (name === 'statusEffectName') {
          this.listHybridTable[rowIndex].statusEffectName = newValue;        
         }
         else if (name === 'description') {
          this.listHybridTable[rowIndex].description = newValue;        
         }
         else if (name === 'powerPoints') {
          this.listHybridTable[rowIndex].powerPoints = newValue;        
         }       
         else if (name === 'duration') {
           this.listHybridTable[rowIndex].duration = newValue;        
          }   
        this._hybridTableService.svcToModify(this.listHybridTable[rowIndex]);
      }    
   


}
