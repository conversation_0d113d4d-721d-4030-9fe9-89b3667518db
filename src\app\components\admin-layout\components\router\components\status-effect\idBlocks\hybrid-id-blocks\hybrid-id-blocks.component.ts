import { Component, EventEmitter, Output } from '@angular/core';
import { HybridIdBlocks } from 'src/app/lib/@bus-tier/models';
import { HybridIdBlockservice } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';
import { Button } from '../../../../../../../../lib/@pres-tier/data';

@Component({
  selector: 'app-hybrid-id-blocks',
  templateUrl: './hybrid-id-blocks.component.html',
  styleUrls: ['./hybrid-id-blocks.component.scss']
})
export class HybridIdBlocksComponent {
  
  titles = ['ID', 'CATEGORY', 'ID BOOST', 'ID NEGATIVE', 'ID AFFLICTION', 'SKILL RESIST USER','SKILL RESIST ENEMY',
     'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'DURATION (TURNS)'];
  listHybrid: HybridIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();
  isListHibridEmpty: boolean;

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _hybridIdBlockservice: HybridIdBlockservice
  ){}


  async ngOnInit(): Promise<void>{
    
      this.removeEmptyItems();
      this.listHybrid = this._hybridIdBlockservice.models;
      this.isListHibridEmpty = this.listHybrid.length === 0;
 
    }

    removeEmptyItems() {
      this._hybridIdBlockservice.toFinishLoading();
      this._hybridIdBlockservice.models = this._hybridIdBlockservice.models.filter(hibrid => hibrid.idHybrid !== "");
      this._hybridIdBlockservice.toSave();     
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._hybridIdBlockservice.models = [];
        this._hybridIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._hybridIdBlockservice.createNewHybridIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Hybrid imported successfully!');
        this.activeTab2.emit('hybrid');
        this.ngOnInit();
      }
    }
    
 
    changeHybrid(rowIndex: number, name: string, newValue: string){

      if (name === 'idHybrid') {
       this.listHybrid[rowIndex].idHybrid = newValue;        
      }
      else if (name === 'category') {
        this.listHybrid[rowIndex].category = newValue;        
       }
       else if (name === 'idBoost') {
        this.listHybrid[rowIndex].idBoost = newValue;        
       }
       else if (name === 'idNegative') {
        this.listHybrid[rowIndex].idNegative = newValue;        
       }
       else if (name === 'idAffliction') {
        this.listHybrid[rowIndex].idAffliction = newValue;        
       }
       else if (name === 'skillResistUser') {
        this.listHybrid[rowIndex].skillResistUser = newValue;        
       }
       else if (name === 'skillResistEnemy') {
        this.listHybrid[rowIndex].skillResistEnemy = newValue;        
       }
       else if (name === 'statusEffectName') {
        this.listHybrid[rowIndex].statusEffectName = newValue;        
       }
       else if (name === 'description') {
        this.listHybrid[rowIndex].description = newValue;        
       }
       else if (name === 'powerPoints') {
        this.listHybrid[rowIndex].powerPoints = newValue;        
       }
       else if (name === 'duration') {
        this.listHybrid[rowIndex].duration = newValue;        
       }

      this._hybridIdBlockservice.svcToModify(this.listHybrid[rowIndex]);
    }    
 

}
