.card {
    // padding-top: 8px !important;
     padding-top: 17px !important;
     height: 70px;
   }
 
   .card-header-content {
     display: block;
     margin-left: 30px;
     margin-right: 15px;
     width: 40%;
     padding-top: 5px;
   }

   //tabela

.table-bordered {
    width: 30%;
   // border-collapse: collapse;
  }
  
  .table-bordered th, .table-bordered td {
    padding: 8px;
    text-align: center;
  }
  
  .table-bordered thead th {
    background-color: #2E2E2E;
    color: white;
  }
  
  .table-bordered thead th[colspan="3"] {
    background-color: #2E2E2E;
    color: white;
  }
  
  .table-bordered thead th[colspan="4"] {
    background-color: #555555;
    color: white;
  }

 
.gray-color-th, th {
    background-color: #595959 !important;
}

.trBC {
    background-color: #595959 !important;
    text-align: center;
    font-weight: 400 !important;
  }

.gray{
background-color:#ddd;
}

.btn-MAX {
  display: flex;
  justify-content: end;
  margin-right: 20px;
  width: 50%;
  float: right;
  margin-top: -37px;
}

