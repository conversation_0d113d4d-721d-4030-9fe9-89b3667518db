import { Injectable } from '@angular/core';
import { RoadBlockService } from './road-block.service';
import { OptionService } from './option.service';
import { DilemmaService } from './dilemma.service';
import { OptionBoxService } from './option-box.service';
import { DilemmaBoxService } from './dilemmaBox.service';

/**
 * Dialogue Tree Utility Service
 *
 * ## Key Features
 * - **Roadblock Management**: Centralized roadblock checking, counting, and evaluation
 * - **Type Detection**: Unified box type and leaf type identification
 * - **Service Consolidation**: Reduces service dependencies across components
 * - **Performance Optimization**: Efficient roadblock checking with early returns
 * - **Maintainability**: Single location for business logic changes
 *
 * ## Usage
 * Components should inject this service and use its methods instead of duplicating
 * roadblock checking, type detection, or complex calculation logic.
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class DialogueTreeUtilityService {

  constructor(
    private _roadBlockService: RoadBlockService,
    private _optionService: OptionService,
    private _dilemmaService: DilemmaService,
    private _optionBoxService: OptionBoxService,
    private _dilemmaBoxService: DilemmaBoxService
  ) {}

  /**
   * Check if a story box has roadblocks.
   * Consolidates the common pattern: filterStoryboxForAllRoadblocks + length check
   */
  hasRoadblocks(storyBoxId: string): boolean {
    if (!storyBoxId) return false;
    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(storyBoxId);
    return roadblocks.length > 0;
  }

  /**
   * Get roadblock count for a story box.
   * Consolidates the common pattern used across components.
   */
  getRoadblockCount(storyBoxId: string): number {
    if (!storyBoxId) return 0;
    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(storyBoxId);
    return roadblocks.length;
  }

  /**
   * Check if any options in a list have roadblocks on their answer boxes.
   * Consolidates duplicate logic from DialogueTreeView and DialogueTreeLayer.
   */
  checkOptionsForRoadblocks(optionIds: string[]): boolean {
    if (!optionIds || optionIds.length === 0) return false;
    
    for (const optionId of optionIds) {
      const option = this._optionService.svcFindById(optionId);
      if (option?.answerBoxId && this.hasRoadblocks(option.answerBoxId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if any dilemmas in a list have roadblocks on their answer boxes.
   * Consolidates duplicate logic from DialogueTreeView and DialogueTreeLayer.
   */
  checkDilemmasForRoadblocks(dilemmaIds: string[]): boolean {
    if (!dilemmaIds || dilemmaIds.length === 0) return false;
    
    for (const dilemmaId of dilemmaIds) {
      const dilemma = this._dilemmaService.svcFindById(dilemmaId);
      if (dilemma?.idDilemmaBox && this.hasRoadblocks(dilemma.idDilemmaBox)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Detect box type from ID prefix.
   * Consolidates the common pattern: boxId.includes('SB'/'OB'/'DIL')
   */
  detectBoxType(boxId: string): 'StoryBox' | 'OptionBox' | 'DilemmaBox' | 'Unknown' {
    if (!boxId) return 'Unknown';
    
    if (boxId.includes('SB')) return 'StoryBox';
    if (boxId.includes('OB')) return 'OptionBox';
    if (boxId.includes('DIL')) return 'DilemmaBox';
    
    return 'Unknown';
  }

  /**
   * Get maximum roadblock count from parent option/dilemma boxes.
   * Consolidates duplicate logic from DialogueTreeLayer.
   */
  getMaxParentRoadblocks(previousBoxId: string): number {
    if (!previousBoxId) return 0;
    
    let maxParentRoadblocks = 0;
    const boxType = this.detectBoxType(previousBoxId);

    if (boxType === 'OptionBox') {
      const optionBox = this._optionBoxService.svcFindById(previousBoxId);
      if (optionBox?.optionIds) {
        optionBox.optionIds.forEach(optionId => {
          const option = this._optionService.svcFindById(optionId);
          if (option?.answerBoxId) {
            const roadblockCount = this.getRoadblockCount(option.answerBoxId);
            maxParentRoadblocks = Math.max(maxParentRoadblocks, roadblockCount);
          }
        });
      }
    } else if (boxType === 'DilemmaBox') {
      const dilemmaBox = this._dilemmaBoxService.svcFindById(previousBoxId);
      if (dilemmaBox?.optionDilemmaIds) {
        dilemmaBox.optionDilemmaIds.forEach(dilemmaId => {
          const dilemma = this._dilemmaService.svcFindById(dilemmaId);
          if (dilemma?.idDilemmaBox) {
            const roadblockCount = this.getRoadblockCount(dilemma.idDilemmaBox);
            maxParentRoadblocks = Math.max(maxParentRoadblocks, roadblockCount);
          }
        });
      }
    }

    return maxParentRoadblocks;
  }

  /**
   * Check if a box ID represents an investigation box.
   * Consolidates duplicate logic from multiple components.
   */
  isInvestigationBox(boxId: string): boolean {
    if (!boxId || !boxId.includes('OB')) return false;
    
    const optionBox = this._optionBoxService.svcFindById(boxId);
    return optionBox?.type === 1; // 1 = InvestigationBox
  }

  /**
   * Check if a box ID represents an option layer (OptionBox or DilemmaBox).
   * Consolidates duplicate logic from DialogueTreeLayer.
   */
  isOptionLayer(boxId: string): boolean {
    if (!boxId) return false;
    return boxId.includes('OB') || boxId.includes('DIL');
  }

  /**
   * Get dice negative outcome IDs from option IDs.
   * Consolidates duplicate logic from DialogueTreeLayer.
   */
  collectDiceNegativeOutcomes(optionIds: string[]): string[] {
    if (!optionIds || optionIds.length === 0) return [];
    
    const diceNegativeOutcomeIds: string[] = [];
    optionIds.forEach(optionId => {
      const option = this._optionService.svcFindById(optionId);
      if (option?.answerBoxNegativeId) {
        diceNegativeOutcomeIds.push(option.answerBoxNegativeId);
      }
    });
    
    return diceNegativeOutcomeIds;
  }

  /**
   * Get option IDs from a previous layer for branch selection logic.
   * Consolidates duplicate logic from DialogueTreeLayer.
   */
  getPreviousOptionIds(previousBoxId: string): string[] {
    if (!this.isOptionLayer(previousBoxId)) {
      return [];
    }

    const boxType = this.detectBoxType(previousBoxId);

    if (boxType === 'OptionBox') {
      const optionBox = this._optionBoxService.svcFindById(previousBoxId);
      if (!optionBox) return [];

      const result: string[] = [];
      optionBox.optionIds.forEach(optionId => {
        // Add the regular option ID
        result.push(optionId);

        // Add dice negative outcome StoryBox ID if it exists
        const option = this._optionService.svcFindById(optionId);
        if (option?.answerBoxNegativeId) {
          result.push(option.answerBoxNegativeId); // Use actual dice failure StoryBox ID
        }
      });

      return result;
    }

    if (boxType === 'DilemmaBox') {
      const dilemmaBox = this._dilemmaBoxService.svcFindById(previousBoxId);
      return dilemmaBox?.optionDilemmaIds || [];
    }

    return [];
  }

  /**
   * Check if a leaf type represents a dialogue option (choice, investigation, dilemma, or dice failures).
   * Consolidates duplicate logic from DialogueTreeLeaf and other components.
   */
  isDialogueOption(leafType: string): boolean {
    return leafType === 'ChoiceOption' ||
           leafType === 'InvestigationOption' ||
           leafType === 'DilemmaOption' ||
           leafType === 'ChoiceFailure' ||
           leafType === 'InvestigationFailure' ||
           leafType === 'DiceFailure';
  }

  /**
   * Check if a leaf type represents an option type (including all option variants).
   * Consolidates duplicate logic from DialogueTreeLeaf.
   */
  isOptionType(leafType: string): boolean {
    return this.isDialogueOption(leafType);
  }

  /**
   * Check if a leaf type represents a choice option (including choice failures).
   * Consolidates duplicate type checking logic.
   */
  isChoiceOption(leafType: string): boolean {
    return leafType === 'ChoiceOption' || leafType === 'ChoiceFailure';
  }

  /**
   * Check if a leaf type represents an investigation option (including investigation failures).
   * Consolidates duplicate type checking logic.
   */
  isInvestigationOption(leafType: string): boolean {
    return leafType === 'InvestigationOption' || leafType === 'InvestigationFailure';
  }

  /**
   * Check if a leaf type represents a dilemma option.
   * Consolidates duplicate type checking logic.
   */
  isDilemmaOption(leafType: string): boolean {
    return leafType === 'DilemmaOption';
  }

  /**
   * Check if a leaf type represents a dice failure outcome.
   * Consolidates duplicate type checking logic.
   */
  isDiceFailure(leafType: string): boolean {
    return leafType === 'ChoiceFailure' ||
           leafType === 'InvestigationFailure' ||
           leafType === 'DiceFailure';
  }

  /**
   * Check if a leaf type represents a box type (StoryBox, OptionBox, DilemmaBox).
   * Consolidates duplicate type checking logic.
   */
  isBoxType(leafType: string): boolean {
    return leafType === 'StoryBox' ||
           leafType === 'ChoiceBox' ||
           leafType === 'InvestigationBox' ||
           leafType === 'DilemmaBox';
  }

  /**
   * Check if an element is a child of any option box (choice, investigation, or dilemma).
   * Consolidates duplicate logic from DialogueTreeLeaf.
   */
  isChildOfOptionBox(leafType: string): boolean {
    return leafType === 'ChoiceOption' ||
           leafType === 'InvestigationOption' ||
           leafType === 'DilemmaOption';
  }

  /**
   * Calculate roadblock information for different box types.
   * Consolidates duplicate roadblock detection logic.
   */
  calculateRoadblockInfo(boxId: string, previousBoxId?: string): {
    roadblockCount: number;
    hasRoadblocks: boolean;
    incomingBranchRoadblockCount: number;
    outgoingBranchRoadblockCount: number;
  } {
    const boxType = this.detectBoxType(boxId);
    let roadblockCount = 0;
    let hasRoadblocks = false;
    let incomingBranchRoadblockCount = 0;
    let outgoingBranchRoadblockCount = 0;

    if (boxType === 'StoryBox') {
      roadblockCount = this.getRoadblockCount(boxId);
      hasRoadblocks = roadblockCount > 0;
      incomingBranchRoadblockCount = roadblockCount;

      // If receiving connections from option/dilemma boxes, use maximum parent roadblock count
      if (previousBoxId && this.isOptionLayer(previousBoxId)) {
        const maxParentRoadblocks = this.getMaxParentRoadblocks(previousBoxId);
        incomingBranchRoadblockCount = Math.max(roadblockCount, maxParentRoadblocks);
      }

      outgoingBranchRoadblockCount = roadblockCount;
    } else if (boxType === 'OptionBox') {
      const parentRoadblocks = this.getRoadblockCount(boxId);
      let maxRoadblocks = parentRoadblocks;

      // Check roadblocks on child options for branch consistency
      const optionBox = this._optionBoxService.svcFindById(boxId);
      if (optionBox?.optionIds) {
        optionBox.optionIds.forEach(optionId => {
          const option = this._optionService.svcFindById(optionId);
          if (option?.answerBoxId) {
            const childRoadblocks = this.getRoadblockCount(option.answerBoxId);
            maxRoadblocks = Math.max(maxRoadblocks, childRoadblocks);
          }
        });
      }

      roadblockCount = maxRoadblocks;
      hasRoadblocks = roadblockCount > 0;
      incomingBranchRoadblockCount = parentRoadblocks;
      outgoingBranchRoadblockCount = maxRoadblocks;
    } else if (boxType === 'DilemmaBox') {
      const parentRoadblocks = this.getRoadblockCount(boxId);
      let maxRoadblocks = parentRoadblocks;

      // Check roadblocks on child dilemmas for branch consistency
      const dilemmaBox = this._dilemmaBoxService.svcFindById(boxId);
      if (dilemmaBox?.optionDilemmaIds) {
        dilemmaBox.optionDilemmaIds.forEach(dilemmaId => {
          const dilemma = this._dilemmaService.svcFindById(dilemmaId);
          if (dilemma?.idDilemmaBox) {
            const childRoadblocks = this.getRoadblockCount(dilemma.idDilemmaBox);
            maxRoadblocks = Math.max(maxRoadblocks, childRoadblocks);
          }
        });
      }

      roadblockCount = maxRoadblocks;
      hasRoadblocks = roadblockCount > 0;
      incomingBranchRoadblockCount = parentRoadblocks;
      outgoingBranchRoadblockCount = maxRoadblocks;
    }

    return {
      roadblockCount,
      hasRoadblocks,
      incomingBranchRoadblockCount,
      outgoingBranchRoadblockCount
    };
  }
}
