import { Injectable } from '@angular/core';

/**
 * Service for handling complex branch positioning and styling calculations in dialogue trees.
 * Manages line widths, spacing, middle branch positioning, and dynamic styling logic.
 */
@Injectable({
  providedIn: 'root'
})
export class DialogueTreeBranchCalculationService {

  constructor() {}

  /**
   * Calculate dynamic line height based on roadblock count
   */
  calculateDynamicLineHeight(hasRoadblocks: boolean, roadblockCount: number): number {
    const baseLineHeight = 40;
    const roadblockSpacing = 50;

    if (!hasRoadblocks || roadblockCount === 0) {
      return baseLineHeight;
    }

    let requiredHeight = baseLineHeight;

    if (roadblockCount === 1) {
      requiredHeight = 65;
    } else if (roadblockCount === 2) {
      requiredHeight = 85;
    } else {
      const additionalSpace = 15 + ((roadblockCount - 1) * roadblockSpacing);
      requiredHeight = Math.max(baseLineHeight, baseLineHeight + additionalSpace);
    }

    return requiredHeight;
  }

  /**
   * Calculate line widths and spacing based on roadblock configuration
   */
  calculateLineWidthsAndSpacing(
    inPaths: boolean[],
    outPaths: boolean[],
    hasRoadblocks: boolean,
    hasOptionRoadblocks: boolean,
    drawAllLinesInput: boolean,
    drawAllLinesOutput: boolean
  ): {
    lineWidthTop: number,
    lineWidthBottom: number,
    lineTopSpacing: number,
    lineBottomSpacing: number,
    usePixelMode: boolean
  } {
    const inCount = inPaths?.length || 0;
    const outCount = outPaths?.length || 0;
    const totalOptions = Math.max(inCount, outCount);

    const usePixelMode = hasOptionRoadblocks && totalOptions >= 4;
    const maxConstraint = hasOptionRoadblocks ? 75 : 50;

    let lineWidthTop = 0;
    let lineWidthBottom = 0;
    let lineTopSpacing = 0;
    let lineBottomSpacing = 0;

    if (!usePixelMode) {
      if (inPaths?.length > 0) {
        const topResult = this.calculateTopLine(inPaths, hasRoadblocks, drawAllLinesInput, maxConstraint);
        lineWidthTop = topResult.lineWidth;
        lineTopSpacing = topResult.lineSpacing;
      }

      if (outPaths?.length > 0) {
        const bottomResult = this.calculateBottomLine(outPaths, hasRoadblocks, drawAllLinesOutput, maxConstraint);
        lineWidthBottom = bottomResult.lineWidth;
        lineBottomSpacing = bottomResult.lineSpacing;
      }
    }

    return {
      lineWidthTop,
      lineWidthBottom,
      lineTopSpacing,
      lineBottomSpacing,
      usePixelMode
    };
  }

  /**
   * Calculate top line dimensions
   */
  private calculateTopLine(
    inPaths: boolean[],
    hasRoadblocks: boolean,
    drawAllLinesInput: boolean,
    maxConstraint: number
  ): { lineWidth: number, lineSpacing: number } {
    let inLength = inPaths.length;
    let firstIn = drawAllLinesInput ? 0 : inPaths.findIndex(b => b);
    let lastIn = drawAllLinesInput ? inPaths.length - 1 : inPaths.lastIndexOf(true);

    let inPart = 100 / inLength;

    // Adjust spacing for roadblocks
    if (hasRoadblocks && inLength > 1) {
      inPart = Math.min(inPart * 1.2, 100 / inLength);
    }

    let lineWidth = inPart * (lastIn - firstIn);
    let lineSpacing = inPart / 2 + (firstIn * inPart);

    if (lineSpacing > maxConstraint) {
      lineSpacing = maxConstraint;
      lineWidth = maxConstraint - inPart / 2;
    } else if (lineSpacing + lineWidth < maxConstraint) {
      // Single centered path
      if (inPaths.filter(b => b).length == 1 &&
        inPaths.length % 2 == 1 && inPaths.indexOf(true) == Math.ceil(inPaths.length / 2)) {
        lineWidth = 0;
        lineSpacing = 0;
      } else {
        lineWidth = maxConstraint - lineSpacing;
      }
    }

    // Minimum width for roadblocks
    if (hasRoadblocks && lineWidth > 0) {
      lineWidth = Math.max(lineWidth, 20);
    }

    return { lineWidth, lineSpacing };
  }

  /**
   * Calculate bottom line dimensions
   */
  private calculateBottomLine(
    outPaths: boolean[],
    hasRoadblocks: boolean,
    drawAllLinesOutput: boolean,
    maxConstraint: number
  ): { lineWidth: number, lineSpacing: number } {
    let outLength = outPaths.length;
    let firstOut = drawAllLinesOutput ? 0 : outPaths.findIndex(b => b);
    let lastOut = drawAllLinesOutput ? outPaths.length - 1 : outPaths.lastIndexOf(true);

    if (firstOut === -1 || lastOut === -1) {
      return { lineWidth: 0, lineSpacing: 0 };
    }

    let outPart = 100 / outLength;

    // Adjust spacing for roadblocks
    if (hasRoadblocks && outLength > 1) {
      outPart = Math.min(outPart * 1.2, 100 / outLength);
    }

    let lineWidth = outPart * (lastOut - firstOut);
    let lineSpacing = outPart / 2 + (firstOut * outPart);

    if (lineSpacing > maxConstraint) {
      lineSpacing = maxConstraint;
      lineWidth = maxConstraint - outPart / 2;
    } else if (lineSpacing + lineWidth < maxConstraint) {
      // Single centered path
      if (outPaths.filter(b => b).length == 1 && outPaths.length % 2 == 1 &&
         outPaths.indexOf(true) == Math.ceil(outPaths.length / 2)) {
        lineWidth = 0;
        lineSpacing = 0;
      } else {
        lineWidth = maxConstraint - lineSpacing;
      }
    }

    // Minimum width for roadblocks
    if (hasRoadblocks && lineWidth > 0) {
      lineWidth = Math.max(lineWidth, 20);
    }

    return { lineWidth, lineSpacing };
  }

  /**
   * Calculate middle branch lines: 2 options = 2 lines, 3 options = 2 lines (skip middle), 4+ = options - 1
   */
  calculateMiddleBranchLines(
    inPaths: boolean[],
    outPaths: boolean[]
  ): { position: number, width: number, marginLeft: number }[] {
    const inCount = inPaths?.length || 0;
    const outCount = outPaths?.length || 0;
    const totalOptions = Math.max(inCount, outCount);

    if (totalOptions < 2) {
      return [];
    }

    let lineCount: number;
    if (totalOptions === 2) {
      lineCount = 2;
    } else if (totalOptions === 3) {
      lineCount = 2;
    } else {
      lineCount = totalOptions - 1;
    }

    return this.generateMiddleBranchLinePositions(lineCount, totalOptions);
  }

  /**
   * Generate middle branch line positions aligned with option columns
   */
  private generateMiddleBranchLinePositions(
    lineCount: number,
    totalOptions: number
  ): { position: number, width: number, marginLeft: number }[] {
    const lines: { position: number, width: number, marginLeft: number }[] = [];
    const columnWidth = 100 / totalOptions;
    const lineWidth = 20;

    if (totalOptions === 2) {
      lines.push(
        { position: 0, width: lineWidth, marginLeft: (columnWidth / 2) - (lineWidth / 2) },
        { position: 1, width: lineWidth, marginLeft: (columnWidth * 1.5) - (lineWidth / 2) }
      );
    } else if (totalOptions === 3) {
      lines.push(
        { position: 0, width: lineWidth, marginLeft: (columnWidth / 2) - (lineWidth / 2) },
        { position: 1, width: lineWidth, marginLeft: (columnWidth * 2.5) - (lineWidth / 2) }
      );
    } else {
      for (let i = 0; i < lineCount; i++) {
        const columnCenter = (columnWidth * (i + 0.5));
        lines.push({
          position: i,
          width: lineWidth,
          marginLeft: columnCenter - (lineWidth / 2)
        });
      }
    }

    return lines;
  }

  /**
   * Calculate path from edge option to center for roadblock scenarios
   */
  calculatePathToCenterRoadblocks(selectedOptionIndex: number): number[] {
    const centerIndex = 1; // Always element 1 for roadblocks
    const pathBranches: number[] = [];

    if (selectedOptionIndex < centerIndex) {
      for (let i = selectedOptionIndex; i < centerIndex; i++) {
        pathBranches.push(i);
      }
    } else if (selectedOptionIndex > centerIndex) {
      for (let i = centerIndex; i < selectedOptionIndex; i++) {
        pathBranches.push(i);
      }
    }

    return pathBranches;
  }

  /**
   * Calculate path from edge option to center for non-roadblock scenarios
   */
  calculatePathToCenter(selectedOptionIndex: number, totalOptions: number): number[] {
    const centerIndex = Math.floor(totalOptions / 2);
    const pathBranches: number[] = [];

    if (selectedOptionIndex < centerIndex) {
      for (let i = selectedOptionIndex; i < centerIndex; i++) {
        pathBranches.push(i);
      }
    } else if (selectedOptionIndex > centerIndex) {
      for (let i = centerIndex; i < selectedOptionIndex; i++) {
        pathBranches.push(i);
      }
    }

    return pathBranches;
  }

  /**
   * Calculate flex branch width for 4+ options without roadblocks
   */
  calculateFlexBranchWidth(totalOptions: number): number {
    // Calculate margin values that decrease irregularly as options increase
    const marginValues: { [key: number]: number } = {
      4: 304,
      5: 243, // or 242
      6: 202,
      7: 173,
      8: 151,
      9: 134,
      10: 120,
      11: 109,
      12: 100
    };

    const margin = marginValues[totalOptions] || 100;
    // Approximate width calculation based on margin values
    return margin * 2; // This is an approximation, adjust as needed
  }

  /**
   * Get margin values for flex branches with 4+ options and no roadblocks
   */
  getFlexBranchMargins(totalOptions: number): { left: number, right: number } {
    const marginValues: { [key: number]: { left: number, right: number } } = {
      4: { left: 304, right: 304 },
      5: { left: 243, right: 242 },
      6: { left: 202, right: 202 },
      7: { left: 173, right: 173 },
      8: { left: 151, right: 151 },
      9: { left: 134, right: 134 },
      10: { left: 120, right: 120 },
      11: { left: 109, right: 109 },
      12: { left: 100, right: 100 }
    };

    return marginValues[totalOptions] || { left: 100, right: 100 };
  }
}
