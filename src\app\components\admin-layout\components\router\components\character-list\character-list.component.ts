import { Component, OnDestroy } from '@angular/core';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Area, Character, Class } from 'src/app/lib/@bus-tier/models';
import { Alert } from 'src/lib/darkcloud';
import { ClassService } from 'src/app/services/class.service';
import { ReviewService } from 'src/app/services/review.service';
import { Index, comparable } from 'src/lib/others';
import { CharacterService } from 'src/app/services/character.service';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { AreaService } from 'src/app/services/area.service';
import { PopupService } from 'src/app/services/popup.service';
import { Popup } from 'src/lib/darkcloud';
import * as XLSX from 'xlsx';
import { CharacterType, GameTypes, Gender} from 'src/lib/darkcloud/dialogue-system';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { Button } from 'src/app/lib/@pres-tier/data';
import { TranslationService } from 'src/app/services/translation.service';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { CharactersSelectorService } from 'src/app/services/characters-selector.service';
import { AnimationsSelectorService, BattleUpgradeService, LevelService, MinionStatsService, PrimalModifierService, SpeechService, StatusInfoService } from 'src/app/services';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { fadeIn, popup } from '../bound-item-list/bound-list.component.animations';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';



@Component({
  selector: 'app-character-list',
  templateUrl: './character-list.component.html',
  styleUrls: ['./character-list.component.scss'],
  animations: [fadeIn, popup]
})

export class CharacterListComponent extends TranslatableListComponent<Character> implements OnDestroy
{
  public readonly exportExcelButtonTemplate: Button.Templateable = 
  {
    btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
    title: 'Export to Excel',
    onClick: this.downloadAsExcel.bind(this),
    iconClass: 'pe-7s-cloud-download',
  };

  public readonly addNewCharacter: Button.Templateable = 
  {
    btnClass: [...Button.Klasses.FILL_GREEN, 'Excel'],
    title: 'Create a new Character',
    onClick: this.toAddCharacter.bind(this),
    iconClass: 'pe-7s-plus',
  };

  public classNames: Index<string>;
  public preloadedAreas: Area[];
  public language: language = 'PT-BR';
  listCharactres: Character[];
  public listBattle: any;
  public characterIndex: Character; 
  public isBattleTotal: Boolean = false;
  public isBattleArea: Boolean = false;
  public isSpeaker: Boolean = false;
  public loadingSpinner: Boolean = false;
  public isClassName: Boolean = false;
  public timeout:any;
  public sortProperty: string = 'id';
  public sortOrder = 1;

  constructor(
    private _charactersSelectorService: CharactersSelectorService,
    private _reviewService: ReviewService,
    private _areaService: AreaService,
    private _classService: ClassService,
    private _characterService: CharacterService,
    private _popupService: PopupService,
    private _levelService: LevelService,
    _activatedRoute: ActivatedRoute,
    private _speechService: SpeechService,
    private _battleUpgradeService: BattleUpgradeService,
    _userSettingsService: UserSettingsService,
    private _statusInfoService: StatusInfoService,
    private _roadblockService: RoadBlockService,
    private _primalModifierService: PrimalModifierService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _animationSelectorService: AnimationsSelectorService,
    private _minionStatsService: MinionStatsService,
    private spinnerService: SpinnerService,
  ) 
  {
    super(_characterService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }
  protected override lstFilterParameters: EasyMVC.Filter[] = [
    { name: 'areaId' },
    {
      name: 'type',
      isNumber: true,
      allowUndefined: true,
    },
  ];

  public characterGroups = [
    'ALL',
    CharacterType.UNDEFINED,
    CharacterType.BOSS,
    CharacterType.SUBBOSS,
    CharacterType.MINION,
    CharacterType.NPC,
    CharacterType.SECONDARY,
  ];


  override lstInit() 
  {
    this._characterService.toFinishLoading();
   // this.inicializedFieldsReviewed();
    this.preloadedAreas = this._areaService.models;   
  }  

  inicializedFieldsReviewed() {
    this._characterService.models.forEach((character) => {
      character.isReviewedName = !!character.isReviewedName;
      character.isReviewedTitle = !!character.isReviewedTitle;
      character.isReviewedDescription = !!character.isReviewedDescription;
      this._characterService.svcToModify(character);
    });
  }
  
  getListCharacters(characterIds: string[]): Character[] 
  {
    return this._characterService.svcCloneByIds(characterIds);
  }

//modal
   onModalClick(character: Character, type: string): void {  

    this.loadingSpinner = true;
      this.timeout = setTimeout(() => {  
        this.loadingSpinner = false
   },1000);  

   this.timeout = setTimeout(() => {  
    if(type.toString() === 'total') {
      this.characterIndex = character;
      this.isBattleTotal = true;
      this.isBattleArea = false;
      this.isSpeaker = false;
    } else if(type.toString() === 'area') {
      this.characterIndex = character;
      this.isBattleArea = true;
      this.isBattleTotal = false;
      this.isSpeaker = false;
    } else {
      this.characterIndex = character;
      this.isSpeaker = true;
      this.isBattleTotal = false;
      this.isBattleArea = false;
    } 
    },400);
  }

  closeAreaStatsPopup()
  {
    if( this.isBattleTotal) {     
      this.isBattleTotal = false;
    } if(this.isBattleArea) {  
      this.isBattleArea = false;
    } else {     
      this.isSpeaker = false;
    } 
  }
  // fim do modal

  override lstAfterInitLoadFilters() 
  {
    const characterId = this._activatedRoute.snapshot.fragment;
    if (characterId) 
    {
      const character = this._characterService.svcFindById(characterId);
      if (!character) return;
      
      const group = character.type;
      this.lstFilterValue['type'] = group ? +group : group;
      this.lstSaveFilters();
      this.HighlightElement(characterId, 300, true, '#e9d1ff', true);
    }
      this.listCharactres = this.getListCharacters(this.lstIds);   

  }

  protected override filterItem(character: Character) 
  {
    return ((this.lstFilterValue['areaId'] as string) === 'ALL' || character.areaId === (this.lstFilterValue['areaId'] as string));
  }

  override lstAfterInitFetchList() 
  {
    this.classNames = {};
    this._classService.models.forEach((klass) => 
    {
      this.classNames[klass.id] = klass.name;
    });
  }

  protected override specialSort(parameter: Sorting.Parameter) 
  {
  
    switch (parameter) 
    {
      case 'assignedAsSpeaker':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? this._reviewService.reviewResults[a.id].asSpeaker.length <
              this._reviewService.reviewResults[b.id].asSpeaker.length ? -1 : 1
            : this._reviewService.reviewResults[b.id].asSpeaker.length > 
            this._reviewService.reviewResults[a.id].asSpeaker.length ? 1  : -1
        );
        if (this.srtLstOrder === 'ascending') 
        {
          this._modelService.models.sort((a, b) => 
          {
            const ca = !this._reviewService.reviewResults[a.id].isBattleCharacter;
            return ca && this._reviewService.reviewResults[a.id].asSpeaker.length === 0 ? -1 : 1;
          });
        }
        break;
      case 'assignedAsBattleCharacter':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? this._reviewService.reviewResults[a.id].asBattleCharacterCount <
              this._reviewService.reviewResults[b.id].asBattleCharacterCount ? -1 : 1
            : this._reviewService.reviewResults[b.id].asBattleCharacterCount >
              this._reviewService.reviewResults[a.id].asBattleCharacterCount ? 1 : -1);
        if (this.srtLstOrder === 'ascending') 
        {
          this._modelService.models.sort((a, b) => 
          {
            const ca = this._reviewService.reviewResults[a.id].isBattleCharacter;
            return !ca && this._reviewService.reviewResults[a.id].asBattleCharacterCount ? -1 : 
                    ca && this._reviewService.reviewResults[a.id].asBattleCharacterCount === 0 ? -1 : 1;
          });
        }
        break;
      case 'assignedAsBattleCharacterOnAssignedArea':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? this._reviewService.reviewResults[a.id]
                .asBattleCharacterOnAssignedAreaCount > this._reviewService.reviewResults[b.id]
                .asBattleCharacterOnAssignedAreaCount ? -1 : 1 : this._reviewService.reviewResults[b.id]
                .asBattleCharacterOnAssignedAreaCount < this._reviewService.reviewResults[a.id]
                .asBattleCharacterOnAssignedAreaCount ? -1 : 1);
        if (this.srtLstOrder === 'descending') 
        {
          this._modelService.models.sort((a, b) => 
          {
            const ca = this._reviewService.reviewResults[a.id].isBattleCharacter;
            return !ca && this._reviewService.reviewResults[a.id].asBattleCharacterOnAssignedAreaCount ? -1 : ca &&
                this._reviewService.reviewResults[a.id].asBattleCharacterOnAssignedAreaCount === 0 ? -1 : 1;
          });
        }
        break;
      case 'class':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? comparable(this.classNames[a.classId]) <
              comparable(this.classNames[b.classId]) ? -1 : 1 : 
              comparable(this.classNames[a.classId]) >
              comparable(this.classNames[b.classId]) ? 1 : -1);
        break;
     /* case 'areaId':
        this._modelService.models.sort((a, b) => {
          console.log("Id - Area A: "+ a.classId + "- valor de A: ", this.classNames[a.classId]);
          console.log("Id - Area B: "+ b.classId + "- valor de B: ", this.classNames[b.classId]);
          const valueA = this.classNames[a.classId] !== undefined ? this.classNames[a.classId] : '';
          const valueB = this.classNames[b.classId] !== undefined ? this.classNames[b.classId] : ''; 
          console.log("Valor A: "+ valueA + " - valor de B: ", valueB);
 
          if (valueA < valueB) {
            return this.srtLstOrder === 'ascending' ? -1 : 1;
          } else if (valueA > valueB) {
            return this.srtLstOrder === 'descending' ? 1 : -1;
          } else {
            return 0;
          }
          /*
          if (valueA !== undefined || valueB !== undefined) {
            if(valueA < valueB) {
              return this.srtLstOrder === 'ascending' ? -1 : 1;
            } else if (valueA > valueB) {
              return this.srtLstOrder === 'descending' ? 1 : -1;
            } 
          } else {
            return 0;
          }
         */
        /*  if (this.srtLstOrder === 'ascending') {
            if( valueA === undefined || valueB === undefined) {
              return 0;
            } else {
              return valueA < valueB ? -1 : 
                     valueA > valueB ? 1 : -1;
            }    
           
          } else {
              return valueA > valueB ? -1 : 
                     valueA < valueB ? 1 : -1;
          }
          */
     // });
      
        case 'areaId':
         //Teste B
        /*this._modelService.models.sort((a, b) =>           
            this.srtLstOrder === 'ascending' ? this.classNames[a.classId] > this.classNames[b.classId] ? -1 : 1 :
            this.classNames[b.classId] < this.classNames[a.classId] ? -1 : 1);

            if(this.srtLstOrder === 'descending') {
              this._modelService.models.sort((a, b) => {
                return this.classNames[a.classId] > this.classNames[b.classId] ? 1 :
                this.classNames[b.classId] < this.classNames[a.classId] ? 1 : -1;
              });         
              
    
            }

            */
            this._modelService.models.sort((a, b) => {
              const valueA = comparable(this.classNames[a.classId]);
              const valueB = comparable(this.classNames[b.classId]);   

              // Check for undefined values
              if ((valueA === undefined || valueA === 'undefined' || valueA === 'Undefined') && (valueB === undefined || valueB === 'undefined' || valueB === 'Undefined')) {
                  return 0; // Both are undefined, consider them equal                  
              }
              if (valueA === undefined || valueA === 'undefined' || valueA === 'Undefined') {    
                  return 1; // Put undefined values at the end
              }
              if (valueB === undefined || valueB === 'undefined'|| valueB === 'Undefined' ) {     
                  return -1; // Put undefined values at the end
              }

              if (this.srtLstOrder === 'ascending') {
                  if ((valueA !== undefined || valueB !== undefined) && valueA < valueB) {
                      return -1;
                  }
                  if ((valueA !== undefined || valueB !== undefined) && valueA > valueB) {
                      return 1;
                  }
                  return 0; // A e B são equivalentes
              } else {
                  if ((valueA !== undefined || valueB !== undefined) && valueA > valueB) {               
                      return -1;
                  }
                  if ((valueA !== undefined || valueB !== undefined) && valueA < valueB) {                  
                      return 1;
                  }
               
                  return 0; // A e B são equivalentes
              }
          });


        break;
      
    /*  case 'areaId':
        this._modelService.models.sort((a, b) =>             

            this.srtLstOrder === 'ascending' ? 
             this.classNames[a.areaId] < this.classNames[b.areaId] ? -1 : 1
            : this.classNames[b.areaId] > this.classNames[a.areaId] ? 1: -1
            */

            /*if (this.srtLstOrder === 'ascending') {                
                if (this.classNames[a.classId] === this.classNames[b.classId]) return 1;
                if (comparable(this.classNames[a.classId]) !== comparable(this.classNames[b.classId])) return 1;
                return 0;
            } else {
                if (comparable(this.classNames[a.classId]) !== comparable(this.classNames[b.classId])) return -1;
                if (comparable(this.classNames[a.classId]) === comparable(this.classNames[b.classId])) return 1;
                return 0;
            }
            */

            /*
            if (this.srtLstOrder === 'ascending') {
            if(comparable(this.classNames[a.classId]) === comparable(this.classNames[b.classId]) || 
              (comparable(this.classNames[a.classId]) === 'Undefined' || comparable((this.classNames[b.classId]) === 'Undefined' ))) {
              postIndex = -1;
            } {
              postIndex = 1;
            }
          } else {
            if(comparable((this.classNames[a.classId]) !== comparable(this.classNames[b.classId]) || 
              (comparable(this.classNames[a.classId]) !== 'Undefined' || comparable(this.classNames[b.classId]) !== 'Undefined' ))) {
              postIndex = 1;
            } {
              postIndex = -1;
            }
          }
            return postIndex;
            */
      //  );
        break;      
      case 'type':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? 
              comparable(GameTypes.characterTypeName[a[parameter]]) <
              comparable(GameTypes.characterTypeName[b[parameter]]) ? 1 : -1 : 
              comparable(GameTypes.characterTypeName[a[parameter]]) > 
              comparable(GameTypes.characterTypeName[b[parameter]]) ? 1 : -1);
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  //Metodo teste
  sortBy() {
    this._modelService.models.sort((a: any, b: any) => { 
    const valueA = this.classNames[a.classId] !== undefined ? this.classNames[a.classId] : '';
    const valueB = this.classNames[b.classId] !== undefined ? this.classNames[b.classId] : ''; 


    if (valueA < valueB) {
      return this.srtLstOrder === 'ascending' ? -1 : 1;
    } else if (valueA > valueB) {
      return this.srtLstOrder === 'descending' ? 1 : -1;
    } else {
      return 0;
    }
  });
}

getCharactername(classId: any) {

  const valueClass = this._classService.svcFindById(classId);
  return valueClass ? valueClass.name : ''; 
}

  public async toPromptSelectCharacterGender(character: Character) 
  {
    const selectedGenderButton = await this._popupService.fire<Gender, Gender>(
      new Popup.Interface(
        {
          title: 'Select Gender',
          actionsClass: 'column',
        },
        Popup.genderButtons,
        {
          hideButton: { value: character.gender },
        }
      )
    );

    if (!selectedGenderButton) return;
    
    character.gender = selectedGenderButton.value;
    await this._characterService.svcToModify(character);
  }

  public async toPromptChangeCharacterType(character: Character) 
  {
    const selectedCharacterTypeButton = await this._popupService.fire<CharacterType,CharacterType>(
      new Popup.Interface(
        {
          buttonSize: 'lg',
          title: 'Select Character Group',
          actionsClass: 'column',
        },
        Popup.characterButtons,
        {
          hideButton: { value: character.type },
        }
      )
    );
    if (!selectedCharacterTypeButton) return;
    
    const type = selectedCharacterTypeButton.value;  
    
    if(type == 3) {     
      character.isCollectible = true;   
    } else {
      character.isCollectible = false;
    }
    
    this._characterService.ChangeType(character, type);
    this.lstFetchLists(true);
  }

  public async toPromptSelectCharacterClass(character: Character) 
  {
    const selectedClassButton = await this._popupService.fire<Class, Class>(
      new Popup.Interface(
        {
          title: 'Select Class',
          actionsClass: 'column',
        },
        Popup.toButtonList(this._classService.models, 'name',
        { 
          undefinedTitle: 'Undefined' 
        }),
        {
          hideButton: 
          {
            value: this._classService.svcFindById(character.classId),           
          }
        }
      )
    );

    if (!selectedClassButton) return;    

    character.classId = selectedClassButton.value?.id;
    this.changeMinionStatsClass(character, character.classId);
    await this._characterService.svcToModify(character);    
  }

  async changeMinionStatsClass(character: Character, newClass:string)
  {
    for(let i = 0; i < this._minionStatsService.models.length; i++)
    {
      if(this._minionStatsService.models[i].name == character.name)
      {
        this._minionStatsService.models[i].klass = newClass;
        await this._minionStatsService.svcToModify(this._minionStatsService.models[i]);
        await this._minionStatsService.toSave();
        break;
      }
    }
  }

  public async toPromptSelectCharacterBindArea(character: Character) 
  {
    const selectedClassButton = await this._popupService.fire<Area, Area>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { 
            undefinedTitle: 'Undefined' 
          }
        ),
        {
          hideButton: 
          {
            value: this._areaService.svcFindById(character.areaId),
          },
        }
      )
    );

    if (!selectedClassButton) return;    

    for(let i = 0; i < this._charactersSelectorService.models.length; i++)
    {
      if(this._charactersSelectorService.models[i].character == character.name)
      {  
        this._charactersSelectorService.models[i].characterArea = selectedClassButton.value?.hard.id;
        await this._charactersSelectorService.svcToModify(this._charactersSelectorService.models[i])
        await this._charactersSelectorService.toSave();
        break;
      }
    }

    character.areaId = selectedClassButton.value?.id;
    this.changeCharacterSelectionWhenEditingArea(character, character.areaId);
    await this._characterService.svcToModify(character);
  }


  public override async lstPromptRemove(character: Character) 
  {
    // removes from the database
    const removed = await this._modelService.toPromptRemove(character);
    if (!removed) return;
        
    for(let i = 0; i < this._charactersSelectorService.models.length; i++)
    {
      if(this._charactersSelectorService.models[i].character == character.name)
      {
        await this._charactersSelectorService.svcToRemove(this._charactersSelectorService.models[i].id);
        break;
      }
    }

    this.removeCharacterFromMinionstats(character);
    this.removeCharacterFromLevelSpeakersid(character);
    this.removeCharacterFromSpeechSpeakerid(character);
    this.removeCharacterStatusinfoCharacter(character);
    this.removeCharacterFromBattleupgradeCharacter(character);
    this.removeCharacterFromPrimalModifierCharacter(character);
    this.removeCharacterFromRoadblock(character);
    this.removeCharacterFromAnimations(character);
    this.lstResetHighlights();
    this.lstRemove(character);
  }

  removeCharacterFromMinionstats(character: Character)
  {
    for(let i = 0; i < this._minionStatsService.models.length; i++)
    {
      if(character.name == this._minionStatsService.models[i].name)
      {
        this._minionStatsService.svcToModify(this._minionStatsService.models[i]);
        this._minionStatsService.toSave();
        break;
      }
    }
  }

  removeCharacterFromLevelSpeakersid(character: Character)
  {
    for(let i = 0; i < this._levelService.models.length; i++)
    {
      for(let j = 0; j < this._levelService.models[i].speakerIds.length; j++)
      {
        if(this._levelService.models[i].speakerIds[j] == character.id)
        {
          this._levelService.models[i].speakerIds.splice(j, 1);
          this._levelService.svcToModify(this._levelService.models[i]);
          break;
        }
      }
    }
  }

  removeCharacterFromSpeechSpeakerid(character: Character)
  {
    for(let i = 0; i < this._speechService.models.length; i++)
    {
      if(this._speechService.models[i].speakerId == character.id)
      {
        this._speechService.models[i].speakerId = '';
        this._speechService.svcToModify(this._speechService.models[i]);
      }
    }
  }

  removeCharacterFromPrimalModifierCharacter(character:Character)
  {
    for(let i = 0; i < this._primalModifierService.models.length; i++)
    {
      if(this._primalModifierService.models[i].character == character.id)
      {
        this._primalModifierService.models[i].character = '';
        this._primalModifierService.svcToModify(this._primalModifierService.models[i]);
      }
    }
  }

  removeCharacterFromBattleupgradeCharacter(character:Character)
  {
    for(let i = 0; i < this._battleUpgradeService.models.length; i++)
    {
      if(this._battleUpgradeService.models[i].character == character.id)
      {
        this._battleUpgradeService.models[i].character = '';
        this._battleUpgradeService.svcToModify(this._battleUpgradeService.models[i]);
      }
    }
  }

  removeCharacterStatusinfoCharacter(character:Character)
  {
    for(let i = 0; i < this._statusInfoService.models.length; i++)
    {
      if(this._statusInfoService.models[i].character == character.id)
      {
        this._statusInfoService.models[i].character = '';
        this._statusInfoService.svcToModify(this._statusInfoService.models[i]);
      }
    }
  }

  removeCharacterFromRoadblock(character: Character)
  {
    for(let i = 0; i < this._roadblockService.models.length; i++)
    {
      if(this._roadblockService.models[i].characterId == character.id)
      {
        this._roadblockService.models[i].characterId = '';
        this._roadblockService.svcToModify(this._roadblockService.models[i]);
      }
    }
  }

  async removeCharacterFromAnimations(character:Character)
  {
    for(let i = 0; i < this._animationSelectorService.models.length; i++)
    {
      if(this._animationSelectorService.models[i].character == character.name)
      {
        await this._animationSelectorService.svcToRemove(this._animationSelectorService.models[i].id);
        break;
      }
    }
  }

  public async toAddCharacter() 
  {
    try 
    {
      const obj = await this._characterService.svcPromptCreateNew(
        this.lstFilterValue['type'] === 'ALL' ? CharacterType.UNDEFINED : (this.lstFilterValue['type'] as number));
      if (!obj) return;
      
      let char = this._charactersSelectorService.createNewCharactersSelector()
      char.character = obj.name
      char.concept = false;
      char.separation = false;
      char.preFinish = false;
      char.finish = false;
      char.characterArea = undefined;

      await this.lstToAdd(obj);

      this.lstResetHighlights();
      this.HighlightElement(obj.id, 100, true);
    } 
    catch (error) 
    {
      Alert.showError(error);
    }
  }

  public downloadAsExcel() 
  {
    const characterTable = document.createElement('table');
    const tHead = characterTable.createTHead();
    const tHeadRow = tHead.insertRow();
    tHeadRow.insertCell().innerText = 'ID';
    tHeadRow.insertCell().innerText = 'Collectible';
    tHeadRow.insertCell().innerText = 'Battle Total';
    tHeadRow.insertCell().innerText = 'Battle Area';
    tHeadRow.insertCell().innerText = 'Speaker';
    tHeadRow.insertCell().innerText = 'Name';
    tHeadRow.insertCell().innerText = 'Title';
    tHeadRow.insertCell().innerText = 'Birthyear';
    tHeadRow.insertCell().innerText = 'Age';
    tHeadRow.insertCell().innerText = 'Height';
    tHeadRow.insertCell().innerText = 'Gender';
    tHeadRow.insertCell().innerText = 'Class';
    tHeadRow.insertCell().innerText = 'Area';
    tHeadRow.insertCell().innerText = 'Group';
    const tBody = characterTable.createTBody();
    this._characterService.svcCloneByIds(this.lstIds)
    .filter(char => this.lstFilterValue['type'] === 'ALL' || +char.type === this.lstFilterValue['type'] || 
    char.type === this.lstFilterValue['type'])
    .forEach((character) => 
    {
      const tBodyRow = tBody.insertRow();
      tBodyRow.insertCell().innerText = character.id;
      tBodyRow.insertCell().innerText = character.isCollectible ? 'X' : '';
      tBodyRow.insertCell().innerText =
        this._reviewService.reviewResults[character.id].asBattleCharacterCount +
        '';
        tBodyRow.insertCell().innerText =
        this._reviewService.reviewResults[character.id].asBattleCharacterOnAssignedAreaCount +
        '';
      tBodyRow.insertCell().innerText =
        this._reviewService.reviewResults[character.id].asSpeaker.length + '';
      tBodyRow.insertCell().innerText = character.name || '';
      tBodyRow.insertCell().innerText = character.title || '';
      tBodyRow.insertCell().innerText = character.birthyear || '';
      const birthYear =
        new Date().getFullYear() - parseInt(character.birthyear, 10);
      tBodyRow.insertCell().innerText =
        birthYear || birthYear === 0 ? birthYear + '' : '';
      tBodyRow.insertCell().innerText = character.height || '';
      tBodyRow.insertCell().innerText =
        GameTypes.genderName[+character.gender] || '';
      tBodyRow.insertCell().innerText =
        this._classService.svcFindById(character.classId)?.name || '';
        tBodyRow.insertCell().innerText = 
        this._areaService.svcFindById(character.areaId)?.hierarchyCode || '';
      tBodyRow.insertCell().innerText =
        GameTypes.characterTypeName[+character.type] || '';
    });

    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(characterTable);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Character List');

    const now = new Date();
    XLSX.writeFile(wb, now.toLocaleDateString() + '_' + now.toLocaleTimeString() + ' DSAdmin Character List.xlsx');
  }

  public downloadCharacterOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  isCharacterTranslated(character: Character)
  {
    return this._translationService.checkTranslation(character.id, 'EN-US');
  }

  async changeCharacterSelectionWhenEditingName(character: Character, newName : string)
  {
    let characterName: string = character.name;
    for(let i = 0; i < this._charactersSelectorService.models.length; i++)
    {
      if(this._charactersSelectorService.models[i].character == characterName)
      {
        this._charactersSelectorService.models[i].character = newName;
        await this._charactersSelectorService.svcToModify(this._charactersSelectorService.models[i]);
        await this._charactersSelectorService.toSave();
        break;
      }
    }
    //change character animation
    for(let i = 0; i < this._animationSelectorService.models.length; i++)
    {
      if(this._animationSelectorService.models[i].character == characterName)
      {
        this._animationSelectorService.models[i].character = newName;
        await this._animationSelectorService.svcToModify(this._animationSelectorService.models[i]);
        await this._animationSelectorService.toSave();
        break;
      }
    }
    
    //change minion stats name
    for(let i = 0; i < this._minionStatsService.models.length; i++)
    {
      if(characterName == this._minionStatsService.models[i].name)
      {
        this._minionStatsService.models[i].name = newName;
        await this._minionStatsService.svcToModify(this._minionStatsService.models[i]);
        await this._animationSelectorService.toSave();
        break;
      }
    }
  }

  async changeCharacterSelectionWhenEditingArea(character: Character, newName : string)
  {
    for(let i = 0; i < this._charactersSelectorService.models.length; i++)
    {
      if(this._charactersSelectorService.models[i].character == character.name)
      {
        this._charactersSelectorService.models[i].characterArea = newName;
        await this._charactersSelectorService.svcToModify(this._charactersSelectorService.models[i]);
        await this._charactersSelectorService.toSave();
        break;
      }
    }
    //change minion stats area
    for(let i = 0; i < this._minionStatsService.models.length; i++)
    {
      if(character.areaId == this._minionStatsService.models[i].area)
      {
        this._minionStatsService.models[i].area == character.areaId;
        await this._minionStatsService.svcToModify(this._minionStatsService.models[i]);
        await this._minionStatsService.toSave();
        break;
      }
    }
  }

  changeName(character: Character, fieldName, value:string) {
    character.isReviewedName = false;
    character.revisionCounterNameAI = 0;
    this.lstOnChange(character, fieldName, value);
  }
  
  changeTitle(character: Character, fieldTitle, value:string) {
    character.isReviewedTitle = false;
    character.revisionCounterTitle = 0;
    this.lstOnChange(character, fieldTitle, value);
  }

  changeDescription(character: Character, fieldDescription, value:string) {
    character.isReviewedDescription = false;
    character.revisionCounterDescriptionAI = 0;
    this.lstOnChange(character, fieldDescription, value);
  }

  ngOnDestroy(): void {
    clearImmediate(this.timeout);
  }
}
