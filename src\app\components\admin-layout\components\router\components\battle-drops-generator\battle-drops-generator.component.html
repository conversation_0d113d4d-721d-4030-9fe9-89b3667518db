<div class="card list-header"
  style="height: 70px; margin-right: 30px; margin-left: 30px;margin-top: 10px; margin-bottom: 0px;">
  <div class="header" style="position: fixed; z-index: 9999; display: flex; width: 88%; justify-content: space-between;">
    <div style="width: 670px;">
      <button class="{{activeTab === 'necromodsDrops' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('necromodsDrops')">
        SLOT ≠ 0 (NECROMOD DROPS)
      </button>
      <button class="{{activeTab === 'slot0' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('slot0')">
        SLOT = 0
      </button>
      <button class="{{activeTab === 'otherDrops' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('otherDrops')">
        Other Drops
      </button>
    </div>
    <div>
      <button class="{{activeTab === 'reportsHCExcel' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('reportsHCExcel')">
        REPORTS
      </button>
    </div>

  </div>
</div>

<div *ngIf="activeTab === 'necromodsDrops'">

  <div class="card list-header" style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header">
      <button class="{{activeTab2 === 'codeBlockDrops' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('codeBlockDrops')">
        Code Block (Drop Probability)
      </button>
      <button class="{{activeTab2 === 'guaranteedDrop' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('guaranteedDrop')">
        Code Block (Garanteed Drop)
      </button>
      <button style="position:relative; float:right"
        class="{{activeTab2 === 'varianceB' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('varianceB')">
        Area Drops (Variance B)
      </button>
      <button style="position:relative; float:right"
        class="{{activeTab2 === 'varianceA' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('varianceA')">
        Area Drops (Variance A)
      </button>
      <button style="position:relative; float:right"
        class="{{activeTab2 === 'drops' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('drops')">
        Area Drops
      </button>
    </div>

    <div *ngIf="activeTab2 === 'codeBlockDrops'">
      <br />
      <app-code-block-drops-generator [probability]="true">
      </app-code-block-drops-generator>
    </div>

    <div *ngIf="activeTab2 === 'guaranteedDrop'">
      <br />
      <app-code-block-drops-generator [probability]="false">
      </app-code-block-drops-generator>
    </div>

    <div *ngIf="activeTab2 === 'drops'">
      <br />
      <app-area-drops-generator [slot0]="false" [isAreaDrops]="isSelectAreaDrop"></app-area-drops-generator>
    </div>

    <div *ngIf="activeTab2 === 'varianceA'">
      <br />
      <app-drop-varianceA [slot0]="false"></app-drop-varianceA>
    </div>

    <div *ngIf="activeTab2 === 'varianceB'">
      <br />
      <app-drop-varianceB [slot0]="false"></app-drop-varianceB>
    </div>
  </div>
</div>

<div *ngIf="activeTab === 'slot0'">
  <div class="card list-header" style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header">
      <button class="{{activeTab2 === 'drops' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('drops')"> Area Drops
      </button>
      <button class="{{activeTab2 === 'varianceA' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('varianceA')"> Area Drops (Variance A)
      </button>
      <button class="{{activeTab2 === 'varianceB' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab2('varianceB')"> Area Drops (Variance B)
      </button>
    </div>

    <div *ngIf="activeTab2 === 'drops'">
      <br />
      <app-area-drops-generator [slot0]="true"></app-area-drops-generator>
    </div>

    <div *ngIf="activeTab2 === 'varianceA'">
      <br />
      <app-drop-varianceA [slot0]="true"></app-drop-varianceA>
    </div>

    <div *ngIf="activeTab2 === 'varianceB'">
      <br />
      <app-drop-varianceB [slot0]="true"></app-drop-varianceB>
    </div>
  </div>
</div>

<div *ngIf="activeTab === 'otherDrops'">
  <app-other-drops-generator></app-other-drops-generator>
</div>
<ng-container *ngIf="activeTab === 'reportsHCExcel'">
  <app-reports-hcexcel></app-reports-hcexcel>
</ng-container>