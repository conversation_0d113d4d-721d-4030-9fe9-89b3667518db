<div class="card list-header" style="height: 70px; margin-top: 10px; margin-bottom: 0px; margin-left: 30px; margin-right: 30px;">
    <div class="header">
        <button class="{{activeTab === 'classList' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('classList')">
          Class List
        </button>
        <button class="{{activeTab === 'classModifier' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
        (click)="switchToTab('classModifier')">
          Class Modifier (CM) 
        </button>
        <button class="{{activeTab === 'situationalModifier' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
        (click)="switchToTab('situationalModifier')">
           Situational Modifier (SM)
        </button>

          <button class="{{activeTab === 'relicUses' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
          (click)="switchToTab('relicUses')" style="float: right;">
          Relic Uses
          </button>

    </div>
</div>

<app-class-list *ngIf="activeTab === 'classList'"></app-class-list>
<app-class-modifier *ngIf="activeTab === 'classModifier'"></app-class-modifier>
<app-situational-modifier *ngIf="activeTab === 'situationalModifier'"></app-situational-modifier>
<app-relic-Uses *ngIf="activeTab === 'relicUses'"></app-relic-Uses>
