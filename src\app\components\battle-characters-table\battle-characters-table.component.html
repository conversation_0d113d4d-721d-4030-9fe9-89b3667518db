<!--Add Minion Button-->
<div class="collumn no-horizontal-margin">
  <div *ngIf="level.battleCharacterIds.length === 0; else battleCharacterTable">
    <button *ngIf="!editable; else warningIcon"
            class="btn-absolute td-btn-focus btn btn-simple center"
            (click)="toPromptAddBattleCharacterToLevel(level)">
      <i class="pe-7s-plus icon center success"></i> Add Character
    </button>
    <ng-template #warningIcon>
      <i class="pe-7s-attention warning icon-review icon-large"></i>
    </ng-template>
  </div>
</div>
<ng-template #battleCharacterTable>
  <div class="collumn no-horizontal-margin">
    <!--List of minions-->
    <table ngClass="table {{ editable ? 'compact-' : '' }}battle-character-table">
      <thead>
        <tr>
          <th style="border-color:rgb(219, 219, 219) !important; color:black !important">{{ level.type | levelTypeName | titleConvention: "plural" }}</th>
          <th style="border-color:rgb(219, 219, 219) !important;" *ngIf="!editable">
            <button class="btn btn-simple btn-success pull-right"
                    (click)="toPromptAddBattleCharacterToLevel(level)">
              <i class="pe-7s-plus"></i>
            </button>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="tr-focus"
            *ngFor="let battleCharacterId of level.battleCharacterIds">
          <td>
            <button [ngStyle]="editable ? { margin: '3px' } : null"
                    (click)="accessCharacter(battleCharacterId)"
                    ngClass="btn btn-fill btn-{{
                (battleCharacterId | character)?.type | characterTypeClass
              }}">
              {{ (battleCharacterId | character)?.name }}
            </button>
          </td>
          <td *ngIf="!editable">
            <button class="btn tr-btn-focus btn-simple btn-danger pull-right"
                    (click)="
                toPromptRemoveOneBattleCharacterFromLevel(
                  level,
                  battleCharacterId
                )
              ">
              <i class="pe-7s-less"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</ng-template>
