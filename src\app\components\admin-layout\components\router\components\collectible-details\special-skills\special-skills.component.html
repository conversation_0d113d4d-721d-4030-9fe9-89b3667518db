<div style="margin-top: 20px; padding-bottom: 100px;" class="card">
    <div class="cardContent">
        <div class="titlecard" [style.background-color]="(itemCharacter?.rarity | tierColor: 'Character Rarity')">
            <h3>{{itemCharacter?.rarity}}</h3>
        </div>
    </div>
    <div style="display: flex; justify-content: center;">
        <h4>{{listCountRarity}}</h4>
    </div>


    <!--Modal Info-->
    <ng-container *ngIf="isModalInfo">
        <div class="background-div handleOut" aria-hidden="true">
            <div class="modal-backdrop" *ngIf="isModalInfo"></div> 
            <div id="modal-close" @popup class="popup-report" (mouseleave)="isModalInfo = false"
                style="background-color: black;">
                <div class="modal-header">
                    <div style="display: flex; justify-content: space-between;">
                        <p style="color:azure !important; text-align: center;" class="modal-title">RULES</p>
                        <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                            data-dismiss="background-div" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
                <div class="contextInfo">
                    <p class="modal-title title p-text">
                        1. Each category can be entered a maximun of <span class="span-text">two
                            (2) times.</span>
                    </p>
                    <p class="modal-title title p-text">
                        2. The <span class="span-text">DEFENSIVE</span> category is restricted to
                        <span class="span-text">WEAK SKILL</span> or <span class="span-text">Negative Affinity
                            Skills</span>
                    </p>
                    <p class="modal-title title p-text">
                        3. It is allowed to add up to <span class="span-text">five (5) Special
                            Skill.</span>
                    </p>
                    <p class="modal-title title p-text">
                        4. <span class="span-text">Physical</span> Special Skill occur
                        independently of the character's Elemental Affinities.
                    </p>
                </div>
            
                <div class="modal-header">
                    <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                        <p style="color:azure !important; text-align: center;" class="modal-title">Hybrid Rules</p>
                    </div>
                </div>
                <div class="contextInfo">
                    <p class="modal-title title p-text">
                        It will <span class="span-text">not depend</span> on the following tables:
                    </p>
                    <ul>
                        <li>Repetitions</li>
                        <li>ID Blocks</li>
                    </ul>
                    <p class="modal-title title p-text">
                        It <span class="span-text">depends</span> solely on the table:
                    </p>
                    <ul>
                        <li>Elemental Affinities</li>
                        <li>Status Effects Tables</li>
                    </ul>
                    <p class="modal-title title p-text">
                        Thus, the pool of choices is entirely open, characterized by individual units related to the
                        <span class="span-text">SKILL available.</span>
                    </p>
                    <p class="modal-title title p-text">
                        If an ID <span class="span-text">BOOST</span> or a <span class="span-text">NEGATIVE</span> is
                        already chosen, the system blocks
                        the <span class="span-text">HYBRID</span> option, and vice versa, if the
                        <span class="span-text">ID</span> is part of the HYBRID construction.
                    </p>
                </div>

              
                <div class="modal-header">
                    <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                        <p style="color:azure !important; text-align: center;" class="modal-title">Ailment Rules</p>
                    </div>
                </div>
                <div class="contextInfo">
                    <p class="modal-title title p-text">
                        The rule will not depend on the logic of RELATION, eliminating the need to consult rules in the
                        <span class="span-text">Elemental Affinities</span> table. Thus, the pool
                        of choices will remain completely open, as long as repetitions are still available.
                    </p>
                    <ul>
                        <li>Repetitions</li>
                        <li>ID Blocks</li>
                    </ul>
                </div>
            </div>
        </div>
    </ng-container>
    <!--Fim do Modal-->
    <!--List-->

    <div class="container">
        <!-- CATEGORY-->
        <div style="width: 350px !important;;">
            <div class="titleCategory">
                <span>Category</span>
                <i ngClass="iconInter" (click)="onModalClick()" class="pe-7s-info batt"></i>
            </div>
            <!-- Dropdown para seleção -->
            <select class="dropdown filter-dropdown limited selectedCategory" (change)="selectCategoryItem($event)"
                [disabled]="IsMaxCatgeory || listSpecialSkills?.listSpecialSkills.length == 5"
                [ngStyle]="{'cursor': IsMaxCatgeory || listSpecialSkills?.listSpecialSkills.length == 5 ? 'none' : 'pointer'}"
                [class.pulse-red]="isPulsing">
                <option value="default">Select</option>
                <option *ngFor="let cat of listCategory; let i = index" [value]="i" localTemplateVar-i="i">
                    {{ i + 1 }} - {{ cat?.category }}
                </option>
            </select>
            <div style="margin-left: 13px; width: 300px;">
                <ng-container *ngIf="itemSelectedCategory">
                    <div style="display: flex; font-weight: 600; margin-top: 10px; margin-bottom: 6px;">
                        <span>Description</span>
                    </div>
                    <div>
                        <span style="font-weight: 600">{{itemSelectedCategory}}: </span>
                        {{descriptionCatergory}}
                    </div>
                </ng-container>
            </div>
        </div>

        <!-- STATUS EFFECT-->
        <ng-container *ngIf="descriptionCatergory || listSpecialSkills?.listSpecialSkills.length > 0">
            <div style="width: 600px !important;">
                <div class="titleEffect">
                    <span>Status Effect</span>
                    <span>Repetition/ Remaining units</span>
                </div>
                <div class="dropdown-wrapper" (click)="stopPropagation($event)">
                    <!-- Botão para abrir/fechar o dropdown -->
                    <button class="dropdown-toggle" (click)="toggleDropdown()"
                        [ngClass]="{'isDisabled-Status': isStatusSelected  || listSpecialSkills.listSpecialSkills.length == 5}"
                        [disabled]="isStatusSelected  || listSpecialSkills.listSpecialSkills.length == 5"> {{
                        'Select' }}
                    </button>
                    <!-- Menu do dropdown -->
                    <ul *ngIf="dropdownOpen" class="dropdown-menu">
                        <li *ngFor="let status of listDescriptionStatusEffect; let i = index"
                            (click)="selectStatusEffect(i)">
                            <!-- Texto da opção -->
                            <span style="width: 500px;">{{ i + 1 }} - {{ status.description }}</span>
                            <!-- Círculo com positionID -->
                            <span class="circle" *ngIf="selectedCategory.typology !== 'Hybrid'">{{
                                getTotalPosition(status) }}</span>
                            <span class="circle">{{ getAmountPosition(status) }}</span>
                        </li>
                    </ul>
                </div>

                <div class="listRepetition">
                    <div style="display: flex; font-weight: 600; margin-top: 10px; margin-bottom: 6px;">
                        <span>Repetition</span>
                    </div>
                    <div style="display: flex; font-weight: 600; margin-top: 10px; margin-bottom: 6px;">
                        <span>Remaining units</span>
                    </div>
                </div>
                <div>
                    <table id="customers" style="width: 100%;  pointer-events: none;">
                        <tr style="background-color: white;"
                            *ngFor="let repetition of listRemainingUnits; let i = index">
                            <td class="repRemain">
                                <span class="spanTitle">{{ i + 1 }}</span>
                                <span class="remaing">{{ repetition.amount}}</span>
                            </td>
                        </tr>
                    </table>
                </div>
                <!-- HYBRID -->
                <div>
                    <table id="customers" style="width: 100%;  pointer-events: none; margin-top: 20px;">
                        <tr style="background-color: white;">
                            <td class="repRemain">
                                <span class="spanTitle fontWeight">Hybrid</span>
                                <span class="remaing">{{ listCalculatedHybrid}}</span>
                            </td>
                        </tr>
                    </table>
                    <table id="customers" style="width: 100%;  pointer-events: none; margin-top: 20px;">
                        <tr style="background-color: white;">
                            <td class="repRemain">
                                <span class="spanTitle fontWeight">Physical</span>
                                <span class="remaing">{{ listCalculatedPhysical}}</span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </ng-container>






        <!-- SPECIAL SKILL-->
        <ng-container *ngIf="listSpecialSkills?.listSpecialSkills.length > 0">
            <div style="width: 580px !important; margin-left: 40px;">
                <div class="titleCategory" style="margin-bottom: 10px;">
                    <span>Special Skill</span>
                </div>
                <table id="customers" style="width: 100%; cursor: pointer;">
                    <tr *ngFor="let select of listSpecialSkills.listSpecialSkills; let i = index"
                        (click)="returnSkillList(i)">
                        <td>
                            <span style="font-weight: 600; width: 80px;">
                                <ng-container>{{ select?.category }} {{ getCategoryCount(select?.category,
                                    i)}}:</ng-container>
                            </span>
                            {{select.nameStatusEffect}} 🠪 {{select?.descriptionStatusEffect}}
                        </td>
                    </tr>
                </table>
            </div>
        </ng-container>

    </div>
</div>