import { Component, Input } from '@angular/core';
import { ElementalDefenses } from 'src/app/lib/@bus-tier/models/ElementalDefenses';
import { StatusInfo } from 'src/app/lib/@bus-tier/models/StatusInfo';
import { Button } from 'src/app/lib/@pres-tier/data';
import { BattleUpgradeService, CharacterService, ClassService, StatusInfoService, StatusService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { BattleUpgrade } from '../../../../../../../lib/@bus-tier/models/BattleUpgrade';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-status-info',
  templateUrl: './status-info.component.html',
  styleUrls: ['./status-info.component.scss'],
})
export class StatusInfoComponent extends SortableListComponent<StatusInfo> implements ICollectibleDetails {

  currentCharacter: BattleUpgrade;
  valueBossLevel: string;
  nameClass: string;
  nameRarity: string;
  type: string;

  constructor(
    private _elementalDefensesService: ElementalDefensesService,
    protected _customService: CustomService,
    _activatedRoute: ActivatedRoute,
    protected _router: Router,
    protected _elmentalAffinitiesService: StatusInfoService,
    _userSettingsService: UserSettingsService,
    protected _statusService: StatusService,
    protected _battleUpgradeService: BattleUpgradeService,
    private _characterService: CharacterService,
    private _classService: ClassService,

  ) {
    super(_elmentalAffinitiesService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  statusInfo: StatusInfo[] = []
  @Input() character = ''
  statusInfoIds = []
  defensesSkillList: ElementalDefenses[] = [];
  spDefErrors: string[] = [];
  sortStatusOrder = -1;

override async ngOnInit(){
  await this._statusService.toFinishLoading();
  await this._elmentalAffinitiesService.toFinishLoading();  
  
   this.updateOrCreatedElementalAffinities();

  const valueCharacter = this.currentCharacter?.character || this.character;

  setTimeout(() => {
    const character = this._characterService.models.find(x => x.id === valueCharacter);
    this.nameRarity = character?.rarity;
    this.nameClass = this._classService.models.find(x => x.id === character?.classId)?.name;
    this.type = GameTypes.characterTypeName[character.type];

    if (this.currentCharacter) {
      const op = this.currentCharacter.bl || 0;
      this.valueBossLevel = `BL: ${op}`;
    } else {
      this.valueBossLevel = 'BL: 0';
    }
  }, 100);
 
  this.sortListByStatus();

}

updateOrCreatedElementalAffinities(): void {
  this.statusInfo = this._elmentalAffinitiesService.models.filter(status => status.character === this.character);

  if (this.statusInfo.length > 0) {
    const statusMap = this._statusService.models.reduce((map, status) => ({ ...map, [status.skill]: status }), {});
    
    this.statusInfo = this.statusInfo.map(statusInfo => { // update Elemental Affinities
      const status = statusMap[statusInfo.status];
      if (status) {
        statusInfo.acronym = status.acronym;
        statusInfo.idSkill = status.id;
        statusInfo.status = status.skill;
        this._elmentalAffinitiesService.svcToModify(statusInfo);
        return statusInfo;
      } else {
        const id = statusInfo.id; // define o valor de id
        this._elmentalAffinitiesService.svcToRemove(id);
        return null; // retorna null para remover o elemento
      }
    }).filter(statusInfo => statusInfo !== null); // remove os elementos null

    // verifica se há itens no statusMap que não estão no this.statusInfo
    Object.keys(statusMap).forEach(skill => {
      if (!this.statusInfo.find(statusInfo => statusInfo.status === skill)) {
        const newStatusInfo = this._elmentalAffinitiesService.createNewStatusInfo(skill);
        newStatusInfo.acronym = statusMap[skill].acronym;
        newStatusInfo.idSkill = statusMap[skill].id;
        newStatusInfo.character = this.character;
        this._elmentalAffinitiesService.svcToModify(newStatusInfo);
        this.statusInfo.push(newStatusInfo);
      }
    });
  } else {
      this._statusService.models.forEach(status => {
      const newStatusInfo = this._elmentalAffinitiesService.createNewStatusInfo(status.skill);
      newStatusInfo.acronym = status.acronym;
      newStatusInfo.idSkill = status.id;
      newStatusInfo.character = this.character;
      this._elmentalAffinitiesService.svcToModify(newStatusInfo);
      this.statusInfo.push(newStatusInfo);
     // return newStatusInfo;
    });
  }
}

  reset(character) {
    this.character = character
    this.sortStatusOrder = -1
    this.ngOnInit()
  }

  public onBack() {
    this._router.navigate(['others']);
  }

  async onExcelPaste(): Promise<void> {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if (this.DisplayErrors(lines)) return;

    const spDefErrors: string[] = [];

    this.defensesSkillList = this._elementalDefensesService.models.filter((x) => x.selectDrop === 'Defenses - Skill');
    const noValueAcronym = this.statusInfo.find((i) => i.acronym === undefined || i.acronym === '');

    if (noValueAcronym) {  
      Alert.showError('In Elemental Affinities there is a field without the Acronym.');
      return;
    }
   
    if (lines.length !== this.statusInfo.length) {
      Alert.showError('The number of lines does not match the number of Elemental Affinities. Please check the copied data.');
      return;      
    }
  
    // Primeira Passagem: Validações apenas para a coluna spDef
    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);  

      if (this.statusInfo.findIndex(s => s.status === cols[0]?.trim()) === -1) { 
        Alert.showError(`The status on line ${l + 1} does not exist in Elemental Affinities. Please check the copied data.`);
        return;
      }

    // Verifica se a segunda coluna está vazia
    if (!cols[1]?.trim()) {
      Alert.showError('The second column does not contain an acronym. Please check the copied data.');
      return;
    }

      // Tratamento da coluna spDef
      if (cols[2]?.trim()) {
        const spDefValue = cols[2].trim();
        // Verifica se é string ou número
        if (isNaN(Number(spDefValue))) {
          // Valida a string na lista de defesas
          const defense = this.defensesSkillList.find(d => d?.defenses.trim().toLowerCase() === spDefValue.toLowerCase());
          if (!defense) {
            spDefErrors.push(spDefValue);
            continue;
          } else {
            this.statusInfo.find((i) => i.acronym === cols[1]).idDefensesSkill = defense.id;         
          }
        }
      }
    }

    // Se houver erros de validação, exibe a mensagem e encerra o fluxo
    if (spDefErrors.length > 0) {
      Alert.showError(`Copied name not found in the system: ${spDefErrors.join(', ')}`);
      return; // Encerra o fluxo sem atribuir ou salvar nada
    }

    if (spDefErrors.length === 0) {
    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);

      let atribuido = false; // Variável booleana para indicar se o valor foi atribuído corretamente

      for (let i = 0; i < this.statusInfo.length; i++) {
        if (this.statusInfo[i].status === cols[0]?.trim()) {
          if (cols[2]?.trim()) {
            this.statusInfo[i].spDef = cols[2].trim();
          } else {
            this.statusInfo[i].spDef = null;
          }
          if (cols[3]?.trim()) {
            this.statusInfo[i].resistence = +cols[3]
              .split(' ')
              .join('')
              .split('.')
              .join('')
              .replace(',', '.');
          } else {
            this.statusInfo[i].resistence = null;
          }

          if (cols[4]?.trim()) {
            this.statusInfo[i].weakeness = +cols[4]
              .split(' ')
              .join('')
              .split('.')
              .join('')
              .replace(',', '.');
          } else {
            this.statusInfo[i].weakeness = null;
          }

          if (cols[5]?.trim()) {
            this.statusInfo[i].spAtk = +cols[5]
              .split(' ')
              .join('')
              .split('.')
              .join('')
              .replace(',', '.');
          } else {
            this.statusInfo[i].spAtk = null;
          }

          if (cols[6]?.trim()) {
            this.statusInfo[i].ascensionOrder = +cols[6]
              .split(' ')
              .join('')
              .split('.')
              .join('')
              .replace(',', '.');
          } else {
            this.statusInfo[i].ascensionOrder = null;
          }

          atribuido = true; // Indica que o valor foi atribuído corretamente
        }
      }

      if (atribuido) {
        this.statusInfo.forEach((statusInfo) => {
          this._elmentalAffinitiesService.svcToModify(statusInfo);
        });
        
      }
    }
    }

    await this._elmentalAffinitiesService.toSave();
    Alert.ShowSuccess('Status info copied successfully!');
    this.sortStatusOrder = -1
    this.ngOnInit();
  }


  DisplayErrors(array): boolean {
    let count = array[0].split(/\t/);
    if (count.length < 7) {
      Alert.showError("Copy the STATUS column values too!");
      return true;
    }

    if (count[0] === "") {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }

  getSpDef(status: StatusInfo): string {

    if (status.spDef === undefined) {
      return status.spDef = '';
    } else {
      return status.spDef;
    }

  }

  changeSPDEF(status: StatusInfo, value: string) {
    status.spDef = value == '' ? null : value;
    this._elmentalAffinitiesService.svcToModify(status);
    this._elmentalAffinitiesService.toSave();
  }

  changeResitence(status: StatusInfo, value: string) {
    status.resistence = value == '' ? null : parseInt(value);
    this._elmentalAffinitiesService.svcToModify(status);
    this._elmentalAffinitiesService.toSave();
  }

  changeWeakeness(status: StatusInfo, value: string) {
    status.weakeness = value == '' ? null : parseInt(value);
    this._elmentalAffinitiesService.svcToModify(status);
    this._elmentalAffinitiesService.toSave();
  }

  changeSPATK(status: StatusInfo, value: string) {
    status.spAtk = value == '' ? null : parseInt(value);
    this._elmentalAffinitiesService.svcToModify(status);
    this._elmentalAffinitiesService.toSave();
  }

  changeAscension(status: StatusInfo, value: string) {
    status.ascensionOrder = value == '' ? null : parseInt(value);
    this._elmentalAffinitiesService.svcToModify(status);
    this._elmentalAffinitiesService.toSave();
  }
  sortListByStatus() {
    this.sortStatusOrder *= -1;
    this.statusInfo.sort((a, b) => {
      return this.sortStatusOrder * a.status.localeCompare(b.status);
    });
  }

  sortAcronymOrder = -1;
  sortListByAcronym() {
    this.sortAcronymOrder *= -1;
    this.statusInfo.sort((a, b) => {
      return this.sortAcronymOrder * a.acronym.localeCompare(b.acronym);
    });
  }


  sortSPDEFOrder = -1;
  sortListBySPDEF() {
    /*  this.sortSPDEFOrder *= -1;
       this.statusInfo.sort((a, b) => {
         if(!a.spDef && b.spDef) return 1
         if(a.spDef && !b.spDef) return -1
         if(!a.spDef && !b.spDef) return 0
         return this.sortSPDEFOrder * (a.spDef - b.spDef);
       });
       */
  }

  sortSPATKOrder = -1;
  sortListBySPATK() {
    this.sortSPATKOrder *= -1;
    this.statusInfo.sort((a, b) => {
      if (!a.spAtk && b.spAtk) return 1
      if (a.spAtk && !b.spAtk) return -1
      if (!a.spAtk && !b.spAtk) return 0
      return this.sortSPATKOrder * (a.spAtk - b.spAtk);
    });
  }


  sortResistenceOrder = -1;
  sortListByResistence() {
    this.sortResistenceOrder *= -1;
    this.statusInfo.sort((a, b) => {
      if (!a.resistence && b.resistence) return 1
      if (a.resistence && !b.resistence) return -1
      if (!a.resistence && !b.resistence) return 0
      return this.sortResistenceOrder * (a.resistence - b.resistence);
    });
  }

  sortWeakenessOrder = -1;
  sortListByWeakeness() {
    this.sortWeakenessOrder *= -1;
    this.statusInfo.sort((a, b) => {
      if (!a.weakeness && b.weakeness) return 1
      if (a.weakeness && !b.weakeness) return -1
      if (!a.weakeness && !b.weakeness) return 0
      return this.sortWeakenessOrder * (a.weakeness - b.weakeness);
    });
  }

}
