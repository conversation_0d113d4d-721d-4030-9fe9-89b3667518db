<div class="card card-header">
    <div>
        <app-header-with-buttons [isBackButtonEnabled]="true" (cardBackButtonClick)="redirectToSettings()"
            [cardTitle]="title" [cardDescription]="'OpenAI Information Configuration Environment.'" [rightButtonTemplates]="[addEnvironmentAI]">           
        </app-header-with-buttons>
    </div>
</div>

<div class="content-holder">
   <div class="card card-modal">
    <!--MODAL key General-->
    <ng-container *ngIf="openModalGeneralKey || isEditKeyGeneral">
        <div class="modal-backdrop" *ngIf="openModalGeneralKey || isEditKeyGeneral"></div>
        <div id="modal-close" @popup class="popup-report" style="background-color: transparent">
            <form #formRef="ngForm" (ngSubmit)="submitFormKeyGeneral()" class="card-inter" (ngSubmit)="submitFormKeyGeneral()">           
                <div class="titleModal error401">
                  <h4 *ngIf="titleError401">{{ titleError401 }}</h4> 
                  <h4 *ngIf="titelErrorKey">{{ titelErrorKey }}</h4>   
                  <h4 *ngIf="titleModalEditKeyGeneral">{{ titleModalEditKeyGeneral }}</h4>        
                </div>                 
               <div class="closeModal">
                  <button (click)="closeModalKeyGeneral()" type="button" class="swal-icon-close" style="display: flex;">
                    <i class="pe-7s-close i-icon"></i>
                  </button>
                </div>              
                <div class="div-name">
                  <span for="name" class="span-name">Name</span>
                  <input id="name" 
                         type="text" 
                         [(ngModel)]="name" 
                         name="name" 
                         required 
                         #nameRef="ngModel"
                         [class.invalid-field]="(nameRef.invalid && (nameRef.touched || formRef.submitted))"
                         class="input-nameKey" />
                </div>
              
                <div class="div-key">
                  <span for="apiKey" class="span-key">Primary key</span>
                  <input id="apiKey" 
                         [(ngModel)]="apiKey" 
                         name="apiKey" 
                         required 
                         type="text"
                         #apiKeyRef="ngModel"
                         [class.invalid-field]="(apiKeyRef.invalid && (apiKeyRef.touched || formRef.submitted))"
                         class="input-nameKey" />
                </div>
                <div class="div-created">
                  <button class="btn confirm btn-primary ConfirmForm" 
                          style="width: 150px;" 
                          type="submit"
                          [disabled]="formRef.invalid">
                    Confirm
                  </button>
                </div>
              </form>
              
                
        </div> 
</ng-container>
  <!--FIM DO MODAL Key General-->

      <!-- Tabela da Key geral-->
      <ng-container *ngIf="listKeyGeneral.length > 0">
        <div style="width: 100%; margin-top: 10px;" >
          <table class="table-bordered">
              <thead >
                  <tr>
                    <th class="trBC th-general" style="width: 10px;">Index</th>
                    <th class="trBC th-general" style="width: 350px;">Name</th>         
                    <th class="trBC th-general" >API Primary key
                      <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                      ttPadding="10px" tooltip="Key used to search for chatgpt model types."
                      class="pe-7s-info batt" style="top: 8px !important;"></i>
                    </th>
                    <th class="trBC th-general" style="width: 170px;">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let general of listKeyGeneral; let i = index" >    
                      <td class="gray">
                          {{i + 1}}
                      </td>
                      <td>
                          {{general?.name}}
                      </td>
                      <td>
                        <div style="width: min-content; word-wrap: break-word;">
                          {{general?.apiKey}}
                        </div>                        
                      </td>
                      <td style="display: flex; justify-content: center;">
                          <div>
                              <button class="btn btn-fill btn-info ng-star-inserted" 
                              style="padding: 0 !important; width: 65px;" (click)="editKeyGeneral(general.id)" >
                                  <i class="pe-7s-pen"></i>
                              </button>
                              <button _ngcontent-hkn-c250="" class="btn btn-danger btn-fill btn-remove"
                                  style="padding: 0 !important; width: 65px;" (click)="deleteKeyGeneral(general.id)">
                                  <i _ngcontent-hkn-c250="" class="pe-7s-close"></i>
                              </button>
                          </div>
    
                      </td>
                  </tr>
                </tbody>
    
          </table>
      </div>
      </ng-container>
  

      <!--MODAL List Keys-->
      <ng-container *ngIf="popupStats">
        <div class="modal-backdrop" *ngIf="popupStats"></div>
        <div id="modal-close" @popup class="popup-report" style="background-color: transparent">
            <form #formRef="ngForm" (ngSubmit)="submitForm()" class="card-inter" (ngSubmit)="submitForm()">
              <ng-container *ngIf="popupStats">
                <div class="titleModal">
                  <h4>{{ !isEdit ? titleModal : titleModalEdit }}</h4>
                </div>
              </ng-container>         
              
                <div class="closeModal">
                  <button (click)="closeModal()" type="button" class="swal-icon-close" style="display: flex;">
                    <i class="pe-7s-close i-icon"></i>
                  </button>
                </div>
              
                <div class="div-name">
                  <span for="name" class="span-name">Name</span>
                  <input id="name" 
                         type="text" 
                         [(ngModel)]="name" 
                         name="name" 
                         required 
                         #nameRef="ngModel"
                         [class.invalid-field]="(nameRef.invalid && (nameRef.touched || formRef.submitted))"
                         class="input-nameKey" />
                </div>
              
                <div class="div-key">
                  <span for="apiKey" class="span-key">API Key</span>
                  <input id="apiKey" 
                         [(ngModel)]="apiKey" 
                         name="apiKey" 
                         required 
                         type="text"
                         #apiKeyRef="ngModel"
                         [class.invalid-field]="(apiKeyRef.invalid && (apiKeyRef.touched || formRef.submitted))"
                         class="input-nameKey" />
                </div>
     
                  <div class="div-between">
                    <div class="div-model">
                      <span for="model" class="span-model">Model</span>
                      <select id="model" 
                              [(ngModel)]="selectedModel" 
                              name="model" 
                              required 
                              #modelRef="ngModel"
                              [class.invalid-field]="(modelRef.invalid && (modelRef.touched || formRef.submitted))"
                              class="select-width">
                        <option value="">Select Environment</option>
                        <option *ngFor="let model of models" [value]="model.id">{{ model.id }}</option>
                      </select>
                    </div>
                
                    <div>
                      <span for="temperature" style="margin-right: 10px;">Temperature</span>
                      <input id="temperature" 
                             type="number" 
                             required 
                             [(ngModel)]="temperature" 
                             name="temperature" 
                             min="0" max="2" step="0.1"
                             #temperatureRef="ngModel"
                             [class.invalid-field]="(temperatureRef.invalid && (temperatureRef.touched || formRef.submitted))"
                             class="input-temperature" />
                    </div>
                  </div>
                
                  <div class="div-between">
                    <div>
                      <span for="limitForReview" style="margin-right: 10px;">Limit for review</span>
                      <input id="limitForReview" 
                             type="number" 
                             required 
                             [(ngModel)]="limitForReview" 
                             name="limitForReview" 
                             min="1"
                             #limitRef="ngModel"
                             [class.invalid-field]="(limitRef.invalid && (limitRef.touched || formRef.submitted))"
                             class="input-limit" />
                    </div>
                  </div>
                
                  <div>
                    <span style="font-weight: 700;">OBS:</span>
                    <p class="p-size">
                     Model: gpt-4 - Max_tokens: 2048; (Usado no Ghostwriter, Grammar e Creative Writing) <br>
                     Model: gpt-4-1106-preview - Max_tokens: 4096; (Usado no AI Revisor)
                    </p>
                  </div>     
              
                <div class="div-created">
                  <button class="btn confirm btn-primary ConfirmForm" 
                          style="width: 150px;" 
                          type="submit"
                          [disabled]="formRef.invalid">
                    Confirm
                  </button>
                </div>
              </form>
              
                
        </div> 
</ng-container>
  <!--FIM DO MODAL-->
  
  <!-- Tabela de keys-->
  <ng-container *ngIf="listKeyGeneral.length > 0 && listEnvironment.length > 0">
    <div style="width: 100%; margin-top: 10px;" >
      <table class="table-bordered">
          <thead >
              <tr>
                <th class="trBC" style="width: 10px;">Index</th>
                <th class="trBC" style="width: 350px;">Name</th>
                <th class="trBC" style="width: 300px;">Model</th>          
                <th class="trBC" >API KEY</th>
                <th class="trBC" style="width: 130px;">Temperature</th>
                <th class="trBC" style="width: 130px;">Limite of Review</th>
                <th class="trBC" style="width: 170px;">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of listEnvironment; let i = index" >    
                  <td class="gray">
                      {{i + 1}}
                  </td>
                  <td>
                      {{item?.name}}
                  </td>
                  <td>
                      {{item?.model}}
                  </td>
                  <td>
                    <div style="width: 900px; word-wrap: break-word;">
                      {{item?.apiKey}}
                    </div>             
                  </td>
                  <td>
                      {{item?.temperature}}
                  </td>
                  <td>
                      {{item?.limitForReview}}
                  </td>
                  <td style="display: flex; justify-content: center;">
                      <div>
                          <button class="btn btn-fill btn-info ng-star-inserted" 
                          style="padding: 0 !important; width: 65px;" (click)="editEnvironment(item.id)" >
                              <i class="pe-7s-pen"></i>
                          </button>
                          <button _ngcontent-hkn-c250="" class="btn btn-danger btn-fill btn-remove"
                              style="padding: 0 !important; width: 65px;" (click)="deleteEnvironment(item.id)">
                              <i _ngcontent-hkn-c250="" class="pe-7s-close"></i>
                          </button>
                      </div>

                  </td>
              </tr>
            </tbody>

      </table>
  </div>

  <ng-container *ngIf="listEnvironment.length === 0">
      <div style="text-align: center; padding: 20px;">
          <h3>Empty list. Click to create the list.</h3>
      </div>
  </ng-container>
  </ng-container>



    </div>

</div>

  