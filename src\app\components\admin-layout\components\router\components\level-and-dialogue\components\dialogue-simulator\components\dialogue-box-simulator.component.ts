import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { SpeechService } from 'src/app/services/speech.service';
import { OptionService } from 'src/app/services/option.service';
import { Dialogue, StoryBox, OptionBox, Option, Speech, DilemmaBox, Dilemma} from 'src/app/lib/@bus-tier/models';
import { CharacterService } from 'src/app/services/character.service';
import { OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { characterTypeColor } from 'src/lib/darkcloud/dialogue-system/game-types';
import { AnswerDilemmaBoxService } from 'src/app/services';
import { DilemmaService } from 'src/app/services';
import { AnswerDilemmaBox } from 'src/app/lib/@bus-tier/models/AnswerDilemmaBox';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { DiceOverlayService } from 'src/app/services/dice-overlay.service';
import { DiceChoicePopupService, DiceChoiceOption } from 'src/app/services/dice-choice-popup.service';

/**
 * Interface defining the structure of chat-style messages displayed in the simulator
 */
interface Message {
  message: string;
  name?: string;
  color?: string;
  loading: boolean;
  sideLeft: boolean;
  option?: boolean;
}

/**
 * Individual dialogue box simulator component that handles the processing and display
 * of a single dialogue box (story, option, or dilemma). Manages message animations,
 * user interactions, dice rolling mechanics, and roadblock evaluation.
 */
@Component({
  selector: 'dialogue-box-simulator',
  templateUrl: './dialogue-box-simulator.component.html',
  styleUrls: ['dialogue-box-simulator.component.scss'],
})
export class DialogueBoxSimulatorComponent implements OnInit, OnDestroy {
  // Event emitters for parent component communication
  @Output() proccessFinished: EventEmitter<void> = new EventEmitter();
  @Output() updateScroll: EventEmitter<void> = new EventEmitter();

  // Input properties from parent component
  @Input() public dialogue: Dialogue;
  @Input() public box: StoryBox | OptionBox | DilemmaBox | AnswerDilemmaBox;
  @Input() public questionAgainBox: StoryBox | OptionBox | DilemmaBox | AnswerDilemmaBox;

  // Message display and animation configuration
  public messages: Message[] = [];
  @Input() messageTyppingDelay = 600; // Milliseconds for typing animation
  @Input() messagesBetweenDelay = 1000; // Milliseconds between messages

  // User interaction state
  public options: Option[] = [];
  public dilemmas: Dilemma[] = [];
  public optionType = undefined;
  public selectedOption: string;
  public usedOptionsPerBox: Map<string, Set<string>> = new Map();

  // Animation timeout handles
  timeout: any;
  timeout2: any;

  // Feature flags and state
  public hasRoadBlock: boolean = false;
  public isRoadBlockViewEnabled = false;
  public isDiceSimulationEnabled = true;

  // Dice system state management
  private waitingForDiceResult: boolean = false; // Prevents multiple dice operations
  private activeDiceSubscription: any = null; // Tracks active dice roll subscription

  constructor(
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _dilemmaService: DilemmaService,
    private _speechService: SpeechService,
    private _characterService: CharacterService,
    private _change: ChangeDetectorRef,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    public sessionService: OptionBoxService, // Used for tracking selected options across boxes
    private _roadBlockService: RoadBlockService,
    private _diceOverlayService: DiceOverlayService,
    private _diceChoicePopupService: DiceChoicePopupService,
  ) {}

  /**
   * Initialize the component and start processing the assigned dialogue box
   */
  public ngOnInit(): void {
    this.processBox(this.box);
    console.log('messages', this.messages);
  }

  /**
   * Main method to process different types of dialogue boxes
   * @param box The dialogue box to process (story, option, dilemma, or answer)
   */
  private processBox(box: StoryBox | OptionBox | DilemmaBox | AnswerDilemmaBox): void {
    // Reset messages for new box processing
    this.messages = [];
    console.log("processBox", box);

    if (box instanceof StoryBox) {
      // Check for roadblocks and process story speeches
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.storyProgressIds?.length > 0) {
        this.processNextStoryProgress(box.storyProgressIds);
      } else {
        this.finishBoxProcessing();
      }
    } else if (box instanceof OptionBox) {
      // Check for roadblocks and set up user choice options
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.optionIds?.length > 0) {
        this.optionType = box.type;
        this.processOptions(box.optionIds);
      } else {
        this.finishBoxProcessing();
      }
    } else if (box instanceof DilemmaBox) {
      // Check for roadblocks and set up dilemma choices
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.optionDilemmaIds?.length > 0) {
        console.log('DilemmaBox ID:', box.optionDilemmaIds);
        this.processDilemmas(box.optionDilemmaIds);
      } else {
        this.finishBoxProcessing();
      }
    } else if (box instanceof AnswerDilemmaBox) {
      // Check for roadblocks and process answer speeches
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.storyProgressIds?.length > 0) {
        this.processNextStoryProgress(box.storyProgressIds);
      } else {
        this.finishBoxProcessing();
      }
    } else {
      // Unknown box type, finish processing
      this.finishBoxProcessing();
    }
  }

  /**
   * Process story speeches sequentially with typing animations
   * @param storyProgressIds Array of speech IDs to process
   * @param current Current index in the array (for recursion)
   */
  processNextStoryProgress(storyProgressIds: string[], current = 0): void {
    if (current < storyProgressIds.length) {
      let delay = 1;
      let speech = this._speechService.svcFindById(storyProgressIds[current]);

      if (speech) {
        // Start typing animation (left side for non-player characters)
        this.startTyppingAnimation(this._characterService.svcFindById(speech.speakerId)?.id != 'C0');
        delay = this.messageTyppingDelay;

        // Show the actual message after typing animation
        this.timeout = setTimeout(() => {
          this.finsishTyppingAnimation();
          this.addSpeechMessage(speech);
          this.updateUI();
        }, delay);

        delay = this.messagesBetweenDelay + this.messageTyppingDelay;
      }

      // Process next speech after delay
      this.timeout2 = setTimeout(() => {
        this.processNextStoryProgress(storyProgressIds, current + 1);
        this.updateUI();
      }, delay);
    } else {
      // All speeches processed, finish box processing
      this.finishBoxProcessing();
    }
    this.updateUI();
  }

  /**
   * Trigger UI updates and scroll position adjustment
   */
  public updateUI(): void {
    this._change.detectChanges();
    this.updateScroll.emit();
  }

  /**
   * Set up user choice options for an option box
   * @param optionIds Array of option IDs to load
   */
  processOptions(optionIds: string[]): void {
    this.options = this._optionService.svcCloneByIds(optionIds);

    // Initialize tracking for used options in this box
    if (!this.sessionService.usedOptionsPerBox.has(this.box.id)) {
      this.sessionService.usedOptionsPerBox.set(this.box.id, new Set<string>());
    }
  }

  /**
   * Set up dilemma choices for a dilemma box
   * @param dilemmaIds Array of dilemma IDs to load
   */
  processDilemmas(dilemmaIds: string[]): void {
    this.dilemmas = this._dilemmaService.svcCloneByIds(dilemmaIds);
    console.log('processDilemmas:', this.dilemmas);
  }

  // ===== DICE SYSTEM METHODS =====

  /**
   * Check if an option has dice system (both success and failure outcomes)
   */
  private hasDiceSystem(option: Option): boolean {
    return !!(option.answerBoxId && option.answerBoxNegativeId);
  }



  /**
   * Show dice choice popup for user to decide approach
   */
  private showDiceChoicePopup(option: Option): void {
    this._diceChoicePopupService.showChoicePopup(option, (choice: DiceChoiceOption) => {
      this.handleDiceChoice(option, choice);
    });
  }

  /**
   * Handle the user's choice from the dice choice popup
   */
  private handleDiceChoice(option: Option, choice: DiceChoiceOption): void {
    let targetBoxId: string;

    switch (choice) {
      case DiceChoiceOption.POSITIVE:
        // Guaranteed success - use positive answer box
        targetBoxId = option.answerBoxId;
        console.log('User chose positive outcome');
        this.processTargetBox(targetBoxId);
        break;

      case DiceChoiceOption.NEGATIVE:
        // Guaranteed failure - use negative answer box
        targetBoxId = option.answerBoxNegativeId;
        console.log('User chose negative outcome');
        this.processTargetBox(targetBoxId);
        break;

      case DiceChoiceOption.ROLL_DICE:
        // Show d20 dice roll overlay
        console.log('User chose to roll dice');
        this.showD20DiceRoll(option);
        break;

      default:
        console.error('Unknown dice choice:', choice);
        return;
    }
  }

  /**
   * Display the d20 dice roll overlay and handle the result
   * @param option The option that triggered the dice roll
   */
  private showD20DiceRoll(option: Option): void {
    const dc = option.resultDC || 10; // Difficulty class for the roll
    const modifiers = []; // No modifiers in simulator for now

    console.log('Showing d20 dice roll for option:', option, 'DC:', dc);

    // Clean up any existing dice subscription to prevent conflicts
    this.cleanupDiceSubscription();

    // Set flag to prevent multiple dice operations
    this.waitingForDiceResult = true;

    // Subscribe to dice overlay state changes BEFORE showing overlay
    let isProcessed = false; // Prevents duplicate processing

    this.activeDiceSubscription = this._diceOverlayService.overlayState$.subscribe(state => {
      console.log('Dialogue simulator received dice overlay state:', {
        isVisible: state.isVisible,
        hasResult: !!state.result,
        waitingForDiceResult: this.waitingForDiceResult,
        isProcessed: isProcessed,
        result: state.result
      });

      // Process result when overlay is dismissed with a valid result
      if (!state.isVisible && state.result && this.waitingForDiceResult && !isProcessed) {
        isProcessed = true; // Prevent multiple processing

        console.log('Processing dice result - overlay dismissed with result');

        // Determine target box based on success/failure
        const success = state.result.success;
        const targetBoxId = success ? option.answerBoxId : option.answerBoxNegativeId;

        console.log('Dice roll completed:', state.result, 'Target box:', targetBoxId);

        // Reset state and clean up
        this.waitingForDiceResult = false;
        this.cleanupDiceSubscription();

        // Continue dialogue with appropriate outcome
        this.processTargetBox(targetBoxId);
      }
    });

    // Show the dice roll overlay after subscription is set up
    this._diceOverlayService.showDiceRoll(dc, modifiers);
  }

  /**
   * Safely clean up active dice subscription to prevent memory leaks
   */
  private cleanupDiceSubscription(): void {
    if (this.activeDiceSubscription && !this.activeDiceSubscription.closed) {
      this.activeDiceSubscription.unsubscribe();
    }
    this.activeDiceSubscription = null;
  }

  /**
   * Process a target dialogue box by finding and loading it
   * @param targetBoxId ID of the box to process next
   */
  private processTargetBox(targetBoxId: string): void {
    const storyBox = this._storyBoxService.svcFindById(targetBoxId);
    const optionBox = this._optionBoxService.svcFindById(targetBoxId);

    if (storyBox) {
      this.processBox(storyBox);
    } else if (optionBox) {
      this.processBox(optionBox);
    }
  }

  /**
   * Handle user selection of an option or dilemma
   * @param id The ID of the selected option or dilemma
   */
  public processedOption(id: string): void {
    // Prevent multiple selections
    if (this.selectedOption) return;
    this.selectedOption = id;

    // Track used options for investigation type boxes (allows multiple selections)
    if (this.optionType === OptionBoxType.INVESTIGATION) {
      const usedOptions = this.sessionService.usedOptionsPerBox.get(this.box.id)!;
      usedOptions.add(id);
    }

    // Filter to show only the selected option/dilemma
    this.options = this.options.filter(o => o.id == id && !o.isOmitted);
    this.dilemmas = this.dilemmas.filter(x => x.id == id);

    // Get the actual option/dilemma objects
    let option = this._optionService.svcFindById(id);
    let dilemma = this._dilemmaService.svcFindById(id);

    if (!option && !dilemma) return;

    // Handle dilemma selections
    if (id.includes('DIL')) {
      console.log('Processing dilemma selection');
      let dilemmabox = this._answerDilemmaBoxService.svcFindById(dilemma?.idDilemmaBox);
      console.log('Dilemma box:', dilemmabox);
      if (dilemmabox) {
        this.processBox(dilemmabox);
      }
      return;
    }

    // Handle regular option selections
    if (!option) return;

    // Default to success outcome
    let targetBoxId = option.answerBoxId;

    // Check if dice system should be applied
    if (this.isDiceSimulationEnabled && this.hasDiceSystem(option)) {
      // Show choice popup for user to decide approach (positive/negative/roll dice)
      this.showDiceChoicePopup(option);
    } else {
      // No dice system or dice disabled - proceed with default outcome
      this.processTargetBox(targetBoxId);
    }
  }

  /**
   * Input setter for roadblock view toggle
   */
  @Input() set roadBlockViewEnabled(value: boolean) {
    this.isRoadBlockViewEnabled = value;
    this.recheckRoadBlock();
    this._change.detectChanges();
  }

  /**
   * Input setter for dice simulation toggle
   */
  @Input() set diceSimulationEnabled(value: boolean) {
    this.isDiceSimulationEnabled = value;
    this._change.detectChanges();
  }

  /**
   * Check for roadblocks on the current box and its child elements
   * Roadblocks are conditions that must be met before dialogue elements are accessible
   */
  private recheckRoadBlock(): void {
    const currentBox = this.box;

    if (currentBox instanceof StoryBox) {
      // Check roadblocks directly on the story box
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);
    } else if (currentBox instanceof OptionBox) {
      // Check roadblocks on the option box itself
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);

      // Also check roadblocks on child option answer boxes
      if (this.options && this.options.length > 0) {
        let hasChildRoadblocks = false;
        for (const option of this.options) {
          const optionData = this._optionService.svcFindById(option.id);
          if (optionData && optionData.answerBoxId) {
            if (this._roadBlockService.filterByStoryBoxId(optionData.answerBoxId)) {
              hasChildRoadblocks = true;
              break;
            }
          }
        }
        // Show roadblock indicator if parent or any child has roadblocks
        this.hasRoadBlock = this.hasRoadBlock || hasChildRoadblocks;
      }
    } else if (currentBox instanceof DilemmaBox) {
      // Check roadblocks on the dilemma box itself
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);

      // Also check roadblocks on child dilemma answer boxes
      if (this.dilemmas && this.dilemmas.length > 0) {
        let hasChildRoadblocks = false;
        for (const dilemma of this.dilemmas) {
          const dilemmaData = this._dilemmaService.svcFindById(dilemma.id);
          if (dilemmaData && dilemmaData.idDilemmaBox) {
            if (this._roadBlockService.filterByStoryBoxId(dilemmaData.idDilemmaBox)) {
              hasChildRoadblocks = true;
              break;
            }
          }
        }
        // Show roadblock indicator if parent or any child has roadblocks
        this.hasRoadBlock = this.hasRoadBlock || hasChildRoadblocks;
      }
    } else if (currentBox instanceof AnswerDilemmaBox) {
      // Check roadblocks directly on the answer dilemma box
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);
    } else {
      // Unknown box type, no roadblocks
      this.hasRoadBlock = false;
    }
  }

  /**
   * Handle dialogue completion and clean up session data
   */
  onDialogueFinished(): void {
    this.sessionService.clearSession();
  }


  /**
   * Start the typing animation by adding a loading message
   * @param sideLeft Whether the message appears on the left side (true for NPCs, false for player)
   */
  startTyppingAnimation(sideLeft = true): void {
    this.messages.push({
      message: '',
      loading: true,
      sideLeft: sideLeft
    });
  }

  /**
   * Finish the typing animation by removing the loading message
   */
  finsishTyppingAnimation(): void {
    if (this.messages[this.messages.length - 1].loading) {
      this.messages.pop();
    }
  }

  /**
   * Add a speech message to the chat display
   * @param speech The speech object containing message and speaker information
   */
  addSpeechMessage(speech: Speech): void {
    let character = this._characterService.svcFindById(speech.speakerId);
    this.messages.push({
      message: speech.message,
      name: this._characterService.svcFindById(speech.speakerId)?.name,
      color: characterTypeColor[character?.type],
      loading: false,
      sideLeft: character?.id != 'C0' // Player character (C0) appears on right side
    });
  }

  /**
   * Complete the processing of the current dialogue box
   * @param forceFinish Whether to force finish the entire dialogue
   */
  finishBoxProcessing(forceFinish = false): void {
    if (forceFinish) {
      // Force finish the entire dialogue
      this.options = [];
      this.proccessFinished.emit();
      this.sessionService.clearSession();
    } else {
      if (this.optionType === OptionBoxType.INVESTIGATION) {
        // Investigation boxes allow re-questioning, set up for repeat
        this.questionAgainBox = this.box;
      } else {
        // Regular boxes proceed to next
        this.proccessFinished.emit();
      }
    }
  }

  /**
   * Clean up resources when component is destroyed
   */
  ngOnDestroy(): void {
    // Clear animation timeouts
    clearInterval(this.timeout);
    clearInterval(this.timeout2);

    // Clean up dice subscription and reset state
    this.cleanupDiceSubscription();
    this.waitingForDiceResult = false;
  }

}

