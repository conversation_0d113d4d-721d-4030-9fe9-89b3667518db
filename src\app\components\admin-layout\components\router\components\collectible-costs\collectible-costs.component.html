<div class="card list-header"
     style="height: 70px; margin: 30px; margin-bottom: 0px;">
  <div class="header">
    <button class="{{activeTab === 'upgrade' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('upgrade')">
      Upgrade Cost
    </button>
    <button class="{{activeTab === 'ascension' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('ascension')">
      Ascension Cost
    </button>      
  </div>
</div>
<app-upgrade-costs [active]="activeTab" [active3]="activeTab2"></app-upgrade-costs>