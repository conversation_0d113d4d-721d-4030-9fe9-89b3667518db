import { ChangeDetectorRef, Component } from '@angular/core';
import { SituationalModifier } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { KnowledgeService } from 'src/app/services';
import { SituationalModifierService } from 'src/app/services/situational-modifier.service';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-situational-modifier',
  templateUrl: './situational-modifier.component.html',
  styleUrls: ['./situational-modifier.component.scss']
})
export class SituationalModifierComponent {

  titles = ['Knowledge', 'Situational Modifier', 'Factor', 'Description'];
  listSituationalModifier: SituationalModifier[] = [];
  newSituational: SituationalModifier;
  description: string;
  activeLanguage = 'PTBR';
  activeTab: string;
  isSituational: boolean;

  constructor(
    private _situationalModifierService: SituationalModifierService,
    private _knowledgeService: KnowledgeService,
    private ref: ChangeDetectorRef
  ) { }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };


  async ngOnInit(): Promise<void> {
    this._situationalModifierService.toFinishLoading();

    setTimeout(() => {
      this.listSituationalModifier = this._situationalModifierService.models;
      this.description = `Showing ${this.listSituationalModifier.length} results`;
      this.isSituational = this.listSituationalModifier.length > 0; 
    }, 60);    
  }

  async onExcelPaste() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);

      this._situationalModifierService.models = [];
      this._situationalModifierService.toSave();

      for (const line of lines) {
        const values = line.split('\t');
        const knowledge = values[0];

        if (values.length !== this.titles.length) {
          Alert.showError(`Error: Number of columns in the imported data does not match the expected (${this.titles.length}).`);
          return;
        }

        // Verifica se o atributo existe em this._atributteService.models
        if (knowledge || knowledge !== '') {
          const exists = this._knowledgeService.models.some((model) => model?.knowledge === knowledge);
          if (!exists) {
            Alert.showError(`Error: Knowledge "${knowledge}" not found in the system.`);
            return; // Interrompe o fluxo
          }

          this.newSituational = await this._situationalModifierService.createNewSubContextKnowledge();
          this.newSituational.situationalmodifier = [];
          this.newSituational.factor = [];
          this.newSituational.description = [];

          this.newSituational.knowledge = knowledge;
          this.newSituational.situationalmodifier.push(values[1]);
          this.newSituational.factor.push(values[2]);
          this.newSituational.description.push(values[3]);

        } else {
          this.newSituational.situationalmodifier.push(values[1]);
          this.newSituational.factor.push(values[2]);
          this.newSituational.description.push(values[3]);
        }
      }

      this.listSituationalModifier.push(this.newSituational);
      const index = this.listSituationalModifier.indexOf(this.newSituational);
      this._situationalModifierService.svcToModify(this.listSituationalModifier[index]);

      this._situationalModifierService.toSave();
      this.ref.detectChanges();
      Alert.ShowSuccess('Situational Modifier list copied successfully!');
      this.ngOnInit();
    } catch (error) {
      Alert.showError('Error importing data from Excel.');
      console.error(error);
    }
  }



  changeSituationalModifierValue(rowIndex: number, rowSub: number, name: string, newValue: string) {

    if (name === 'knowledge') {
      this.listSituationalModifier[rowIndex].knowledge = newValue;
    } else if (name === 'situationalmodifier') {
      this.listSituationalModifier[rowIndex].situationalmodifier[rowSub] = newValue;
    } else if (name === 'factor') {
      this.listSituationalModifier[rowIndex].factor[rowSub] = newValue;
    }
    else if (name === 'description') {
      this.listSituationalModifier[rowIndex].description[rowSub] = newValue;
    }
    this._situationalModifierService.svcToModify(this.listSituationalModifier[rowIndex]);
  }

}
