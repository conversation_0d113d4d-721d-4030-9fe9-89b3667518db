import { Component, OnInit } from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { ItemService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';

@Component({
  selector: 'app-item-details-class-selection',
  templateUrl: './item-details-class-selection.component.html',
  styleUrls: ['./item-details-class-selection.component.scss'],
})
export class ItemDetailsClassSelectionComponent implements OnInit 
{
  itemClasses: ItemClass[];
  custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _customService: CustomService,
  ) {}

  async ngOnInit(): Promise<void>
  {
    this.itemClasses = this._itemClassService.models;
    this.custom = await this._customService.svcGetInstance();
    if(!this.custom.weaponClassItem) this.custom.weaponClassItem = [];
    this.addClassesNameInListofselectedclassesArray();
  }
  
  addClassesNameInListofselectedclassesArray()
  {
    for(let i = 0; i < this.custom.weaponClassItem.length; i++)
    {
      let item = this._itemClassService.svcFindById(this.custom.weaponClassItem[i]);
      this._customService.listOfSelectedClasses.push(item.name);
    }
    this._customService.listOfSelectedClasses = [...new Set(this._customService.listOfSelectedClasses)]; 
  }

  async onItemClassSelected(itemClass: ItemClass)
  {
    this.addRemoveSelectClass(itemClass.name);    
    this._customService.toggleItemClass(itemClass, 'weaponClassItem');
    this.custom = await this._customService.svcGetInstance();
  }

  addRemoveSelectClass(itemClassName:string)
  {
    let remove: boolean = false; 
    
    for(let i = 0; i < this._customService.listOfSelectedClasses.length; i++)
    {
      if(itemClassName == this._customService.listOfSelectedClasses[i])
      {
        this._customService.listOfSelectedClasses.splice(i, 1);
        remove = true;
        break;
      }
    }
    if(!remove)
    {
      this._customService.listOfSelectedClasses.push(itemClassName);
      this._customService.listOfSelectedClasses = [...new Set(this._customService.listOfSelectedClasses)];
    }
  }
}
