import { Component, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { CommonWeaponsService } from 'src/app/services/commonWeaponsService';
import { SpecialWeaponServiceHC } from 'src/app/services/special-weaponHC.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-weapons-wlbase',
  templateUrl: './weapons-wlbase.component.html',
  styleUrls: ['./weapons-wlbase.component.scss']
})
export class WeaponsWLBaseComponent implements OnInit, OnDestroy  {
    
  public activeTab: string;
  activeLanguage = 'PTBR';
  cardTitle: string;
  description: string;  
  timeout:any;

  constructor(
    private _commonWeaponsService: CommonWeaponsService,
    private _specialWeaponServiceHC: SpecialWeaponServiceHC,
  ) {}

  public ngOnInit() {

    this.cardTitle ='Common Weapons';    
    const tab = localStorage.getItem(
      `tab-WeaponsWLBaseComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = 'commonWeapons';
    let count = 0;

    this.timeout = setTimeout(()=> {      
    this._commonWeaponsService.models.forEach((x) => {
      x.commonWeaponReceivedHC.forEach((a) => {
        if (a.nameHc) { // Verifica se nameHc está definido e não é nulo
          count++;
        }
      });
    });    
      this.description = `Showing ${count} Received results`;  
    }, 550); 
   
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(
      `tab-WeaponsWLBaseComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );

    if (this.activeTab === 'specialWeapons'){
    this.cardTitle = 'Special Weapons';
    this._specialWeaponServiceHC.toFinishLoading();
    let count = 0;
      
    this._specialWeaponServiceHC.models.forEach((x) => {
      x.bluePrintReceivedHC.forEach((a) => {
        if (a.idNameBPR) { // Verifica se nameHc está definido e não é nulo
          count++;
        }
      });
    }); 
    this.description = `Showing ${count} Received results`;    
    
  } else {
    this.cardTitle ='Common Weapons';
    let count = 0;
      
    this._commonWeaponsService.models.forEach((x) => {
      x.commonWeaponReceivedHC.forEach((a) => {
        if (a.nameHc) { // Verifica se nameHc está definido e não é nulo
          count++;
        }
      });
    });    
      this.description = `Showing ${count} Received results`;    
  }
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout); 
  }
 
}
