import { ChangeDetectorRef, Component,EventEmitter,Input,OnDestroy,Output,} from '@angular/core';
import { Subscription } from 'rxjs';
import { Area, Character, ConfigThreshold, Dialogue,DilemmaBox,Level,Mission, SpokePlace} from 'src/app/lib/@bus-tier/models';
import { Dilemma } from 'src/app/lib/@bus-tier/models/Dilemma';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';

import { AreaService, ConfigThresholdService,DialogueService,DilemmaBoxService,DilemmaService,EventHandlerService,LevelHelperService,LevelService,OptionService,ReviewService,UserSettingsService,} from 'src/app/services';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { Alert } from 'src/lib/darkcloud';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import {AtributteDilemma, RoadBlockType,} from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { HighlightElement } from 'src/lib/others';
import { Atributte } from '../../../../../../../../../../lib/@bus-tier/models/Atributte';
import { AnswerDilemmaBoxService } from '../../../../../../../../../../services/answer-dilemmaBox.service';
import { AtributteService } from '../../../../../../../../../../services/atributte.service';
import { fadeIn, popup,} from '../../../../../bound-item-list/bound-list.component.animations';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { SpeechService } from 'src/app/services/speech.service';

interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}

@Component({
  selector: 'app-dilemma-box',
  templateUrl: './dilemma-box.component.html',
  styleUrls: ['./dilemma-box.component.scss'],
  animations: [fadeIn, popup],
})
export class DilemmaBoxComponent implements OnDestroy {
  @Input() index: number;
  @Input() dialogue: Dialogue;
  @Input() dilBox: DilemmaBox;
  @Input() preloadedSpeakers: Character[];
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() language: string;
  @Output() refresh: EventEmitter<void> = new EventEmitter();
  public HighlightElement = HighlightElement;

  toRemoveProcessConditionFunc: (roadblock: any) => void;

  popupStats = false;
  rpgSubscription: Subscription;
  optionDilemmaBox: DilemmaBox;
  roadblockId: string;
  isConfirm = false;
  optionId: string;
  hasLabel: boolean = false;
  timeout: any;
  timeout2: any;
  optionBoxes;
  dilemmaOptionBoxes: Dilemma;
  spokePlaces = [];
  usedRoadBlocks = [];
  usedOnLevels = [];
  roadBlocksUseds: RoadBlock[] = [];
  atributtoClasses: Atributte[] = [];
  isTextValid = true;
  openDilemmaBox = false;
  selectedThreshold;
  pointsOptions = [-2, -1, 0, 1, 2];
  thresholdOptions: ConfigThreshold[] = [];
  valueThreshold: number;
  totalValuePoints: AtributteDilemma[] = [];
  valueDescription: string;
  existValueThreshold: number;
  existValuePoints = [];
  isUsedRoad = false;
  listSpokePlace: SpokePlace[] = [];

  constructor(

    private _userSettingsService: UserSettingsService,
    private _dialogueService: DialogueService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _dilemmaService: DilemmaService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _roadblockService: RoadBlockService,
    private _eventsHandlerService: EventHandlerService,
    private _change: ChangeDetectorRef,
    private _levelHelperService: LevelHelperService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _reviewService: ReviewService,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    private _atributteService: AtributteService,
    private _configThresholdService: ConfigThresholdService,
  ) {
    this.toRemoveProcessConditionFunc = this.toRemoveRoadblock.bind(this);
  }

  async ngOnInit() {
    this.rpgSubscription = this._eventsHandlerService
      .OnRpgWordAdded()
      .subscribe((object) => {
        this.optionDilemmaBox = this._dilemmaBoxService.svcCloneById(
          this.dilBox?.id
        );
      });

    this.timeout = setTimeout(() => {
      this._change.detectChanges();
    }, 350);

    if (this.dilBox.AndOrCondition == undefined) {
      this.dilBox['AndOrCondition'] = 'OR';
    }

    await this.getRoadBlocks(); 
    this.selectedThreshold = 0;
    this.optionBoxes = this._dilemmaService.svcFilterByIds(
      this.dilBox.optionDilemmaIds,
      true
    );
    this.listSpokePlace = this._levelHelperService.models;
    this._configThresholdService.toFinishLoading();
    this.thresholdOptions = (await this._configThresholdService.models) || [];
    this.roadblocksForKeyInformation();
    this._atributteService.toFinishLoading();
    this.atributtoClasses = this._atributteService.models;

    // Verifica se os atributos foram carregados antes de chamar checkDilemmaPoints
    setTimeout(() => {
      if (this.atributtoClasses && this.atributtoClasses.length > 0) {
        this.checkDilemmaPoints();
      } else {
        console.warn('Atributos não foram carregados corretamente.');
      }
    }, 50);
  }

  checkDilemmaPoints() {
    this.optionBoxes.forEach((item: any) => {
      if (!item.threshold) {
        item.threshold = this.thresholdOptions[0]?.valueThreshold;
      }
      if (!item.points) {
        item.points = []; // Inicializa o array 'points' se estiver undefined

        for (let i = 0; i < this.atributtoClasses.length; i++) {
          if (!item.points[i]) {
            item.points.push({
              idAtributte: this.atributtoClasses[i].id,
              nameAtributte: this.atributtoClasses[i].atributte,
              valuePoints: 0,
            });
          }
        }
        this._dilemmaService.svcToModify(item);
      }
    });
    this.optionBoxes = this._dilemmaService.svcFilterByIds(
      this.dilBox.optionDilemmaIds,
      true
    );
  }

async roadblocksForKeyInformation() {
  const roadblocks = this._roadblockService.models.filter((roadblock) => {
    return roadblock.spokeElementId === this.dilBox?.id;
  });

  roadblocks.forEach((roadblock) => {
    const dialogueIds = this._dialogueService.models.filter((dialogue) => {
      return (
        roadblock.StoryBoxId &&
        !dialogue.id.includes('ML') &&
        roadblock.StoryBoxId.includes(dialogue.id)
      );
    }).map((dialogue) => dialogue.id);

    dialogueIds.forEach((dialogueId) => {
      const areaId = Area.getSubIdFrom(dialogueId);
      const area = this._areaService.svcFindById(areaId);
      if (!area) return;

      const levelId = Level.getSubIdFrom(dialogueId);
      const level = this._levelService.svcFindById(levelId);
      const dialogue = this._dialogueService.svcFindById(Dialogue.getSubIdFrom(dialogueId, 'PT-BR'));
      const hierarchyCode = area.hierarchyCode;
      const levelIndex = this._reviewService.reviewResults[levelId]?.index;
      const type = GameTypes.dialogueTypeDisplay[+dialogue.type];

      this.usedOnLevels.push(`[${hierarchyCode}] ${levelIndex} "${level.name}" (${type})`);
    });

    this.usedRoadBlocks.push(roadblock);
  });
}

  isUsedRoadblock(dil: DilemmaBox): boolean {
    // Retorna true se encontrar um elemento em usedRoadBlocks com o mesmo spokeElementId que o id do answerBox
    const isUsed = this.usedRoadBlocks.some(
      (rb) => rb.spokeElementId === dil.id
    );

    if (isUsed) {
      this.isUsedRoad = true;
    } else {
      this.isUsedRoad = false;
    }
    return isUsed;
  }

  async getRoadBlocks() {
    await this._roadblockService.toFinishLoading();

    let rb = await this._roadblockService.models.find(
      (rb) => rb.spokeElementId == this.dilBox.id
    );
    if (rb) this.hasLabel = true;
  }
 

  ngOnDestroy() {
    // prevent memory leak when component is destroyed
    this.rpgSubscription.unsubscribe();

    clearInterval(this.timeout);
    clearInterval(this.timeout2);
  }

  public getInformation(id: string): Data.Internal.Base {
    return this._userSettingsService.getInformation(id);
  }

  public updateInformation<TKey extends keyof Data.Internal.Base>(
    id: string,
    key: TKey,
    value: Data.Internal.Base[TKey]
  ) {
    if (id) this._userSettingsService.updateInformation(id, key, value);
  }

  // ng trackers (for list optimization)
  trackByIndex(index: number, option: Dilemma): any {
    return index;
  }

  public async toMove(transpose: number) {
    if (this.dialogue.boxIds.length <= 1) return;
    const oldIndex = this.dialogue.boxIds.indexOf(this.dilBox.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.dialogue.boxIds[newIndex]) return;

    this.dialogue.boxIds[oldIndex] = this.dialogue.boxIds[newIndex];
    this.dialogue.boxIds[newIndex] = this.dilBox.id;
    await this._dialogueService.svcToModify(this.dialogue);

    this._change.detectChanges();
    this.refresh.emit();
  }

  public async toRemove(box: DilemmaBox) {
    if (await Alert.showRemoveAlert(box.id)) {      
      this._dilemmaBoxService.svcToRemove(box.id);
      this._dialogueService.removeBox(this.dialogue, box.id);
      this.removeSubComponents(box.id);
      this._userSettingsService.removeIdObjectInformations(box.id);
      this.removePlacesLabels(box.id);

    }
    this._change.detectChanges();
    this.refresh.emit();
  }

  removePlacesLabels(id: string) {
    this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === id) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });   

    this._levelHelperService.models.forEach((places) => {
      if (places.elementId.includes(id)) {
        this._levelHelperService.svcToRemove(places.id);
      }
    });     
  }

  public async onChange(
    optionBox: DilemmaBox,
    property?: string,
    value?: string
  ) {
    if (property) optionBox[property] = value;

    await this._dilemmaBoxService.svcToModify(this.dilBox);
    this._change.detectChanges();
  }

  public async onChangeOption(option: Dilemma) {
    await this._dilemmaService.svcToModify(option);
    this._change.detectChanges();
  }

  public async onChangeOptionValue(
    option: Dilemma,
    property: string,
    value: any
  ) {
    option[property] = value ? value : '';
    this._dilemmaService.svcToModify(option);
    // this._dilemmaService.svcHasErrors(option);
    const answer = this._answerDilemmaBoxService.svcFindById(
      option.idDilemmaBox
    );
    answer.messageDilemma = value ? value : '';
    this._answerDilemmaBoxService.svcToModify(answer);
    this._change.detectChanges();
    this.refresh.emit();
  }

  //Look the method on the documentation to understand better.
  async updateOptionFromAnswerBox(option: Dilemma) {
    for (let i = 0; i < this.optionBoxes.length; i++) {
      if (this.optionBoxes[i].id == option.id) {
        this.optionBoxes[i].label = option.label;
        break;
      }
    }

    await this._dilemmaService.svcToModify(option);
    this._change.detectChanges();
    this.refresh.emit();
  }

  public async selectWeight(option: Dilemma) {
    await Alert.showNumberField('Dilemma Weight').then(async (e) => {
      if (e.isDismissed) return;

      option.weight = e.value;
      if (option.weight === 0) {
        option.weight = undefined;
      }
      await this._dilemmaService.svcToModify(option);
    });
    this._change.detectChanges();
    this.refresh.emit();
  }

  // methods that can be called within the html (add, remove, move listItems)
  public async toMoveOption(option: Dilemma, transpose: number) {
    if (this.dilBox.optionDilemmaIds.length <= 1) return;

    const oldIndex = this.dilBox.optionDilemmaIds.indexOf(option.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.dilBox.optionDilemmaIds[newIndex]) return;

    this.dilBox.optionDilemmaIds[oldIndex] =
      this.dilBox.optionDilemmaIds[newIndex];
    this.dilBox.optionDilemmaIds[newIndex] = option.id;
    await this._dilemmaBoxService.svcToModify(this.dilBox);
    this._change.detectChanges();
    this.ngOnInit();
    this.refresh.emit();
  }

  closeAreaStatsPopup() {
    this.popupStats = false;
    this.valueThreshold = 0;
    this.existValuePoints = [];
  }

  handleOutsideMouseClick(event: MouseEvent) {
    if (!this.popupStats) return;
    const myDiv = document.getElementById('modal-close');
    // Get the position relative to the viewport
    const rect = myDiv.getBoundingClientRect();
    const top = rect.top;
    const left = rect.left;
    //Check the x axis
    if (event.clientX < left || event.clientX > left + myDiv.offsetWidth) {
      this.closeAreaStatsPopup();
    } else if (
      event.clientY > top + myDiv.offsetHeight ||
      event.clientY < top
    ) {
      this.closeAreaStatsPopup();
    }
  }

  closeLevelReferencePopup() {
    this.popupStats = false;
    this.ngOnInit();
  }

  public async toAddRoadblock() {
    let roadblock = await this._roadblockService.svcPromptCreateNew(
      this.dilBox.id,
      RoadBlockType.OBTAINED_ITEM
    );
    this.roadblockId = roadblock.id;
    this._roadblockService.srvAdd(roadblock);
    this._change.detectChanges();
    await this.roadBlocksUseds.push(roadblock);
    this.refresh.emit();
  }

  async ngAfterViewInit() {
    await this._roadblockService.toFinishLoading();
    this.roadBlocksUseds =
      this._roadblockService.filterStoryboxForAllRoadblocks(this.dilBox?.id);
    this.refresh.emit();
  }

  public async toPromptAddAnswerDilemma() {
    try {
      let option = await this._dilemmaBoxService.addOption(this.dilBox);
      this.optionBoxes.push(option);
      this.optionId = option.id;
      const answerdilemma =
        await this._answerDilemmaBoxService.svcPromptCreateNew(option.id);
      await this._answerDilemmaBoxService.srvAdd(answerdilemma);
      option.idDilemmaBox = answerdilemma.id;
      option.points = [];

      for (let index = 0; index < this.atributtoClasses.length; index++) {
        option.points.push({
          idAtributte: this.atributtoClasses[index].id,
          nameAtributte: this.atributtoClasses[index].atributte,
          valuePoints: 0,
        });
      }

      await this._dilemmaService.svcToModify(option);
      this._change.detectChanges();

      this.timeout2 = setTimeout(() => {
        this._change.detectChanges();
        this.refresh.emit();
      }, 300);
    } catch (error) {
      Alert.showError(error);
    }
    this.checkDilemmaPoints();
    this._change.detectChanges();
    this.refresh.emit();
  }

  public async RemoveOption(option: Dilemma) {
    try {
      if (
        await Alert.showRemoveAlert('Dilemma: ' + option.message)) {
        await this._dilemmaService.svcToRemove(option.id);
        await this._dilemmaBoxService.RemoveOption(this.dilBox, option.id);
        await this._answerDilemmaBoxService.svcToRemove(option.idDilemmaBox);
        await this.removeSubComponents(option.id);

        this._levelHelperService.models.forEach((places) => {
          if (places.elementId === option?.id) {
            this._levelHelperService.svcToRemove(places.id);
          }
        });

        this._roadblockService.models.forEach((roadblock) => {
          if (roadblock.spokeElementId === option?.id) {
            roadblock.spokeElementId = undefined;
            this._roadblockService.svcToModify(roadblock);
          }
        });   
        this._userSettingsService.removeIdObjectInformations(option.id);  

        this.ngOnInit();

      }
    } catch (error) {
      Alert.showError(error);
    }
    this.ngOnInit();
    this._change.detectChanges();
    this.refresh.emit();
  }

  async removeSubComponents(id: string) {    

    await this._speechService.models.forEach((speech) => {
      if (speech.id.includes(id)) {
        this._speechService.svcToRemove(speech.id);
      }
    });
    await this._eventService.models.forEach((event) => {
      if (event.id.includes(id)) {
        this._eventService.svcToRemove(event.id);
      }
    });
    await this._markerService.models.forEach((marker) => {
      if (marker.id.includes(id)) {
        this._markerService.svcToRemove(marker.id);
      }
    });
    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.id.includes(id)) {
        this._roadblockService.svcToRemove(roadblock.id);
      }
    });
    
  }

  async toRemoveRoadblock(roadblock: RoadBlock) {
    if (await Alert.showRemoveAlert(Typing.typeName(roadblock))) {
      this.roadblockId = undefined;
      this._roadblockService.svcToRemove(roadblock.id);
      this.roadBlocksUseds = this.roadBlocksUseds.filter(
        (rb) => rb.id != roadblock.id
      );
      this._change.detectChanges();
      this.refresh.emit();
      this.ngOnInit();
    }
  }

  async changeLabel(event: Event) {   
    const inputElement = event.target as HTMLInputElement;
    const text = inputElement.value.trim();

    const existingLabels = {};
    this.listSpokePlace.forEach((spoke) => {
      existingLabels[spoke.originalLabel] = true;
    });

    const spoke = this.listSpokePlace.find(
      (spoke) => spoke.elementId === this.dilBox.id
    );

    if (text === '') {
      inputElement.value = '<<Label for progress condition>>';
      this.dilBox.label = undefined;
      this._dilemmaBoxService.svcToModify(this.dilBox);
      this._dilemmaBoxService.toSave();

      if (spoke) {  
        this.removeBDLabelPlaces(this.dilBox.id, spoke.id);
       }

       this.refresh.emit();   
    } 
    else {
      if (existingLabels[text]) {
        // Label já existe na base de dados do Places 
        Alert.showError('This Label ALREADY Exists!!', '');

       if(this.dilBox.label === undefined) {
        inputElement.value = '<<Label for progress condition>>';
        this._change.detectChanges();
      } else {
        inputElement.value =this.dilBox.label;
      }
       this.refresh.emit();    
      } 
      else {
        // Atualiza label
        if (spoke) {
          spoke.originalLabel = text;
          spoke.text = '[Dilemma Box] ' + text;
          this._levelHelperService.svcToModify(spoke);
        } 
        else {
          // Adiciona label
          const helper: SpokePlaceHelper = {
            elementId: this.dilBox.id,
            label: text,
            component: '[Dilemma Box]',
            text: '[Dilemma Box] ' + text,
          };
          this._levelHelperService.createNewLevelHelper(helper);
        }
        inputElement.value = text;
        this.dilBox.label = text;
        this._dilemmaBoxService.svcToModify(this.dilBox);           
      }        
    }  
  }

  async removeBDLabelPlaces(idDil: string, idPlaces: string) {    

    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === idDil) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });

    await this._levelHelperService.svcToRemove(idPlaces);     
  }
 

  async chooseAndOrCondition(event) {
    //AND = FALSE, OR = TRUE.
    if (event.target.checked == false) this.dilBox['AndOrCondition'] = 'AND';
    else this.dilBox['AndOrCondition'] = 'OR';

    await this._dilemmaBoxService.svcToModify(this.dilBox);
    await this._dilemmaBoxService.toSave();
  }

  // Dilemma Points open
  public async openModalDilemmaPoints(dilemma: Dilemma) {
    this.dilemmaOptionBoxes = dilemma;
    this.popupStats = true;

    if (this.dilemmaOptionBoxes.threshold !== undefined) {
      this.existValueThreshold = this.dilemmaOptionBoxes.threshold;
    }
    // Verificar os pontos e configurar os valores nos selects correspondentes
    if (
      this.dilemmaOptionBoxes.points &&
      this.dilemmaOptionBoxes.points !== undefined
    ) {
      // Limpa o array this.existValuePoints antes de começar
      this.existValuePoints = [];

      this.existValuePoints = this.dilemmaOptionBoxes.points;
    } else {
      // Se não houver pontos configurados, define todos os valores padrão como 0
      for (let index = 0; index < this.atributtoClasses.length; index++) {
        this.existValuePoints.push({
          idAtributte: this.atributtoClasses[index].id,
          nameAtributte: this.atributtoClasses[index].atributte,
          valuePoints: 0,
        });
      }
    }
  }

  updateThreshold(event: Event) {
    const selectElement = event.target as HTMLSelectElement;
    this.valueThreshold = parseInt(selectElement.value, 10);
    this.isConfirm = true;
  }

  updatePoints(idAtributte: string, nameAtributte: string, event: Event) {
    const selectElement = event.target as HTMLSelectElement;
    const points = parseInt(selectElement.value);

    const attribute = this.existValuePoints.find(
      (item) => item.idAtributte === idAtributte
    );

    // Se o objeto foi encontrado, atualiza o campo valuePoints
    if (attribute) {
      attribute.valuePoints = points;
    } else {
      console.log(`Não encontrado: idAtributte = ${idAtributte}`);
    }

    this.isConfirm = true;
  }

  saveDilemmaPoints() {
    if (this.valueThreshold === undefined) {
      this.valueThreshold = this.thresholdOptions[0].valueThreshold;
    }

    this.dilemmaOptionBoxes.threshold = this.valueThreshold;
    this.dilemmaOptionBoxes.points = [];
    this.dilemmaOptionBoxes.points = this.existValuePoints;
    this._dilemmaService.svcToModify(this.dilemmaOptionBoxes);
    this._dilemmaService.toSave();
    this.totalValuePoints = [];
    this.popupStats = false;
    this.isConfirm = false;
    this._change.detectChanges();
  }
}
