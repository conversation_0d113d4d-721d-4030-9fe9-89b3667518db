import { SoundService } from 'src/app/services/sound.service';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter} from '@angular/core';
import { ReviewService } from 'src/app/services/review.service';
import { LevelService } from 'src/app/services/level.service';
import { DialogueService } from 'src/app/services/dialogue.service';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { EmotionService } from 'src/app/services/emotion.service';
import { ItemService, ReviewMessage } from 'src/app/services/item.service';
import { MissionService } from 'src/app/services/mission.service';
import { ObjectiveService } from 'src/app/services/objective.service';
import { VideoService } from 'src/app/services/video.service';
import { TutorialService } from 'src/app/services/tutorial.service';
import { SpeechService } from 'src/app/services/speech.service';
import { Al<PERSON> } from 'src/lib/darkcloud';
import { Index } from 'src/lib/others';
import { ClassService } from 'src/app/services/class.service';
import { Subscription } from 'rxjs';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { CharacterService } from 'src/app/services/character.service';
import { OptionService } from 'src/app/services/option.service';
import { SceneryService } from 'src/app/services/scenery.service';
import { ThemeService } from 'src/app/services/video-theme.service';
import { GetSetChange } from 'src/lib/darkcloud/utils/utils';
import { ChangeLog } from 'src/lib/darkcloud/angular/dsadmin/ChangeLog';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { MicroloopService } from 'src/app/services/microloop.service';
import { LanguageService } from 'src/app/services/language.service';
import { CustomService } from 'src/app/services/custom.service';
import { AreaService } from 'src/app/services';


declare const $: any;
declare interface RouteInfo {
  path: string;
  title: string;
  icon: string;
  review: string[];
  reviewTooltip?: string;
  reviewMessages?: ReviewMessage[];
}
export const ROUTES: RouteInfo[] = [
  { path: '/settings', title: 'Settings', icon: 'pe-7s-config', review: [] },
  {
    path: '/review',
    title: 'Review',
    icon: 'pe-7s-attention',
    review: ['Option', 'Speech', 'Event', 'Marker', 'Level'],
  },
  {
    path: '/report',
    title: 'Reports',
    icon: 'pe-7s-news-paper',
    review: [],
  },
  {
    path: '/rpg',
    title: '"RPG" Words',
    icon: 'pe-7s-plugin',
    review: [],
  },
  { path: '/areas', title: 'Areas', icon: 'pe-7s-map-2', review: [] },
  {
    path: '/levels',
    title: 'Levels',
    icon: 'pe-7s-share',
    review: ['Level', 'Dialogue'],
    reviewTooltip: 'Parameters:\n\nTransitions need a an area\nNot Transitions need a scenery\nNeed to be linked in order\nBoss or Minion Type need a battle character\nRelease dialogue need a marker',
  },
  {
    path: '/sceneries',
    title: 'Sceneries',
    icon: 'pe-7s-photo',
    review: ['Scenery'],
    reviewTooltip: 'Parameters:\n\nScenery must be assigned to an area',
  },
  {
    path: '/characters',
    title: 'Characters',
    icon: 'pe-7s-users',
    review: ['Character'],
    reviewTooltip: 'Parameters:\n\nCharacter must be assigned to a level\nCharacter (Boss/Suboss/Minion) should be assigned to a battle',
  },
  {
    path: '/classes',
    title: 'Classes',
    icon: 'pe-7s-id',
    review: ['Class'],
    reviewTooltip: 'Parameters:\n\nMust be assigned to a character\nAssigned characters must be in battle or speech', },
  {
    path: '/emotions',
    title: 'Emotions',
    icon: 'pe-7s-smile',
    review: ['Emotion'],
  },
  {
    path: '/missions',
    title: 'Missions',
    icon: 'pe-7s-medal',
    review: ['Mission', 'Objective'],
    reviewTooltip: 'Parameters:\n\nMust have objectives\nShould be assigned to an event\nShould not be assigned more than one event\nAll objectives should have a complete event',
  },
  {
    path: '/itemClass',
    title: 'Item Classes',
    icon: 'pe-7s-box2',
    review: ['ItemClass', 'Item'],
    reviewTooltip: 'Parameters:\n\nItems should be assigned and received',
  },
  {
    path: '/microloopList',
    title: 'Microloops',
    icon: 'pe-7s-repeat',
    review: ['Microloop'],
    reviewTooltip: 'Parameters:\n\nMust have at least one battle character\nMust have at least one speaker',
  },
  {
    path: '/language',
    title: 'Language',
    icon: 'mat-translate',
    review: ['Language']
  },
  {
    path: '/others',
    title: 'Others',
    icon: 'pe-7s-edit',
    review: ['Video', 'Tutorial', 'Minigame'],
    reviewTooltip: 'Parameters:\n\nShould be assigned to an event\n',
  },
  {
    path: '/stats',
    title: 'XP',
    icon: 'pe-7s-graph2',
    review: ['Stats'],
    reviewTooltip: 'Parameters:\n\nShould be assigned must an Level XP',
  },
  {
    path: '/skillTree',
    title: 'Skill tree',
    icon: 'pe-7s-keypad',
    review: ['Casts'],
   
  },
  {
    path: '/weaponRecords',
    title: 'Weapons',
    icon: 'pe-7s-note2',
    review: ['ItemDetails']
  },
   {
    path: '/battleDropsGenerator',
    title: 'Battle Drops',
    icon: 'pe-7s-drop',
    review: ['BattleDrops']
  },
  {
    path: '/specialWeaponGenerator',
    title: 'Special Weapon',
    icon: 'pe-7s-hammer',
    review: ['SpecialWeapons']
  },
  {
    path: '/buildingGenerator',
    title: 'Buildings',
    icon: 'pe-7s-culture',
    review: ['Buildings']
  },
  {
    path: '/laboratories',
    title: 'Lab',
    icon: 'pe-7s-home',
    review: ['Laboratory']
  },
  {
    path: '/upgrades',
    title: 'Upgrades',
    icon: 'pe-7s-airplay',
    review: ['Upgrades']
  },
];

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})

export class SidebarComponent implements OnInit, OnDestroy 
{
  @Input() @GetSetChange isReady: boolean;
  @Output() isReadyChange = new EventEmitter<boolean>();

  public appVersion: string;
  public buildDate: string;
  public topMenuItems: RouteInfo[];
  public bottomMenuItems: RouteInfo[];
  public objIdsToReview: Index<string[]>;
  public objsReviews: ReviewMessage[];
  public checkedImportedFile: boolean;
  private reviewSubscription: Subscription;
  private svcReviewAllSubscription: Subscription;
  private fileImportCheckSubscription: Subscription;

  constructor(
    private _userSettingsService: UserSettingsService,
    public _languageService: LanguageService,
    private _reviewService: ReviewService,
    private _levelService: LevelService,
    private _areaService: AreaService,
    private _dialogueService: DialogueService,
    private _characterService: CharacterService,
    private _eventService: EventService,
    private _speechService: SpeechService,
    private _markerService: MarkerService,
    private _emotionService: EmotionService,
    private _itemService: ItemService,
    private _missionService: MissionService,
    private _objectiveService: ObjectiveService,
    private _videoService: VideoService,
    private _tutorialService: TutorialService,
    private _classService: ClassService,
    private _optionService: OptionService,
    private _sceneryService: SceneryService,
    private _themeService: ThemeService,
    private _soundService: SoundService,
    private _microloopService: MicroloopService,
    private _customService: CustomService
  ) {
    this.appVersion = this._userSettingsService.appVersionString;
    this.buildDate = this._userSettingsService.buildDate;

    this._reviewService.reset();

    this.reviewSubscription = this._reviewService.reviewChange.subscribe(
      (value) => {
        this.objIdsToReview = value;
        this.objsReviews = this._reviewService.objsReviews;
      }
    );
    this.fileImportCheckSubscription =
      this._reviewService.checkImportedFileChange.subscribe((value) => {
        this.checkedImportedFile = value;
      });

    this.initializeReviewAsync();
  }

  private subscribableServices: ModelService[] = 
  [
    this._microloopService,//0
    this._eventService, // 1
    this._markerService, // 2
    this._levelService, // 3
    this._dialogueService, // 4
    this._characterService, // 5
    this._speechService, // 6
    this._emotionService, // 7
    this._itemService, // 8
    this._missionService, // 9
    this._objectiveService, // 10
    this._videoService, // 11
    this._tutorialService, // 12
    this._classService, // 13
    this._optionService, // 14
    this._sceneryService, // 15
    this._themeService, // 16
    this._soundService // 17,
  ];

  

  async ngOnInit() {
    this.topMenuItems = ROUTES.slice(0, 4);
    this.bottomMenuItems = ROUTES.slice(4);    
    this.objIdsToReview = this._reviewService.objIdsToReview; 
    this.objsReviews = this._reviewService.objsReviews;
    this.checkedImportedFile = this._reviewService.checkedImportedFile;

    this.reviewSubscription.add(
      this._reviewService.reviewRequest.subscribe((reviewRequest) => 
      {
        if (reviewRequest) 
        {
          if (reviewRequest.parameters.objectId) 
          {
            this.subscribableServices
              .find((service) => service.typeName === reviewRequest.typeName)
              .svcReviewById(reviewRequest.parameters.objectId);
          } 
          else if (reviewRequest.parameters.levelId) 
          {
            this.subscribableServices
              .find((service) => service.typeName === reviewRequest.typeName)
              .svcReviewAllFromLevel(reviewRequest.parameters.levelId);
          }
        }
        this._reviewService.save();
      })

      
    );
    

    await this._microloopService.toFinishLoading();
    let objectsToRemove = this._microloopService.models.filter(x => x.id.split('.')[0] == "ML");
    let idsToRemove = objectsToRemove.map(x => x.id);

    this._microloopService.svcToRemove(idsToRemove);

    let custom = await this._customService.svcGetInstance();
    if(!custom.generatedPaths)
    {
      this._areaService.processPaths();
      this._customService.SetPathProcessed(true);
    }
  }

  async initializeReviewAsync(): Promise<void> 
  {
    if (!this._reviewService.load()) 
    {
      for (const service of this.subscribableServices) 
      {
        await service.toFinishLoading();
        service.svcReviewAll();
      }
    }
    this._reviewService.save();
    this.isReady = true;
  }
  ngOnDestroy() 
  {
    this.svcReviewAllSubscription.unsubscribe();
    this.reviewSubscription.unsubscribe();
    this.fileImportCheckSubscription.unsubscribe();
  }

  PromptChangeLog() 
  {
    const progSteps: string[] = [];
    const changeLog = ChangeLog.reverse().slice(0, 5);
    changeLog.forEach((step) => progSteps.push(''));
    Alert.showChainingAlert(changeLog, progSteps).then((value) => 
    {
      ChangeLog.reverse();
    });
  }
}
