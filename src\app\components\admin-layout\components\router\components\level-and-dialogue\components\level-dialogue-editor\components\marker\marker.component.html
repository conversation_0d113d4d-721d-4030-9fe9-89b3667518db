<td colspan="6" id="{{ marker.id }}">
  <p style="opacity: 30%;" [ngStyle]="{'color': marker.id.includes('DIL') ? '#D3D3D3' : ''}">{{marker.id}}</p>
  <table class="table table-responsive"
    [ngClass]="{'marker-table': !this.isBossMarker, 'boss-marker-table': this.isBossMarker}">
    <tbody>
      <tr>
        <!--ORDER ARROWS-->
        <td class="td-sort">
          <div [title]="marker.id">
            <button class="btn btn-success btn-simple btn-invert btn-sm" (click)="toMove(marker, -1)">
              <i class="pe-7s-angle-up-circle"></i>
            </button>
            {{ index }}
            <button class="btn btn-danger btn-simple btn-invert btn-sm" (click)="toMove(marker, 1)">
              <i class="pe-7s-angle-down-circle"></i>
            </button>
          </div>
        </td>

        <!--TYPE SELECTION-->

        <td>
          <ng-container
            *ngIf="+this.marker.type == 4 || +this.marker.type == 8 || +this.marker.type == 12 || +this.marker.type == 0; else markerType">
            <label>Event Type</label>
          </ng-container>
          <ng-template #markerType>
            <label>Marker Type</label>
          </ng-template>
          <br />
          <select class="filter-dropdown auto" [(ngModel)]="marker.type" (change)="onChange(); loadRequirements()">
            <option></option>
            <option *ngFor="let markerType of preloadedMarkerTypes" value="{{ markerType }}">
              {{ markerType | markerTypeName }}
            </option>
          </select>
        </td>



        <td colspan="5">
          <ng-container *ngIf="marker.type !== undefined">

            <!--SELECT LEVEL-->
            <!-- UNLOCK LEVEL-->
            <ng-container *ngIf="isRequired['levelId'] && (+marker.type == 2)">
              <label>Level Unlock</label><br />
              <select #selectedLevel class="filter-dropdown auto" [(ngModel)]="marker.levelId"
                (change)="onChange(selectedLevel.value);">
                <option *ngFor="let level of preloadedLevels" value="{{level.id}}">
                  {{ ([level.id] | location : false).toString() }}
                </option>
              </select>
            </ng-container>

            <!-- UNLOCK RELEASE DIALOG-->
            <ng-container *ngIf="isRequired['levelId'] && (+marker.type == 3)">
              <label>Level Release</label><br />
              <select #selectedLevel class="filter-dropdown auto" [(ngModel)]="marker.levelId"
                (change)="onChange(selectedLevel.value);">
                <option *ngFor="let level of listAreas" value="{{level.id}}">
                  {{level.locationLevel}}

                </option>
              </select>
            </ng-container>

            <ng-container *ngIf="isRequired['levelId'] && +marker.type == 7">
              <label>Level Pin</label><br />
              <select #selectedLevel class="filter-dropdown auto" [(ngModel)]="marker.levelId"
                (change)="onChange(selectedLevel.value);">
                <option *ngFor="let level of preloadedLevels" value="{{level.id}}">
                  {{ ([level.id] | location : false).toString() }}
                </option>
              </select>
            </ng-container>

            <ng-container *ngIf="isRequired['levelId'] && +marker.type == 12">
              <label>Level D</label><br />
              <select #selectedLevel class="filter-dropdown auto" [(ngModel)]="levelId"
                (change)="onChange(selectedLevel.value);">
                <option></option>
                <option *ngFor="let level of preloadedLevels" value="{{level.id}}">
                  {{ ([level.id] | location : false).toString() }}
                </option>
              </select>
            </ng-container>

            <!--SELECT CHARACTER-->
            <ng-container *ngIf="isRequired['characterId']">
              <label>Character</label><br />
              <select class="filter-dropdown auto" [(ngModel)]="marker.characterId" (change)="onChange();">
                <option></option>
                <option *ngFor="let character of preloadedCharacters" value="{{ character.id }}">
                  {{ character.name }}
                </option>
              </select>
            </ng-container>

            <!--RESTART DIALOGUE-->
            <ng-container *ngIf="marker.type == 10">
              <div class="unlock">
                <div style="flex: 0;">
                  <label>Unlock Condition</label><br />
                  <select class="filter-dropdown auto" #selectedItem [(ngModel)]="marker.unlockCondition"
                    (change)="unlockCondition(selectedItem.value);">
                    <option></option>
                    <option *ngFor="let type of typeNames; let i = index" [value]="type">
                      {{ type }}
                    </option>
                  </select>
                </div>

                <!--Search Box if Spoke In -->
                <ng-container *ngIf="selectedItem.value == 'Spoke In' ">
                  <div class="justSpokeSearch">
                    <input class="inputSearch" type="text" placeholder="Search Box"  
                    (ngModelChange)="filterSpokePlacesListItem($event)"
                    (ngModelChange)="filterSpokePlacesListItem($event)"
                    [readonly]="isDisabled" 
                    (focus)="enableSearch()" 
                    (blur)="disableSearchIfEmpty()" 
                    (mouseenter)="enableSearch()" 
                    (mouseleave)="disableSearchIfEmpty()">
                  </div>
                </ng-container>
                <!--Fim do Search-->
              </div>
            </ng-container>

                                             <!-- BLOCK_MAP = 11/ FINISH_DIALOGUE = 5/ UNLOCK_LEVEL = 2/ RELEASE_DIALOGUE = 3/ PIN = 7 -->
            <ng-container *ngIf="isCondition && ( marker.type != 11 && marker.type != 5 && marker.type != 2 && marker.type != 3 && marker.type != 7)">
              <div style="margin-top: 10px;">
                <div *ngIf="isSpokeIn">
                  <label>{{this.markerTitle}}</label><br />
                  <select class="filter-dropdown selectWidth" #selectedItem [(ngModel)]="marker.choosedCondition"
                    (change)="onChangeUnlockCondition(selectedItem.value);">
                    <option></option>
                    <option *ngFor="let spokePlace of displaySpokePlaces; let i = index" [value]="spokePlace.text">
                      {{ spokePlace.text }}
                    </option>
                  </select>
                </div>

                <div *ngIf="!isSpokeIn">
                  <label>{{this.markerTitle}}</label><br />
                  <select class="filter-dropdown selectWidth" #selectedItem [(ngModel)]="marker.choosedCondition"
                    (change)="onChangeUnlockCondition(selectedItem.value);">
                    <option></option>
                    <option *ngFor="let element of displayList; let i = index" [value]="element">
                      {{ element }}
                    </option>
                  </select>
                </div>

                <div class="unlock">
                  <ng-container *ngIf="this.markerTitle == 'ITEM REQUIREMENT'">
                    <div style="margin-top: 10px; width: 140px;">
                      <label>CONDITION OPERATOR</label><br />
                      <select style="width: 140px;" class="filter-dropdown" #selectedItem
                        [(ngModel)]="marker.conditionOperator" (change)="conditionOperator(selectedItem.value);">
                        <option></option>
                        <option *ngFor="let element of karmicTypes; let i = index" [value]="element"
                          [selected]="element?.trim()">
                          {{ element }}
                        </option>
                      </select>
                    </div>
                  </ng-container>

                  <ng-container
                    *ngIf="this.markerTitle == 'KARMIC EQUILIBRIUM' || this.markerTitle == 'ITEM REQUIREMENT'">
                    <div [ngClass]="this.markerTitle == 'ITEM REQUIREMENT' ? 'div-requirement': 'marg-karmic'">
                      <label>ITEM REQUIREMENT</label><br />
                      <input class="inputKarmic" type="number" #inputAmount min="0" [(ngModel)]="marker.amountRequired"
                        [value]="+this.marker.amountRequired" (change)="changeItemAmount(inputAmount.value)">
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-container>


            <ng-container *ngIf="isRequired['levelId'] && (+marker.type == 3)">
              <div style="margin-top: 10px; width: 180px; ">
                <div>
                  <label>Message contexts</label><i ngClass="iconInter" (click)="onModalClick()" class="pe-7s-help1 batt"></i><br />
                </div>           
                <select style="width: 100px;" class="filter-dropdown auto" [(ngModel)]="marker.contextType" (change)="onChangeType()">
                  <option [value]="'Empty'">Empty</option>
                  <option *ngFor="let contextType of unlockMarkerTypes" [value]="contextType">
                    {{ contextType }}
                  </option>
                </select>
                <br />
                <ng-container *ngIf="marker.contextType && marker.contextType != 'Empty'">
                  <select class="filter-dropdown auto" #selectedMessage (change)="onChangeMessage(selectedMessage.value)" [value]="isNameMessageType">
                    <option [value]=""></option>
                    <option *ngFor="let message of itemListMessageContext; let i = index" [value]="message.name" [selected]="message.name === isNameMessageType">
                     {{i+1}} - {{message.name}}
                    </option>
                  </select>
                </ng-container>
              </div>
            </ng-container>

          </ng-container>
        </td>



        <td colspan="5" style="padding-top: 35px;">
          <!--SET PIN-->
          <ng-container *ngIf="isRequired['pin']">
            <button class="btn btn-warning {{marker.pin ? 'btn-fill' : ''}}"
              (click)="marker.pin = !marker.pin; onChange();">
              Pin
            </button>
          </ng-container>
        </td>

        <!--REMOVE BUTTON-->
        <td style="padding-top: 35px;">
          <button class="btn btn-simple btn-fill btn-danger btn-remove" (click)="toRemove(marker)">
            <i style="font-size: 24px; margin: 0; padding: 0" class="pe-7s-close"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </table>

     <!--Modal Info-->
     <ng-container *ngIf="isModalInfo">
      <div class="background-div handleOut" aria-hidden="true">
          <div class="modal-backdrop" *ngIf="isModalInfo"></div> 
          <div id="modal-close" @popup class="popup-report" (mouseleave)="isModalInfo = false"
              style="background-color: black;">
              <div class="modal-header">
                  <div style="display: flex; justify-content: space-between;">
                      <p style="color:azure !important; text-align: center;" class="modal-title">Empty</p>
                      <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                          data-dismiss="background-div" aria-label="Fechar">
                          <span aria-hidden="true">&times;</span>
                      </button>
                  </div>
              </div>
              <div class="contextInfo">
                  <p class="modal-title title p-text">
                    💬 Você não deveria estar aqui; há algo importante acontecendo em outro lugar!
                  </p>
                  <p class="modal-title title p-text">
                    ➡️ Sem interação disponível. Pode indicar áreas bloqueadas ou eventos inativos.
                  </p>               
              </div>
           
              <div class="modal-header">
                <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">Character</p>
                </div>
              </div>
              <div class="contextInfo">
                <p class="modal-title title p-text">
                  💬 Marco Antônio não deseja falar com você no momento.
                </p>
                <p class="modal-title title p-text">
                  ➡️ Personagem indisponível. Pode exigir progresso narrativo.
                </p>               
              </div>
        
            <div class="modal-header">
              <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                  <p style="color:azure !important; text-align: center;" class="modal-title">Mission</p>
              </div>
            </div>
            <div class="contextInfo">
              <p class="modal-title title p-text">
                💬 Você ainda não completou a missão “As sandálias de Nanaya”. Volte quando tiver concluído.
              </p>
              <p class="modal-title title p-text">
                ➡️ Interação bloqueada até a missão ser concluída.
              </p>               
            </div>         
          </div>
      </div>
  </ng-container>
  <!--Fim do Modal-->
</td>

