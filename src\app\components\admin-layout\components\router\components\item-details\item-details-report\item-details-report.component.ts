import { Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { Item } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, WeaponService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-item-details-report',
  templateUrl: './item-details-report.component.html',
  styleUrls: ['./item-details-report.component.scss'],
})
export class ItemDetailsReportComponent implements OnInit 
{
  @ViewChildren('query') childGames: QueryList<ICollectibleDetails>;
  public activeTab: string;
  islinedUp:boolean;
  sortNameOrder = -1;
  custom: Custom;
  item: Item;
  characterFromQuery = '';
  collectibleFromQueryList = [];
  itemListLineup = [];
  characterListIndex = 0;
  initialIndex = 0;
  currentCharacter : Item;
  isModalInfo = false;

  constructor(
    protected _customService: CustomService,
    protected _itemService: ItemService,
    private _router: Router,
    private _activatedRoute : ActivatedRoute,
    private _weaponService : WeaponService
  )
  {}

  async ngOnInit(): Promise<void>
  {
    await this._itemService.toFinishLoading();
    await this._customService.toFinishLoading();
    this.characterFromQuery = this._weaponService.currentSelectedCharacter;
    //this.collectibleFromQueryList = await this._activatedRoute.snapshot.queryParams['collectibles'];
    this.collectibleFromQueryList = await this._activatedRoute.snapshot.queryParams['itemListLineup'];   
    this.custom = await this._customService.svcGetInstance();
    this.item = this._itemService.svcFindById(this.custom.selectedWeaponId);
    this.currentCharacter = this._itemService.svcFindById(this.characterFromQuery); 
    
   
    for(let i = 0; i < this.collectibleFromQueryList.length; i++)
    {//compares the position of the id
      if(this.collectibleFromQueryList[i] == this._weaponService.currentSelectedCharacter)
      {
        this.initialIndex = i;
        this.characterListIndex = i;
        break;
      }
    }  
    const tab = localStorage.getItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'class-selection' : tab;
    this.resetAll();
    this.switchToTab('general');
  }

   public async switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

  public onBack()
  {
    this._router.navigate(['weaponRecords']);
  }

  public readonly leftButtonTemplate: Button.Templateable = 
  {
    title: 'Back to last character',
    onClick: this.leftButton.bind(this),
    iconClass: 'pe-7s-angle-left',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  public readonly rightButtonTemplate: Button.Templateable = 
  {
    title: 'Go to next character',
    onClick: this.rightButton.bind(this),
    iconClass: 'pe-7s-angle-right',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  //back
leftButton() {
  // Se o índice atual for 0, vai para o último item da lista
  if (this.characterListIndex === 0) {
    this.characterListIndex = this.collectibleFromQueryList.length - 1;
  } else {
    // Caso contrário, decrementa o índice
    this.characterListIndex = this.characterListIndex - 1;
  }

  // Atualiza o personagem atual
  let nextCharacter = this.collectibleFromQueryList[this.characterListIndex];
  this.currentCharacter = this._itemService.svcFindById(nextCharacter);
  this._weaponService.currentSelectedCharacter = nextCharacter;
  this._customService.currentSelectedItem = nextCharacter;
  this.resetAll();
}

//next
rightButton() {
  // Se o índice atual for o último da lista, volta para o primeiro item
  if (this.characterListIndex === this.collectibleFromQueryList.length - 1) {
    this.characterListIndex = 0;
  } else {
    // Caso contrário, incrementa o índice
    this.characterListIndex = this.characterListIndex + 1;
  }

  // Atualiza o personagem atual
  let nextCharacter = this.collectibleFromQueryList[this.characterListIndex];
  this.currentCharacter = this._itemService.svcFindById(nextCharacter);
  this._weaponService.currentSelectedCharacter = nextCharacter;
  this._customService.currentSelectedItem = nextCharacter;
  this.resetAll();
}

  //For more information look the method name on documentation.
  resetAll() 
  { 
    this.childGames.forEach(c => c.reset(this._weaponService.currentSelectedCharacter));
  }
  
  closeAreaStatsPopup() {
    this.isModalInfo =  false;
  }
  //modal
  onModalClick(): void {  
    this.isModalInfo = !this.isModalInfo;

   }
}
