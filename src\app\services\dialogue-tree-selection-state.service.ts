import { Injectable } from '@angular/core';
import { OptionService } from './option.service';
import { DilemmaService } from './dilemma.service';

/**
 * Service for managing selection state logic in dialogue trees.
 * Handles choice, investigation, and dilemma option selection logic and validation.
 */
@Injectable({
  providedIn: 'root'
})
export class DialogueTreeSelectionStateService {

  constructor(
    private _optionService: OptionService,
    private _dilemmaService: DilemmaService
  ) {}

  /**
   * Check if an option is currently selected (for choice options)
   */
  isOptionSelected(
    optionId: string,
    parentOptionBoxId: string,
    selectedChoiceOptions: Map<string, string>
  ): boolean {
    if (!optionId || !parentOptionBoxId) return false;
    return selectedChoiceOptions?.get(parentOptionBoxId) === optionId;
  }

  /**
   * Check if an investigation option is currently selected
   */
  isInvestigationOptionSelected(
    optionId: string,
    parentOptionBoxId: string,
    selectedInvestigationOptions: Map<string, Set<string>>
  ): boolean {
    if (!optionId || !parentOptionBoxId) return false;
    const selectedOptions = selectedInvestigationOptions?.get(parentOptionBoxId);
    return selectedOptions?.has(optionId) || false;
  }

  /**
   * Check if a dilemma option is currently selected
   */
  isDilemmaOptionSelected(
    dilemmaId: string,
    selectedDilemmaOptions: Map<string, string>
  ): boolean {
    if (!dilemmaId || !selectedDilemmaOptions || selectedDilemmaOptions.size === 0) {
      return false;
    }

    // Check if this dilemma ID appears in any of the selections
    // The selection map uses AnswerDilemmaBox IDs as keys, but we just need to find our dilemma ID
    for (const [, selectedDilemmaId] of selectedDilemmaOptions.entries()) {
      if (selectedDilemmaId === dilemmaId) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if any option is selected for a choice option's parent box
   */
  hasChoiceParentSelection(
    parentOptionBoxId: string,
    selectedChoiceOptions: Map<string, string>
  ): boolean {
    if (!parentOptionBoxId) return false;
    return selectedChoiceOptions?.has(parentOptionBoxId) || false;
  }

  /**
   * Check if any dilemma is selected in the same dilemma group as this dilemma
   * Since each dilemma has its own dilemma box, we group them by their common prefix
   */
  hasDilemmaParentSelection(
    dilemmaId: string,
    selectedDilemmaOptions: Map<string, string>
  ): boolean {
    if (!dilemmaId || !selectedDilemmaOptions || selectedDilemmaOptions.size === 0) {
      return false;
    }

    // Extract the dialogue layer prefix from this dilemma's ID
    // e.g., "A23.L2044.D0_PT-BR.DTL1.dlm5" -> "A23.L2044.D0_PT-BR.DTL1"
    const layerPrefix = dilemmaId.substring(0, dilemmaId.lastIndexOf('.dlm'));

    // Check if any selected dilemma shares the same layer prefix
    for (const [, selectedDilemmaId] of selectedDilemmaOptions) {
      if (selectedDilemmaId && selectedDilemmaId.startsWith(layerPrefix + '.dlm')) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if an option should be hidden based on selections
   * Investigation options should never be hidden by selection logic
   * Choice and dilemma options use single-selection logic
   */
  shouldHideOptionBasedOnSelection(
    leafType: string,
    optionId: string,
    dilemmaId: string,
    parentOptionBoxId: string,
    selectedChoiceOptions: Map<string, string>,
    selectedDilemmaOptions: Map<string, string>
  ): boolean {
    // Investigation options should never be hidden by selection logic
    // They only get hidden by roadblock evaluation
    if (leafType === 'InvestigationOption') return false;

    // Choice and dilemma options use the same single-selection logic
    if (leafType !== 'ChoiceOption' && leafType !== 'DilemmaOption') return false;

    // For choice options, check choice parent selection
    if (leafType === 'ChoiceOption') {
      // If no selection is made for this option's parent, show all options
      if (!this.hasChoiceParentSelection(parentOptionBoxId, selectedChoiceOptions)) return false;

      // IMPORTANT: If this option is selected, NEVER hide it
      if (this.isOptionSelected(optionId, parentOptionBoxId, selectedChoiceOptions)) {
        return false;
      }

      // If another option is selected in the same choice box, hide this one
      return this.hasChoiceParentSelection(parentOptionBoxId, selectedChoiceOptions) && 
             !this.isOptionSelected(optionId, parentOptionBoxId, selectedChoiceOptions);
    }

    // For dilemma options, check dilemma parent selection
    if (leafType === 'DilemmaOption') {
      const hasParentSelection = this.hasDilemmaParentSelection(dilemmaId, selectedDilemmaOptions);
      const isSelected = this.isDilemmaOptionSelected(dilemmaId, selectedDilemmaOptions);

      // If no selection is made for this dilemma's parent, show all options
      if (!hasParentSelection) return false;

      // IMPORTANT: If this dilemma is selected, NEVER hide it
      if (isSelected) {
        return false;
      }

      // If another dilemma is selected in the same dilemma box, hide this one
      return hasParentSelection && !isSelected;
    }

    return false;
  }

  /**
   * Get the parent option box ID for an option
   */
  getParentOptionBoxId(optionId: string): string | undefined {
    if (!optionId) return undefined;

    // The option ID typically contains the parent box ID
    // Format is usually like: A8.L1794.D0_PT-BR.OB1265.opt3453
    // We need to extract the OB part
    const idParts = optionId.split('.');
    for (const part of idParts) {
      if (part.startsWith('OB')) {
        // Reconstruct the full option box ID
        const boxIdParts = optionId.split('.opt')[0];
        return boxIdParts;
      }
    }

    return undefined;
  }

  /**
   * Get the parent dilemma box ID for a dilemma option
   */
  getParentDilemmaBoxId(dilemmaId: string): string | undefined {
    if (!dilemmaId) return undefined;

    // The dilemma ID typically contains the parent box ID
    // Format is usually like: A23.L2044.D0_PT-BR.DIL1.dlm5
    // We need to extract everything before the .dlm part
    const lastDlmIndex = dilemmaId.lastIndexOf('.dlm');

    if (lastDlmIndex !== -1) {
      return dilemmaId.substring(0, lastDlmIndex);
    }

    return undefined;
  }

  /**
   * Check if a leaf represents a choice option that can be clicked
   */
  isChoiceOption(leafType: string): boolean {
    return leafType === 'ChoiceOption';
  }

  /**
   * Check if a leaf represents an investigation option that can be clicked
   */
  isInvestigationOption(leafType: string): boolean {
    return leafType === 'InvestigationOption';
  }

  /**
   * Check if a leaf represents a dilemma option that can be clicked
   */
  isDilemmaOption(leafType: string): boolean {
    return leafType === 'DilemmaOption';
  }

  /**
   * Check if a leaf represents any type of dialogue option (including dice failures)
   */
  isDialogueOption(leafType: string): boolean {
    return leafType === 'ChoiceOption' ||
           leafType === 'InvestigationOption' ||
           leafType === 'DilemmaOption' ||
           leafType === 'ChoiceFailure' ||
           leafType === 'InvestigationFailure' ||
           leafType === 'DiceFailure';
  }

  /**
   * Check if a leaf represents an Option type (ChoiceOption, InvestigationOption, DilemmaOption, or dice failures)
   */
  isOptionType(leafType: string): boolean {
    return leafType === 'ChoiceOption' ||
           leafType === 'InvestigationOption' ||
           leafType === 'DilemmaOption' ||
           leafType === 'ChoiceFailure' ||
           leafType === 'InvestigationFailure' ||
           leafType === 'DiceFailure';
  }

  /**
   * Validate and emit choice option selection event
   * If the option is already selected, unselect it. Otherwise, select it.
   */
  handleChoiceOptionClick(
    optionId: string,
    parentOptionBoxId: string,
    selectedChoiceOptions: Map<string, string>,
    emitCallback: (data: {optionBoxId: string, optionId: string | null}) => void
  ): void {
    if (!optionId || !parentOptionBoxId) return;

    // Check if this option is currently selected
    const isCurrentlySelected = this.isOptionSelected(optionId, parentOptionBoxId, selectedChoiceOptions);
    
    if (isCurrentlySelected) {
      // Unselect by emitting with null optionId
      emitCallback({
        optionBoxId: parentOptionBoxId,
        optionId: null
      });
    } else {
      // Select the option
      emitCallback({
        optionBoxId: parentOptionBoxId,
        optionId: optionId
      });
    }
  }

  /**
   * Validate and emit investigation option selection event
   * Investigation options allow multiple selections, so toggle the selection state
   */
  handleInvestigationOptionClick(
    optionId: string,
    parentOptionBoxId: string,
    selectedInvestigationOptions: Map<string, Set<string>>,
    emitCallback: (data: {optionBoxId: string, optionId: string, selected: boolean}) => void
  ): void {
    if (!optionId || !parentOptionBoxId) return;

    // Check if this option is currently selected
    const isCurrentlySelected = this.isInvestigationOptionSelected(optionId, parentOptionBoxId, selectedInvestigationOptions);

    // Toggle the selection
    emitCallback({
      optionBoxId: parentOptionBoxId,
      optionId: optionId,
      selected: !isCurrentlySelected
    });
  }

  /**
   * Validate and emit dilemma option selection event
   * If the option is already selected, unselect it. Otherwise, select it.
   */
  handleDilemmaOptionClick(
    dilemmaId: string,
    parentDilemmaBoxId: string,
    selectedDilemmaOptions: Map<string, string>,
    emitCallback: (data: {dilemmaBoxId: string, dilemmaId: string | null}) => void
  ): void {
    if (!dilemmaId || !parentDilemmaBoxId) return;

    // Check if this dilemma is currently selected
    const isCurrentlySelected = this.isDilemmaOptionSelected(dilemmaId, selectedDilemmaOptions);
    
    if (isCurrentlySelected) {
      // Unselect this dilemma
      emitCallback({ dilemmaBoxId: parentDilemmaBoxId, dilemmaId: null });
    } else {
      // Select this dilemma (will automatically unselect others in the same box)
      emitCallback({ dilemmaBoxId: parentDilemmaBoxId, dilemmaId: dilemmaId });
    }
  }

  /**
   * Get parent option box ID from an option object
   */
  getParentOptionBoxIdFromOption(option: any): string | undefined {
    if (!option?.id) return undefined;

    // Extract parent box ID from option ID
    const boxIdParts = option.id.split('.opt')[0];
    return boxIdParts;
  }
}
