import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DiceOverlayService, DiceOverlayState } from '../../../services/dice-overlay.service';

/**
 * Dice overlay component that displays the 3D dice rolling interface.
 * Implements a Baldur's Gate 3 style dice rolling experience with:
 * - 3D animated d20 dice with clickable interaction
 * - Success/failure result display with critical hit/miss detection
 * - Ornate fantasy-themed UI frame with decorative borders
 * - Smooth animations and visual feedback
 */
@Component({
  selector: 'app-dice-overlay',
  templateUrl: './dice-overlay.component.html',
  styleUrls: ['./dice-overlay.component.scss']
})
export class DiceOverlayComponent implements OnInit, OnDestroy {
  // Current state of the dice overlay received from service
  public overlayState: DiceOverlayState = {
    isVisible: false,
    result: null,
    dc: 10,
    modifiers: []
  };

  // Animation state tracking for dice behavior
  public isRolling: boolean = false;  // True during dice roll animation
  public hasRolled: boolean = false;  // True after dice has been rolled
  public diceTransform: string = 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)'; // 3D transform for dice

  // Subscription management
  private subscription: Subscription = new Subscription();
  private rollAnimationTimeout?: number; // Timeout for roll animation completion

  constructor(private diceOverlayService: DiceOverlayService) { }

  /**
   * Initialize component and subscribe to dice overlay state changes
   */
  ngOnInit(): void {
    // Subscribe to overlay state changes from the service
    this.subscription = this.diceOverlayService.overlayState$.subscribe(
      state => {
        const wasVisible = this.overlayState.isVisible;
        console.log('Dice overlay component received state update:', {
          state,
          wasVisible,
          currentHasRolled: this.hasRolled,
          currentIsRolling: this.isRolling
        });

        this.overlayState = state;

        // Reset animation states when starting a new dice roll
        if (state.isVisible && !wasVisible) {
          console.log('New dice roll started - resetting dice state');
          this.resetDiceState();
        }

        // Complete the roll animation if we receive a result while rolling
        if (state.result && this.isRolling) {
          console.log('Received dice result while rolling - finishing roll');
          this.finishDiceRoll();
        }
      }
    );
  }

  /**
   * Clean up subscriptions and timeouts when component is destroyed
   */
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.rollAnimationTimeout) {
      clearTimeout(this.rollAnimationTimeout);
    }
  }

  /**
   * Handle clicks on the overlay background to dismiss after roll completion
   * @param event Click event on the overlay background
   */
  onOverlayClick(event: Event): void {
    console.log('Dice overlay clicked:', { hasRolled: this.hasRolled, result: this.overlayState.result, isRolling: this.isRolling });

    // Only allow dismissal after the roll is complete and result is shown
    if (this.hasRolled && this.overlayState.result) {
      console.log('Hiding dice overlay...');
      this.diceOverlayService.hideOverlay();
    }
  }

  /**
   * Handle clicks on the dice to initiate rolling
   * @param event Click event on the dice element
   */
  onDiceClick(event: Event): void {
    event.stopPropagation(); // Prevent triggering overlay click
    console.log('Dice clicked:', { isRolling: this.isRolling, hasRolled: this.hasRolled });

    // Only allow rolling if dice hasn't been rolled yet
    if (!this.isRolling && !this.hasRolled) {
      console.log('Starting dice roll...');
      this.startDiceRoll();
    }
    // Allow dismissal by clicking dice after roll is complete
    else if (this.hasRolled && this.overlayState.result) {
      console.log('Hiding dice overlay from dice click...');
      this.diceOverlayService.hideOverlay();
    }
  }

  /**
   * Reset dice to initial state for a new roll
   */
  private resetDiceState(): void {
    this.isRolling = false;
    this.hasRolled = false;
    this.diceTransform = 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';
  }

  /**
   * Initiate the dice rolling process
   * Triggers the service to calculate the roll result
   */
  private startDiceRoll(): void {
    this.isRolling = true;

    // Execute the actual dice roll calculation in the service
    // The result will be received via subscription and finishDiceRoll() will be called
    this.diceOverlayService.executeDiceRoll();
  }

  /**
   * Complete the dice roll animation and display the result
   */
  private finishDiceRoll(): void {
    this.isRolling = false;
    this.hasRolled = true;

    console.log('Dice roll finished:', {
      result: this.overlayState.result,
      hasRolled: this.hasRolled,
      isRolling: this.isRolling,
      overlayState: this.overlayState
    });

    // Force change detection to ensure UI updates
    setTimeout(() => {
      console.log('After timeout - overlayState:', this.overlayState);
    }, 100);
  }

  /**
   * Get 3D rotation transform for displaying a specific dice face
   * Maps dice roll numbers (1-20) to 3D CSS transforms for proper face orientation
   * @param number The dice roll result (1-20)
   * @returns CSS transform string for 3D rotation
   */
  private getDiceRotationForNumber(number: number): string {
    // Rotation mapping for each face of the d20 dice
    // Each rotation positions the dice to show the corresponding number face-up
    const rotations: { [key: number]: string } = {
      1: 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)',        // Default position
      2: 'rotateX(90deg) rotateY(0deg) rotateZ(0deg)',       // Rotate around X-axis
      3: 'rotateX(180deg) rotateY(0deg) rotateZ(0deg)',      // 180° X rotation
      4: 'rotateX(270deg) rotateY(0deg) rotateZ(0deg)',      // 270° X rotation
      5: 'rotateX(0deg) rotateY(90deg) rotateZ(0deg)',       // Rotate around Y-axis
      6: 'rotateX(0deg) rotateY(180deg) rotateZ(0deg)',      // 180° Y rotation
      7: 'rotateX(0deg) rotateY(270deg) rotateZ(0deg)',      // 270° Y rotation
      8: 'rotateX(90deg) rotateY(90deg) rotateZ(0deg)',      // Combined X+Y rotation
      9: 'rotateX(90deg) rotateY(180deg) rotateZ(0deg)',     // X+Y rotation variant
      10: 'rotateX(90deg) rotateY(270deg) rotateZ(0deg)',    // X+Y rotation variant
      11: 'rotateX(180deg) rotateY(90deg) rotateZ(0deg)',    // X+Y rotation variant
      12: 'rotateX(180deg) rotateY(180deg) rotateZ(0deg)',   // X+Y rotation variant
      13: 'rotateX(180deg) rotateY(270deg) rotateZ(0deg)',   // X+Y rotation variant
      14: 'rotateX(270deg) rotateY(90deg) rotateZ(0deg)',    // X+Y rotation variant
      15: 'rotateX(270deg) rotateY(180deg) rotateZ(0deg)',   // X+Y rotation variant
      16: 'rotateX(270deg) rotateY(270deg) rotateZ(0deg)',   // X+Y rotation variant
      17: 'rotateX(45deg) rotateY(45deg) rotateZ(45deg)',    // Diagonal faces
      18: 'rotateX(135deg) rotateY(45deg) rotateZ(45deg)',   // Diagonal faces
      19: 'rotateX(225deg) rotateY(45deg) rotateZ(45deg)',   // Diagonal faces
      20: 'rotateX(315deg) rotateY(45deg) rotateZ(45deg)'    // Diagonal faces
    };

    // Return the rotation for the number, or default position if not found
    return rotations[number] || 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';
  }
}
