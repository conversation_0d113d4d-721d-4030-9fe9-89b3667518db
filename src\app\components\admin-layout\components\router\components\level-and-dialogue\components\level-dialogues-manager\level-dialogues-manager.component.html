<div class="main-content">
    <div class="card list-header-row m-dialogue">
      <app-header-with-buttons
        class="card"
        [isBackButtonEnabled]="true"
        [cardTitle]="([level.id] | location)[0]"
        [cardDescription]="headerDescription"
        (cardBackButtonClick)="onCardBackButtonClick()"
        [rightButtonTemplates]="headerRightButtonTemplates">
      </app-header-with-buttons>
    </div>


    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="content table-responsive table-full-width">
            <table class="table table-hover table-striped">
              <thead>
                <tr>
                  <th>ID</th>
                  <th></th>
                  <th></th>
                  <th>Name</th>
                  <th></th>
                  <th>Total Boxes</th>
                  <th *ngIf="level.speakerIds.length > 0 || (level | information)?.enablesDialogue">
                    Edit
                  </th>
                  <th>Story Expansion</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let dialogue of dialogues; let i = index; trackBy: trackByIndex">
                  <tr id="{{ dialogues[i].id }}">
                    <td class="td-id">{{ dialogues[i].id }}</td>
                    <td class="td-20px">
                      <i [class]="dialogues[i] | dialogueIcon"></i>
                    </td>
                    <td style="height: 100px; padding-top: 0; padding-bottom: 0">
                      <div [ngStyle]="dialogues[i] | dialogueStyle"></div>
                    </td>
                    <td>{{ dialogues[i] | dialogueTypeDisplay }}</td>
                    <td>
                      {{
                        dialogue.type === DialogueType.RELEASE ? 
                        (level | review).unlockedMissionDialogueBy.length > 0 ? 'Unlocked by "' +
                        ((level | review).unlockedMissionDialogueBy | location).toString() + '"'
                        : "Not set for releasal." : ""
                      }}
                    </td>
                    <td>{{ dialogues[i].boxIds?.length }}</td>
                    <td style="display: flex;">
                      <button 
                          *ngIf="level.speakerIds.length > 0 || (level | information)?.enablesDialogue"
                          [ngClass]="dialogues[i].boxIds?.length > 0 ? 'btn btn-fill btn-info' : 'btn btn-fill btn-default'"
                          (click)="redirectToDialogue(dialogues[i])">
                        <i class="pe-7s-pen"></i>
                      </button>
                      <button 
                          class="btn btn-gray btn-fill translation-button"
                          (click)="downloadTextContent(dialogue)"
                          *ngIf="dialogues[i].boxIds?.length > 0">
                        <div class="mat-translate"></div>
                      </button>                     
                    </td>
                    <td class="td-sort">             
                      <button	
                          *ngIf="dialogue.type === DialogueType.RELEASE || dialogue.type === DialogueType.RETURN || dialogue.type === DialogueType.SPECIAL_RETURN"	
                          style="width: 75px; height: 60px;"	
                          [ngClass]="(dialogues[i]?.id | storyExpansionBtn) ? 'btn btn-fill btn-warning ng-star-inserted' : 'btn btn-fill no-button btn-default ng-star-inserted'"	
                          (click)="addStoryExpansionContent(dialogues[i].id)">	
                        <i class="pe-7s-notebook" style="font-size: 40px;"></i>	
                      </button>
                    </td>
                  </tr>
                  <ng-container *ngIf="(dialogues[i] | dialogueTypeName) === 'Init'|| (dialogues[i] | dialogueTypeName) === 'Return'">
                    <ng-container *ngIf="+level.type === LevelType.MINION || +level.type === LevelType.BOSS">
                  
                      <tr>
                        <td></td>
                        <td>
                          <i style="cursor: pointer"
                             (click)="toPromptAddBattleCharacterToLevel()"
                             class="pe-7s-joy icon-large info"></i>
                        </td>
                        <td colspan="2"
                            class="manager-battle-characters-td">
                          <app-battle-characters-table [level]="level"></app-battle-characters-table>
                        </td>
                        <td>
                          <ng-container *ngIf=" (dialogues[i] | dialogueTypeName) === 'Return'">
                            <button
                                [disabled]="this.canChangeBossButtonColor"
                                *ngIf="+level.type === LevelType.MINION || +level.type === LevelType.BOSS; else disableGrindButton"
                                class="btn btn-fill btn-75px pull-left"
                                title="{{this.blockBossPhrase}}"
                                [ngClass]="level.blockGrind ? this.canChangeBossButtonColor ? 'btn-warning' : 'btn-danger' : 'btn-success'"
                                (click)="blockGrind()">
                              <i [ngClass]="level.blockGrind ? 'pe-7s-shield' : 'pe-7s-check'"></i>
                            </button>

                            <ng-template #disableGrindButton >
                              <button disabled class="btn btn-fill btn-default pull-right">
                                <i [ngClass]="level.blockGrind ? 'pe-7s-shield' : 'pe-7s-check'"></i>
                              </button>
                            </ng-template>

                            <button 
                                [disabled]="this.canChangeSpecialItemButtonColor"
                                *ngIf="dialogue.type === DialogueType.RETURN "
                                title="{{this.removeSpecialItemPhrase}}"
                                class="btn btn-fill btn-75px pull-left"
                                [ngClass]="level.removeSpecialItemBeforeGrind ? this.canChangeSpecialItemButtonColor ? 'btn-warning' : 'btn-danger' : 'btn-hidden'"
                                (click)="removeSpecialItemBeforeGrind()">
                              <i class="pe-7s-scissors"></i>
                            </button>
                          </ng-container>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </tbody>
            </table>
          </div>
          <div>
          </div>
        </div>
      </div>
    </div>

