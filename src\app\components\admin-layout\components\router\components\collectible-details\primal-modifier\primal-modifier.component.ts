import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { PrimalModifier } from 'src/app/lib/@bus-tier/models/PrimalModifier';
import { Button } from 'src/app/lib/@pres-tier/data';
import { BattleUpgradeService, CharacterService, ClassService, ModifierService, PrimalModifierService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { Primal } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { BattleUpgrade } from '../../../../../../../lib/@bus-tier/models/BattleUpgrade';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-primal-modifier',
  templateUrl: './primal-modifier.component.html',
  styleUrls: ['./primal-modifier.component.scss'],
})

export class PrimalModifierComponent extends SortableListComponent<PrimalModifier> implements ICollectibleDetails {
  @Input() character = '';
  currentModifier: PrimalModifier;
  primalModifier: PrimalModifier;
  primalList: PrimalModifier;
  primals: Primal[] = [];
  currentCharacter: BattleUpgrade;
  valueBossLevel: string;
  nameClass: string;
  nameRarity: string;
  type: string;

  constructor(
    protected _customService: CustomService,
    _activatedRoute: ActivatedRoute,
    private ref: ChangeDetectorRef,
    protected _primalModifierService: PrimalModifierService,
    _userSettingsService: UserSettingsService,
    private _modifierService: ModifierService,
    protected _battleUpgradeService: BattleUpgradeService,
    private _characterService: CharacterService,
    private _classService: ClassService,
  ) {
    super(_primalModifierService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable =
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };

  override async ngOnInit(): Promise<void> {
    await this._primalModifierService.toFinishLoading();
    this.currentModifier = this._primalModifierService.createNewPrimalModifier(this.character);

    // this.currentModifier.hard.primalModifier = [];    
    this.createPrimalModifier();


  }


  async createPrimalModifier() {
    this.primals = [];
    await this._modifierService.toFinishLoading();
    this.primalModifier = this._primalModifierService.createNewPrimalModifier(this.character);
    this.primalModifier.hard.primalModifier = [];

    //.1 Try to find old primal modifier
    for (let i = 0; i < this._primalModifierService.models.length; i++) {
      if (this._primalModifierService.models[i].character == this.character && this._primalModifierService.models[i].hard.primalModifier) {
        this.primalModifier = this._primalModifierService.models[i];
        break;
      }
    }

    //2. Put the modifiers fields in the primal modifier
    //2.1 If currentModifier exists add new fields
    if (this.primalModifier?.hard?.primalModifier.length > 0) {
      for (let i = 0; i < this._modifierService.models.length; i++) {
        if (this.primalModifier.hard.primalModifier.length > 0)
          for (let j = 0; j < this.primalModifier.hard.primalModifier.length; j++) {
            if (this.primalModifier.hard.primalModifier[j].fieldName == this._modifierService.models[i].skill) break;
            if (j == this._modifierService.models.length - 1) {
              let primal: Primal = { fieldName: this._modifierService.models[i].skill, fieldValue: undefined };
              this.primalModifier.hard.primalModifier.push(primal);
            }
          }
        else {
          let primal: Primal = { fieldName: this._modifierService.models[i].skill, fieldValue: undefined };
          this.primalModifier.hard.primalModifier.push(primal);
        }
      }
    }
    else {
      //4. If the currentModifier does not exist create a new one
      for (let i = 0; i < this._modifierService.models.length; i++) {
        let primal: Primal = { fieldName: this._modifierService.models[i].skill, fieldValue: undefined };
        this.primalModifier.hard.primalModifier.push(primal);
      }
    }


    this.currentModifier = this.primalModifier;
    for (let i = 0; i < this._modifierService.models.length; i++) {
      let primal: Primal = { fieldName: this._modifierService.models[i].skill, fieldValue: undefined }
      this.primals.push(primal);
    }
    let valueCharacter = this.currentCharacter === undefined ? this.character : this.currentCharacter.character;

      setTimeout(() => {
      if (this.currentCharacter !== undefined) {
        const op = this.currentCharacter.bl != undefined ? this.currentCharacter.bl : 0;
        this.valueBossLevel = `BL: ${op}`;
      } else {
        this.valueBossLevel = 'BL: 0';
      }

      const character = this._characterService.models.find(x => x.id === valueCharacter);
      this.nameRarity = character?.rarity;
      this.nameClass = this._classService.models.find(x => x.id === character?.classId)?.name;
      this.type = GameTypes.characterTypeName[character.type];
    }, 100)


    // Adiciona '%' nos 3 últimos opções do select
    this.currentModifier.primalModifier = this.currentModifier.primalModifier.map((item, index) => {
      if (index >= this.currentModifier.primalModifier.length - 3) {
        item.percentage = '%';
      }
      return item;
    });

    await this._primalModifierService.svcToModify(this.currentModifier);
    await this._primalModifierService.toSave();
    this.ref.detectChanges();
  }

  async changePrimalValue(value: string, index: number, primal: Primal) {
    let createdPrimals = new Set();
    let allPrimals = [].concat(...this.currentModifier.hard.primalModifier.map(pm => pm.fieldName));

    allPrimals.forEach(primal => createdPrimals.add(primal));
    if (!this.currentModifier.hard.primalModifier[index]?.fieldName && !createdPrimals.has(primal.fieldName)) {
      let primall: Primal = { fieldName: primal.fieldName, fieldValue: value == '' ? undefined : +value };
      this.currentModifier.hard.primalModifier.push(primall);
    }
    else this.currentModifier.hard.primalModifier[index].fieldValue = value == '' ? undefined : +value;

    await this._primalModifierService.svcToModify(this.currentModifier);
    await this._primalModifierService.toSave();
  }

  async onExcelPaste(): Promise<void> {
    // this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/);
    lines.filter((l) => l !== "");

    if (this.displayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);
      cols = cols.filter((col) => col !== "");

      this.primalList = this._primalModifierService.models.find((i) => i.character === this.character);
      let primal = this.primals.find((x) => x.fieldName === cols[0]?.split('')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.'));

      if (!primal) continue;

      if (cols[1]?.trim()) {
        primal.fieldValue = +(cols[1].split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.'));
      }
      else {
        primal.fieldValue = undefined;
      }

      this.primalList.primalModifier = [];
      this.primalList.primalModifier.push(primal);
    }
    this.primalList.primalModifier = this.primals;
    this._primalModifierService.svcToModify(this.primalList);
    this._primalModifierService.toSave();
    Alert.ShowSuccess('Primal Modifier successfully!');


    this.lstFetchLists();
    this.ref.detectChanges();
    //this.spinnerService.setState(false);
    this.ngOnInit();
  }

  isPrimalModifierOnPrimals = (cols: string[], i: number): boolean => cols[0]?.trim().toLowerCase() == this.primals[i].fieldName.toLowerCase();

  displayErrors(array) {
    let count = array[0].split(/\t/);
    if (count.length < 2) {
      Alert.showError("Copy the MODIFIERS column values too!");
      return true;
    }

    if (count[0] === "") {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }

  reset(character) {
    this.character = character;
    this.ngOnInit();
  }

  async changePercentage(primalModifier: PrimalModifier, value: any, index: number) {
    primalModifier.hard.primalModifier[index]['percentage'] = value;
    await this._primalModifierService.svcToModify(primalModifier);
    await this._primalModifierService.toSave();
  }
}
