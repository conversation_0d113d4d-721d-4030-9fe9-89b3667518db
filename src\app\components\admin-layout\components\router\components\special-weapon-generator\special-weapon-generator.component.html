<div class="card list-header" style="height: 70px; margin: 15px 30px 0;">
  <div class="header">
    <button class="{{activeTab === 'item-class' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
      (click)="switchToTab('item-class')">
      1 - Item Class
    </button>
    <button class="{{activeTab === 'special-weapons' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
      (click)="switchToTab('special-weapons')">
      2 - Special Weapons
    </button>
  </div>
</div>

<app-special-weapon-class-selection *ngIf="activeTab === 'item-class'"> </app-special-weapon-class-selection>
<app-special-weapon-information *ngIf="activeTab === 'special-weapons'"> </app-special-weapon-information>