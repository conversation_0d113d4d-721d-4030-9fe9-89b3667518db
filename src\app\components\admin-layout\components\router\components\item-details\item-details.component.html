
<div class="card list-header" style="height: 70px; margin-top: 10px; margin-bottom: 0px; margin-left: 30px; margin-right: 30px;">
  <div class="header" style="display: flex; flex-direction: row; justify-content: space-between !important;">
    <div>
      <button class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToTab('class-selection')">
          1 - Item Class
      </button>
      <button class="{{activeTab === 'item-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToTab('item-selection')">
          2 - Weapon Selection
      </button>
    </div>
    <div>
      <button class="{{activeTab === 'weaponRarity' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToTab('weaponRarity')">
          3 - Weapons Rarity
      </button>
      <button class="{{activeTab === 'weaponWLBase' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToTab('weaponWLBase')">
          4 - Weapons WLBase
      </button>
    </div>
  </div>
</div>

<app-item-details-class-selection *ngIf="activeTab === 'class-selection'"> </app-item-details-class-selection>
<app-item-details-item-selection *ngIf="activeTab === 'item-selection'" (itemSelected)="switchToTab('item-record')"> </app-item-details-item-selection>
<app-weapon-rarity *ngIf="activeTab === 'weaponRarity'"></app-weapon-rarity>
<app-weapons-wlbase *ngIf="activeTab === 'weaponWLBase'"></app-weapons-wlbase>

