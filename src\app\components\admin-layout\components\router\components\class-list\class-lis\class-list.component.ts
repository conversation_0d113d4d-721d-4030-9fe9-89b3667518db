import { Voices, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { ChangeDetectorRef, Component } from '@angular/core';
import { ClassService } from 'src/app/services/class.service';
import { Class } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { ReviewService } from 'src/app/services/review.service';
import { Popup } from 'src/lib/darkcloud';
import { PopupService } from 'src/app/services';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { Button } from 'src/app/lib/@pres-tier/data';
import * as XLSX from 'xlsx';
import { ArchetypeListService } from 'src/app/services/archetypesList.service';
import { ArchetypeList } from 'src/app/lib/@bus-tier/models/ArchetypeList';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-class-list',
  templateUrl: './class-list.component.html',
  styleUrls: ['./class-list.component.scss']
})
/**
 * Displays and edits class data as a list
 */
export class ClassListComponent extends TranslatableListComponent<Class> {

  public readonly exportExcelButtonTemplate: Button.Templateable = {
    btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
    title: 'Export to Excel',
    onClick: this.downloadAsExcel.bind(this),
    iconClass: 'pe-7s-cloud-download',
  }
  public readonly tagsButton: Button.Templateable = 
  {
    title: 'Go to the Tags List',
    onClick: this.goToTags.bind(this),
    iconClass: 'pe-7s-ticket',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  public readonly Voices = Voices;
  listArchetype: ArchetypeList[] = [];
  listClass: Class[] = [];
  description: string;

  constructor(
      private _router: Router,
    _activatedRoute: ActivatedRoute,
    private _classService: ClassService,
    private _archetypeListService: ArchetypeListService,
    _userSettingsService: UserSettingsService,
    private _reviewService: ReviewService,
    private _popupService: PopupService,
    private _change: ChangeDetectorRef,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService
  ) {
    super(_classService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  override lstInit() {
    this._archetypeListService.toFinishLoading();
    this._classService.toFinishLoading();
  //  this.inicializedFieldsReviewed();
    this.listClass = this._classService.models;    
    this.listArchetype = this._archetypeListService.models;
    this.description = `Showing ${this.listClass.length} results`;
  }

  protected override specialSort(parameter: Sorting.Parameter) {
    switch (parameter) {
      case 'characters':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'descending'
            ? this._reviewService.reviewResults[a.id].characterIds.length >
              this._reviewService.reviewResults[b.id].characterIds.length
              ? 1
              : -1
            : this._reviewService.reviewResults[a.id].characterIds.length <
              this._reviewService.reviewResults[b.id].characterIds.length
            ? 1
            : -1
        );
        break;
      case 'asBattleCharacter':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'descending'
            ? this._reviewService.reviewResults[b.id].asBattleCharacter.length +
                this._reviewService.reviewResults[b.id].asSpeaker.length ===
                0 ||
              this._reviewService.reviewResults[a.id].asBattleCharacter.length >
                this._reviewService.reviewResults[b.id].asBattleCharacter.length
              ? 1
              : -1
            : this._reviewService.reviewResults[a.id].asBattleCharacter.length +
                this._reviewService.reviewResults[a.id].asSpeaker.length ===
                0 ||
              this._reviewService.reviewResults[a.id].asBattleCharacter.length <
                this._reviewService.reviewResults[b.id].asBattleCharacter.length
            ? 1
            : -1
        );
        break;
      case 'asSpeaker':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'descending'
            ? this._reviewService.reviewResults[b.id].asBattleCharacter.length +
                this._reviewService.reviewResults[b.id].asSpeaker.length ===
                0 ||
              this._reviewService.reviewResults[a.id].asSpeaker.length >
                this._reviewService.reviewResults[b.id].asSpeaker.length
              ? 1
              : -1
            : this._reviewService.reviewResults[a.id].asBattleCharacter.length +
                this._reviewService.reviewResults[a.id].asSpeaker.length ===
                0 ||
              this._reviewService.reviewResults[a.id].asSpeaker.length <
                this._reviewService.reviewResults[b.id].asSpeaker.length
            ? 1
            : -1
        );
        break;
      case 'voices':
        this._modelService.models.sort((a, b) => {
          if (this.srtLstOrder === 'undefinedOnTop') {
            return a[parameter] === undefined && b[parameter] !== undefined
              ? -1
              : 1;
          } else {
            const compA =
              this.srtLstOrder === 'ascending'
                ? GameTypes.voiceName[a[parameter]]
                : GameTypes.voiceName[b[parameter]];
            const compB =
              this.srtLstOrder === 'ascending'
                ? GameTypes.voiceName[b[parameter]]
                : GameTypes.voiceName[a[parameter]];
            return compB !== undefined && compA !== undefined
              ? compA > compB
                ? -1
                : 1
              : GameTypes.voiceName[a[parameter]] === undefined
              ? 1
              : -1;
          }
        });
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  inicializedFieldsReviewed() {
    this._classService.models.forEach((character) => {
      character.isReviewedName = !!character.isReviewedName;
      character.isReviewedDescription = !!character.isReviewedDescription;
      this._classService.svcToModify(character);
    });
  }
    changeSelectedArchetypeList(classList: Class, color: string) {
      classList.nameArchetype = color;   
      this._classService.svcToModify(classList);
      this._classService.toSave();
      this.lstAfterFetchList();
    }

  sortOrder = 'asc'; // inicialmente em ordem ascendente (a-z)

sortArchtypeList() {
  const itemsComConteudo = this.listClass.filter(item => item.nameArchetype !== undefined && item.nameArchetype !== null && item.nameArchetype !== ''); // filtra os itens com conteúdo no campo 'nameArchetype'
  const itemsSemConteudo = this.listClass.filter(item => item.nameArchetype === undefined || item.nameArchetype === null || item.nameArchetype === ''); // filtra os itens sem conteúdo no campo 'nameArchetype'

  itemsComConteudo.sort((a, b) => {
    if (this.sortOrder === 'asc') {
      return a.nameArchetype.localeCompare(b.nameArchetype);
    } else {
      return b.nameArchetype.localeCompare(a.nameArchetype);
    }
  });

  this.listClass = [...itemsComConteudo, ...itemsSemConteudo]; // concatena os itens com conteúdo ordenados com os itens sem conteúdo
  this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
  this._classService.models = [];
  this._classService.models = this.listClass;
  this._classService.toSave();

}

  getColorNameArchetype(archetypeName: string ): string {
    let color: string = '#383838';

    if (archetypeName == undefined) 
        return color;

    for (let i = 0; i < this._archetypeListService.models.length; i++) 
    {
        if (this._archetypeListService.models[i].name == archetypeName) 
        {
            color = this._archetypeListService.models[i].color;   
            break;
        }
    }   
    return color;
}


  /**
   * Prompts a button list to select the voices of the class
   * @param character character to change the gender of
   */
  public async toPromptAddVoice(klass: Class) {
    const btns = Popup.voiceButtons.filter(
      (btn) => +btn.value !== (+klass.voices & +btn.value)
    );
    const selectedBtn = await this._popupService.fire<Voices, Voices>(
      new Popup.Interface(
        {
          title: 'Select Voices',
          actionsClass: 'column',
        },
        btns
      )
    );

    if (!selectedBtn) {
      return;
    }

    klass.voices |= selectedBtn.value;
    await this._classService.svcToModify(klass);
  }

  async toRemoveVoice(klass: Class, voice: Voices) {
    klass.voices &= ~voice;
    await this._classService.svcToModify(klass);
  }

  getClassOrtography(klass: Class)
  {
    this._translationService.getClassOrtography(klass, true);
  }

  isClassTranslated(klass: Class)
  {
    return this._translationService.checkTranslation(klass.id, 'EN-US');
  }

  goToTags()
  {
    this._router.navigate(['/archetypesList']);
  }

  public downloadAsExcel() {
    const classListTable = document.createElement('table');
    const tHead = classListTable.createTHead();
    const tHeadRow = tHead.insertRow();
    tHeadRow.insertCell().innerText = 'ID';
    tHeadRow.insertCell().innerText = 'Name';
    tHeadRow.insertCell().innerText = 'Description';
    tHeadRow.insertCell().innerText = 'Notes';
    tHeadRow.insertCell().innerText = 'Voices';
    tHeadRow.insertCell().innerText = 'Characters';
    tHeadRow.insertCell().innerText = 'Battle';
    tHeadRow.insertCell().innerText = 'Speaker';
    const tBody = classListTable.createTBody();

    this._classService.models.filter((item) => {
      const tBodyRow = tBody.insertRow();     
      let listClass = this._reviewService.reviewResults[item.id];

      tBodyRow.insertCell().innerText = item.id;
      tBodyRow.insertCell().innerText = item.name;
      tBodyRow.insertCell().innerText = item.description;
      tBodyRow.insertCell().innerText = item.notes || '';
      tBodyRow.insertCell().innerText = GameTypes.voiceName[item.voices] || '';
      tBodyRow.insertCell().innerText = listClass.characterIds.length.toString();
      tBodyRow.insertCell().innerText = listClass.asBattleCharacter.length.toString();
      tBodyRow.insertCell().innerText = listClass.asSpeaker.length.toString();
    });

    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(classListTable);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Class List');

    const now = new Date();
    XLSX.writeFile(wb, now.toLocaleDateString() + '_' + now.toLocaleTimeString() + ' DSAdmin Class List.xlsx');

  }


changeName(klass: Class, fieldName, value:string) {
      klass.isReviewedName = false;
      klass.revisionCounterNameAI = 0;
      this.lstOnChange(klass, fieldName, value);
    }

changeDescription(klass: Class, fieldName, value:string) {
      klass.isReviewedDescription = false;
      klass.revisionCounterDescriptionAI = 0;
      this.lstOnChange(klass, fieldName, value);
    }

}
