import { Component } from '@angular/core';
import { RelicUsesService, TierService } from 'src/app/services';
import { RelicUses } from '../../../../../../../lib/@bus-tier/models/RelicUses';

@Component({
  selector: 'app-relic-Uses',
  templateUrl: './relicUses.component.html',
  styleUrls: ['./relicUses.component.scss']
})
export class RelicUserComponent {

  weaponRarityNames = [];
  description: string;
  sortNameOrder = +1;
  listRelicUses: RelicUses[];

  constructor(
    private _tierListService: TierService,
    private _relicUsesService: RelicUsesService,
  ) {}


  ngOnInit() {
    this._tierListService.toFinishLoading();

    this._tierListService.models = this._tierListService.models.filter((tier) => tier.name !== undefined);
    this.weaponRarityNames = this._tierListService.models.filter((tier) => tier.selectDrop === 'Character Rarity');   
    
    this.createdInspirationPoints();
    this.getCompare();
   //this.lineupOrderCommon();
    this.description = `Showing ${this.listRelicUses.length} results`;
  }

  createdInspirationPoints() {
    this.weaponRarityNames.forEach((x) => {
      this._relicUsesService.createNewrelicUses(x);
     });   
  }

  getCompare() {
    this._relicUsesService.toFinishLoading();
    const listRelic = this._relicUsesService.models;

    if (listRelic.length > 0) {
      this.weaponRarityNames.forEach((x) => {
        for (let index = 0; index < listRelic.length; index++) {
          if(x.id === listRelic[index].idRarity && x.name !== listRelic[index].nameRarity ){
            listRelic[index].nameRarity = x.name;
            this._relicUsesService.svcToModify(listRelic[index]);
          }         
        }        
      });
    }
    this.listRelicUses = this._relicUsesService.models;
  }
    
  async onChangeAvailableRarity(relic: RelicUses, value: string) {
    relic.numberRelicUse = value == '' ? undefined : +value;
    this._relicUsesService.svcToModify(relic);
    this._relicUsesService.toSave();
  }
  
  lineupOrderCommon() 
  {
  this.sortNameOrder *= +1;
    this.listRelicUses.sort((a, b) => 
    {  
      return this.sortNameOrder * a.nameRarity.localeCompare(b.nameRarity);
    });
  }
}
