  <!--MODAL-->
  <ng-container *ngIf="popupStats">
    <div class="modal-backdrop" *ngIf="popupStats"></div> 
    <div id="modal-close" @popup class="popup-report" style="background-color: transparent">
      <div class="total-modal">
        <div class="comp-container">
          <div class="width-container"> 

            <ng-container *ngIf="selectType === 'Grammar' || selectType === 'Creative Writing'">
              <div id="component_Grammar_creative" class="box-grammar">     
                   <h4>{{ selectType }}</h4>
                       <button (click)="closeModal()" class="swal-icon-close" style="display: flex;">
                        <i class="pe-7s-close i-icon"></i>
                    </button>                 
                    <div style="width: 200px; display: block; justify-content: center; margin: auto;">
                    <ng-container *ngFor="let option of arrayOptions; let buttonIndex = index">
                        <button class="btn btn-cancel btn-fill" id="confirmBtn" [class.selectedOption]="option.text === selectedOption"  (click)="clickOption(option)" style="margin-bottom: 3px; text-align: center;">
                        {{ option.text }}
                      </button>
                    </ng-container>      
      
                </div>
             </div>
            </ng-container>

            <ng-container *ngIf="title === 'Ghostwriter' || openModalGeneral">
                <div id="Component_geral" class="box">
                    <div class="title-element">
                      <h4>{{ title }}</h4>
                      <button (click)="closeModal()" class="swal-icon-close" style="display: flex;">
                        <i class="pe-7s-close i-icon"></i>
                    </button>
                    </div>  
                    <div class="swal2-html-container tips div-description">
                      {{ description ? description : 'Sem descrição' }}
                    </div>
                    <div>
                      <div class="sendCharaterMessage">{{sendCharaterMessage}}</div>
                      <div class="position-icon">                    
                        <button class="i-btn" style="font-size: 16px;" title="Regenerate response" (click)="refreshPopup()">
                            <i class="icon pe-7s-refresh-2"></i>
                        </button>
                      </div>
                    </div>        
                  <div class="actions-column div-selected">
                    <ng-container *ngFor="let response of responsePrompt; let buttonIndex = index">
                    <div style="display: flex;">
                      <ng-container *ngIf="title === 'Ghostwriter'">
                        <button class="btn btn-main btn-fill btn-Minion btn-dimension"
                            [class.selected]="response === btnSelected"
                            (click)="ClickButton(response)"
                            style="white-space: normal; word-wrap: break-word; max-width: 100%;">
                            {{ response }}
                       </button>
                      </ng-container>   
                                         
                      <ng-container *ngIf="title != 'Ghostwriter'">
                        <button class="btn btn-main btn-fill btn-Minion btn-dimension"
                            [class.selected]="response.text === btnSelected"
                            (click)="ClickButton(response.text)"
                            style="white-space: normal; word-wrap: break-word; max-width: 100%;">
                            {{ response.text }}
                       </button>
                      </ng-container>
                
                    </div>
                    </ng-container>
                     <ng-container *ngIf="responsePrompt.length === 0">
                        <div class="content-refresh">
                          <i class="icon pe-7s-refresh-2 icon-refresh-text"></i>                      
                        </div>
                        <div style="align-items: center;">
                            <h4>Loading, please wait.</h4>  
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                              </div>
                        </div>                             
                      </ng-container>
                    </div>
                  </div>
            </ng-container>

          </div>

          <ng-container *ngIf="btnSelected">
            <div id="component-confirm" class="box-confirm">        
                    <div style="text-align: center; margin-bottom: 25px;">
                       <h4>Are you sure?</h4> 
                       <button (click)="closeModal()" class="swal-icon-close text-center" style="display: flex;">
                        <i class="pe-7s-close i-icon"></i>
                    </button>
                      <span>{{ 'WARNING: This action will change the original contents of the input' }}</span>
                    </div> 
                 <div style="width: 200px; display: grid; justify-content: center; margin: auto;">
                        <button class="btn btn-modal text-center" id="confirmBtn"
                        (click)="confirmChange()">
                        Yes, apply changes!
                      </button>
                      <button class="btn btn-cancel btn-fill text-center" id="confirmBtn"
                      (click)="cancel()">
                      Cancel
                    </button>
                </div>
            </div>
          </ng-container>

        </div>
      </div>
    </div>
  

  </ng-container>
  <!--FIM DO MODAL-->