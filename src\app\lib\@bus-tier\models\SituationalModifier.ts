import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class SituationalModifier extends Base<Data.Hard.ISituationalModifier, Data.Result.ISituationalModifier> implements Required<Data.Hard.ISituationalModifier>
{
  public static generateId(index: number): string {
    return IdPrefixes.SITUATIONALMODIFIER + index;
  }

  constructor( index: number, dataAccess: SituationalModifier['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: SituationalModifier.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get knowledge(): string
  {
    return this.hard.knowledge;
  }
  public set knowledge(value: string) 
  {
    this.hard.knowledge = value;
  }
  public get situationalmodifier(): string[]
  {
    return this.hard.situationalmodifier;
  }
  public set situationalmodifier(value: string[]) 
  {
    this.hard.situationalmodifier = value;
  }
  public get factor(): string[]
  {
    return this.hard.factor;
  }
  public set factor(value: string[]) 
  {
    this.hard.factor = value;
  }
  public get description(): string[]
  {
    return this.hard.description;
  }
  public set description(value: string[]) 
  {
    this.hard.description = value;
  }

}
