<!--<select name="language" id="lang" [(ngModel)]="lstLanguage">
  <option value="PT-BR">PT-BR</option>
  <option value="EN-US">EN-US</option>
</select>-->

<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="description"
          [rightButtonTemplates]="[exportExcelButtonTemplate, tagsButton, addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortListByParameter('id')">ID</th>
            <th class="th-clickable" (click)="sortListByParameter('name')">Name & Description
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('notes')">Notes</th>
            <th class="th-clickable" (click)="sortArchtypeList()">Archetype</th>
            <th class="th-clickable" (click)="sortListByParameter('voices')">Voices</th>
            <th class="th-clickable" (click)="sortListByParameter('characters')">Characters</th>
            <th class="th-clickable" (click)="sortListByParameter('asBattleCharacter')">Battle</th>
            <th class="th-clickable" (click)="sortListByParameter('asSpeaker')">Speaker</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let klass of listClass; let i = index">
            <tr id="{{ klass.id }}">
              <td class="td-sort">{{ i+1 }}</td>
              <td class="td-id">{{ klass.id }}</td>
              <td>
                <!-- <input class="form-control form-short"
                       type="text"
                       value="{{klass.name}}"
                       #name
                       (change)="lstOnChange(klass, 'name', name.value)"
                       *ngIf="lstLanguage == 'PT-BR'"/> -->
                <input class="form-control form-short" type="text"
                  value="{{(klass | translation : lstLanguage : klass.id : 'name')}}" #name
                  (change)="changeName(klass, 'name', name.value)" />

                <!-- <textarea class="form-control"
                       type="text"
                       value="{{klass.description}}"
                       #description
                       (change)="lstOnChange(klass, 'description', description.value)"
                       *ngIf="lstLanguage == 'PT-BR'"></textarea> -->
                <textarea class="form-control" type="text"
                  value="{{(klass | translation : lstLanguage : klass.id : 'description')}}" #description
                  (change)="changeDescription(klass, 'description', description.value)"></textarea>
              </td>
              <td>
                <textarea class="form-control borderless" type="text"
                  value="{{ (klass | translation : lstLanguage : klass.id : 'notes') }}" #notes
                  (change)="lstOnChange(klass, 'notes', notes.value)"></textarea>
              </td>
              <td style="width: 220px;">
                <select class="arche"
                [ngStyle]="{'background-color': getColorNameArchetype(klass.nameArchetype)}"               
                  [(ngModel)]="klass.nameArchetype" #InputArchetype
                  (change)="changeSelectedArchetypeList(klass, InputArchetype.value)">
                  <option></option>
                  <option [ngStyle]="{ background: (archetype | information)?.hex}"
                    *ngFor="let archetype of listArchetype" value="{{ archetype.name }}">
                    {{ archetype.name }}
                  </option>
                </select>
              </td>
              <td style="width: 250px;">
                <button *ngIf="klass.voices | hasVoice : Voices.HUMAN" class="btn btn-fill btn-Human"
                  (click)="toRemoveVoice(klass, Voices.HUMAN)">
                  Human
                </button>
                <button *ngIf="klass.voices | hasVoice : Voices.NONHUMAN" class="btn btn-fill btn-Non-Human"
                  (click)="toRemoveVoice(klass, Voices.NONHUMAN)">
                  Non-Human
                </button>
                <button *ngIf="klass.voices < Voices.HUMAN_AND_NONHUMAN || !klass.voices"
                  class="btn success btn-simple btn-sm" (click)="toPromptAddVoice(klass)">
                  <i class="pe-7s-plus"></i>
                </button>
              </td>
              <td> <i title="Not assigned" *ngIf="(klass | review).characterIds.length === 0"
                  class="pe-7s-attention attention"></i>
                <i style="position: relative" placement='left' delay='250' ttWidth="300px" ttAlign="left" tt ttWordWrap='break-word'
                  ttPadding="10px" tooltip="Assigned at {{ ((klass | review).characterIds | characterNames).toString() }}"
                  *ngIf="(klass | review).characterIds.length > 0" class="pe-7s-check success"></i>
                <p>{{ (klass | review).characterIds.length }}</p>
              </td>
              <ng-container
                *ngIf="(klass | review).asBattleCharacter.length + (klass | review).asSpeaker.length > 0; else notAssigned">
                <td>
                  <i *ngIf="(klass | review).asBattleCharacter.length > 0" style="position: relative" placement='left'
                    delay='250' ttWidth="500px" ttAlign="left" ttPadding="10px"
                    tooltip="Assigned as battle character at {{ ((klass | review).asBattleCharacter | location).toString() }}"
                    class="pe-7s-check success"></i>
                  <p *ngIf="(klass | review).asBattleCharacter.length > 0">
                    {{ (klass | review).asBattleCharacter.length }}</p>
                </td>
                <td>
                  <i *ngIf="(klass | review).asSpeaker.length > 0" style="position: relative" placement='left'
                    delay='250' ttWidth="300px" ttAlign="left" ttPadding="10px"
                    tooltip="Assigned as speaker at {{ ((klass | review).asSpeaker | location).toString() }}"
                    class="pe-7s-check success"></i>
                  <p *ngIf="(klass | review).asSpeaker.length > 0">{{ (klass | review).asSpeaker.length }}</p>
                </td>
              </ng-container>
              <ng-template #notAssigned>
                <td colspan="2">
                  <i style="position: relative" placement='left' delay='250' ttWidth="auto" ttAlign="center"
                    ttPadding="10px" tooltip="Not assigned as battle character" class="pe-7s-attention attention"></i>
                </td>
              </ng-template>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove" (click)="lstPromptRemove(klass)">
                  <i class="pe-7s-close"></i>
                </button>
                <br>
                <button class="btn btn-gray btn-fill translation-button" (click)="getClassOrtography(klass)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>

    </div>
  </div>
</div>