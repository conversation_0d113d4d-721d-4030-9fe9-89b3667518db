import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AIPrompt } from 'src/app/lib/@bus-tier/models/AIPrompt';
import { Button } from 'src/app/lib/@pres-tier/data';
import { OpenAIEnvironmentService, UserSettingsService } from 'src/app/services';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { IOpenAIEnvironment } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';


@Component({
  selector: 'app-ai-prompt',
  templateUrl: './ai-prompt.component.html',
  styleUrls: ['./ai-prompt.component.scss'],
})

export class AIComponent extends TranslatableListComponent<AIPrompt> implements OnInit
{
  prompts: AIPrompt[] = [];
types = ['AI Revisor','Ghostwriter', 'Grammar - Correct', 'Grammar - Reorder Syntax','Grammar - Less Words', 'Creative Writing - Improve Text', 'Creative Writing - Easy Reading Text', 'Creative Writing - Sophisticated Text', 'Creative Writing - Casual Text'];
  subGrammar = ['Correct Grammar', 'Reorder Syntax','Less Words'];
  subCreativeWriting = ['Improve Text', 'Easy Reading Text', 'Sophisticated Text', 'Casual Text',	];
  subTypes = [];
  listEnvironments: IOpenAIEnvironment[] = [];


  constructor(
    _activatedRoute: ActivatedRoute,
    private _aiPromptService: AIPromptService,
    private _openAIEnvironmentService: OpenAIEnvironmentService,
     private _router: Router,
    _userSettingsService: UserSettingsService,
     _translationService: TranslationService,
     _languageService: LanguageService
  ) 
  {
    super(_aiPromptService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public readonly addAIPromptTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addAIPrompt.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  public override ngOnInit(): Promise<void> 
  {
    this.getEmptyPrompt();
    this.prompts = this._aiPromptService.models;
    this.listEnvironments = this._openAIEnvironmentService.models;
    return null;
  }

  getEmptyPrompt() {
    this._aiPromptService.models.forEach(x => {
      if (x.nameEnvironmentAi === undefined) {
        x.nameEnvironmentAi = '';          
      } 
      if (x.selectType === 'Select type' || x.selectType === undefined) {
        x.selectType = '';        
      }

      this._aiPromptService.svcToModify(x);    
    });
  }

  async addAIPrompt()
  {
    let newPrompt: AIPrompt = this._aiPromptService.svcPromptCreateNewAIPrompt();
    newPrompt.selectType = 'Select type';
    await this._aiPromptService.srvAdd(newPrompt);
    this.prompts = this._aiPromptService.models;
  }

  selectType(type : string, prompts: AIPrompt) { 
     prompts.selectType = type;
    this._aiPromptService.svcToModify(prompts);
    this.ngOnInit();
  } 

  selectEnvironment(nameEnvironmentAi : string, prompts: AIPrompt ) { 
    prompts.nameEnvironmentAi = nameEnvironmentAi;
    prompts.idEnvironmentAi = this.listEnvironments.find(x => x.name === nameEnvironmentAi).id;
   this._aiPromptService.svcToModify(prompts);
   this.ngOnInit();
 } 
  
  async changePrompt(prompt, value, property)
  {
    prompt[property] = value;
    await this._aiPromptService.svcToModify(prompt);
    this.ngOnInit();
  }

  async deletePrompt(prompt)
  {
    await this._aiPromptService.toPromptRemove(prompt, 'id');
    this.prompts = this._aiPromptService.models;
    this.ngOnInit();
  }

  public redirectToSettings() {
    this._router.navigate(['settings']);
  }
}
