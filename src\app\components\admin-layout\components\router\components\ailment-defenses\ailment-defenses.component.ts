import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Ailment } from 'src/app/lib/@bus-tier/models/Ailment';
import { AilmentDefenses } from 'src/app/lib/@bus-tier/models/AilmentDefenses';
import { ElementalDefenses } from 'src/app/lib/@bus-tier/models/ElementalDefenses';
import { Button } from 'src/app/lib/@pres-tier/data';
import { BattleUpgradeService, CharacterService, ClassService, UserSettingsService } from 'src/app/services';
import { AilmentService } from 'src/app/services/ailment.service';
import { AilmentDefensesService } from 'src/app/services/ailmentDefeneses.service';
import { CustomService } from 'src/app/services/custom.service';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { Alert } from 'src/lib/darkcloud';
import { <PERSON><PERSON>hara<PERSON> } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { BattleUpgrade } from '../../../../../../lib/@bus-tier/models/BattleUpgrade';
import { ActivatedRoute, Router } from '@angular/router';
import { GameTypes } from 'src/lib/darkcloud/angular/dsadmin/v8/models';


@Component({
  selector: 'app-ailment-defenses',
  templateUrl: './ailment-defenses.component.html',
  styleUrls: ['./ailment-defenses.component.scss']
})
export class AilmentDefensesComponent implements ICollectibleDetails {

  @Input() character = '';
  public ailmentDefenses: AilmentDefenses;
  public ailmentList: Ailment[] = [];
  public ailmentHead: Ailment[] = [];
  public charactersList: ICharacter[] = [];
  public ailDef: AilmentDefenses;
  public elementalList: ElementalDefenses[] = [];
  sortNameOrder = +1;
  currentCharacter: BattleUpgrade;
  valueBossLevel: string;
  nameClass: string;
  nameRarity: string;
  type: string;


  constructor(
    protected _customService: CustomService,
    protected _characterService: CharacterService,
    protected _router: Router,
    _activatedRoute: ActivatedRoute,
    private ref: ChangeDetectorRef,
    private _elementalDefensesService: ElementalDefensesService,
    _userSettingsService: UserSettingsService,
    private _ailmentService: AilmentService,
    private _ailmentDefensesService: AilmentDefensesService,
    private _battleUpgradeService: BattleUpgradeService,
    private _classService: ClassService,
  ) {
    //super(_ailmentDefensesService, _activatedRoute, _userSettingsService, 'name'); 
  }


  public readonly excelButtonTemplate: Button.Templateable =
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };

  async ngOnInit(): Promise<void> {
    await this._elementalDefensesService.toFinishLoading();
    await this._ailmentDefensesService.toFinishLoading();
    this.ailmentList = this._ailmentService.models;
    this.elementalList = this._elementalDefensesService.models.filter((x) => x.selectDrop === 'Defenses - Ailment');
    const defenses = this._ailmentDefensesService.models.filter((ail) => ail.characterId === this.character);
    this.charactersList = this._characterService.models.filter((x) => x.id === this.character);

    let valueCharacter = this.currentCharacter === undefined ? this.character : this.currentCharacter.character;

        setTimeout(() => {
        if (this.currentCharacter !== undefined) {
          const op = this.currentCharacter.bl != undefined ? this.currentCharacter.bl : 0;
          this.valueBossLevel = `BL: ${op}`;
        } else {
          this.valueBossLevel = 'BL: 0';
        }

        const character = this._characterService.models.find(x => x.id === valueCharacter);       
        this.nameRarity = character?.rarity;
        this.nameClass = this._classService.models.find(x => x.id === character?.classId)?.name;
        this.type = GameTypes.characterTypeName[character.type];
      }, 100)


    if (defenses.length > 0) {
      await this._elementalDefensesService.toFinishLoading();
      this.ailmentDefenses = defenses[0];   
    }
    else {
      this.ailDef = this._ailmentDefensesService.createNewAilmentDefenses(this.character);
      this.charactersList.forEach((x) => this.ailDef.characterName = x.name);
      this.ailmentDefenses = this.ailDef;
      this._ailmentDefensesService.svcToModify(this.ailmentDefenses);
      this._ailmentDefensesService.toSave();
    }
  }

  lineupOrderAilmentDefenses() {
    this.sortNameOrder *= +1;
    this.ailmentHead.sort((a, b) => {
      return this.sortNameOrder * a.ailment.localeCompare(b.ailment);
    });
  }


  async onExcelPaste(): Promise<void> {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if (lines.length < 2) {
      Alert.showError("The copied content is invalid. Make sure you copy at least two lines.");
      return;
    }

    // Pega o conteúdo da 1ª linha (Nomes dos ailments)
    const firstLine = lines[0].split(/\t/).map(cell => cell.trim());
    // Pega o conteúdo da 2ª linha (Valores dos defenses)
    const secondLine = lines[1].split(/\t/).map(cell => cell.trim());


    for (const value of secondLine) {
      if (!isNaN(Number(value))) {
        Alert.showError(`The value "${value}" in the second line is not a valid string.`);
        return;
      }
    }

    for (let i = 0; i < firstLine.length; i++) {
      const ailmentName = firstLine[i];
      const ailment = this.ailmentList.find(a => a.ailment.trim().toLowerCase() === ailmentName.toLowerCase());

      if (!ailment) {
        Alert.showError(`The name "${ailmentName}" in the 1ª line was not found.`);
        return;
      }
    }

    for (let i = 0; i < secondLine.length; i++) {
      const defenseValue = secondLine[i];
      const defense = this.elementalList.find(e => e.defenses.trim().toLowerCase() === defenseValue.toLowerCase());

      if (!defense) {
        Alert.showError(`The value "${defenseValue}" in the 2ª line was not found.`);
        return;
      }
    }

    this.ailmentDefenses.ailmentDefensesList = [];

    for (let i = 0; i < this.ailmentList.length; i++) {
      const ailment = this.ailmentList[i];
      const indexInFirstLine = firstLine.findIndex(ail => ail.trim().toLowerCase() === ailment.ailment.trim().toLowerCase());

      if (indexInFirstLine !== -1) {
        this.ailmentDefenses.ailmentDefensesList.push({
          fieldNameAilment: ailment.ailment,
          fieldValueDefense: secondLine[indexInFirstLine]
        });
      }
    }

    await this._ailmentDefensesService.svcToModify(this.ailmentDefenses);
    await this._ailmentDefensesService.toSave();
    Alert.ShowSuccess('Ailment Defenses copied successfully!');
    this.ref.detectChanges();
    this.ngOnInit();
  }



  async changeAilmentDefense(value: string, index: number, name: string) {

    if (value !== '' && value !== null) {
      const isValidDefense = this.elementalList.find(e => e.defenses.trim().toLowerCase() === value.toLowerCase());

      if (!isValidDefense) {
        Alert.showError(`The value "${value}" is not a valid defense.`);

        this.ailmentDefenses.ailmentDefensesList[index].fieldValueDefense = null;
        this.ref.detectChanges();
        return;
      }
    }

    this.ailmentDefenses.ailmentDefensesList[index].fieldValueDefense = value === '' ? null : value;

    if (this.ailmentDefenses.ailmentDefensesList[index].fieldValueDefense) {
      this.ailmentDefenses.ailmentDefensesList[index].fieldNameAilment = name;
    } else {
      this.ailmentDefenses.ailmentDefensesList[index].fieldNameAilment = null;
    }

    const allNull = this.ailmentDefenses.ailmentDefensesList.every(item => item.fieldValueDefense === null);

    if (allNull) {
      this.ailmentDefenses.ailmentDefensesList = [];
    }

    this._ailmentDefensesService.svcToModify(this.ailmentDefenses);
    this._ailmentDefensesService.toSave();

    this.ref.detectChanges();
  }

  reset(character) {
    this.character = character
    this.ngOnInit()
  }

  public onBack() {
    this._router.navigate(['others']);
  }
}
