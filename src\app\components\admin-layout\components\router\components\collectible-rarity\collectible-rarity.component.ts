import { ChangeDetectorRef, Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Character, TierList } from 'src/app/lib/@bus-tier/models';
import { ElementalDefenses } from 'src/app/lib/@bus-tier/models/ElementalDefenses';
import { AilmentDefensesService, AreaService, BattleInferiorService, CharacterService, ClassService, LevelService, PrimalModifierService, SpecialSkillsService, StatusInfoService, TierService } from 'src/app/services';
import { BattleUpgradeService } from 'src/app/services/battle-upgrade.service';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { LanguageService } from 'src/app/services/language.service';
import { ReviewService } from 'src/app/services/review.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';

class CollectibleRecord
{
 id : string
 status:string
 primal : string
 battle:string 

 constructor(char : string, status? :string, primal? :string, battle? :string)
 {
   this.id = char
   this.status = status
   this.primal = primal
   this.battle = battle
 }
} 

@Component({
  selector: 'app-collectible-rarity',
  templateUrl: './collectible-rarity.component.html',
  styleUrls: ['./collectible-rarity.component.scss'],
})

export class CollectibleRarityComponent extends SortableListComponent<Character> 
{ 
  public rarities: TierList[] = [];
  public types = ['Boss', 'Minions Collectable', 'Minions(All)', 'Subbosses','Collectible (Boss + Minion Collectible)'];
  areas = [];
  validaCharacters = [];
  characterListIds = [];
  listOptionSelectedCharacters = [];
  getListoptionCharacters = [];
  allCharacters = [];
  rarityFilter = "ALL";
  areaFilter = "ALL";
  classFilter = "ALL";
  typeFilter = "ALL";
  isDescendingSpDef = false;
  collectibleRecord : CollectibleRecord[] = [];
  defensesSkillList: ElementalDefenses[] = [];
  listAllCharacters = [];
  listOrdernspDef = [];
  charactersList = [];
  getCharacters = [];
  classes = [];
  sortCollectibleRecordOrder = -1;
  description:string = '';
  // Variável para armazenar o valor selecionado no dropdown
  selectedRarity: string = 'ALL'; // Valor padrão
  selectedType: string = 'ALL';
  selectedArea: string = 'ALL';
  selectedClass: string = 'ALL';
  rarityList: TierList[] = [];
  activeTab: string;
  isCtrEvMax = false; // Variável para controlar a aba ctr-ev-max

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _characterService: CharacterService,
    _userSettingsService: UserSettingsService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _router: Router,
    private _change: ChangeDetectorRef,
    private _reviewService: ReviewService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    protected _elementalInfoService : StatusInfoService,
    private _elementalDefensesService: ElementalDefensesService,    
    protected _primalService : PrimalModifierService,
    protected _battleService : BattleUpgradeService,  
    private _ailmentDefensesService: AilmentDefensesService,
    private _classService: ClassService,
    private _tierListService: TierService,
    protected _battleInferiorService: BattleInferiorService,
    private _specialSkillsService: SpecialSkillsService,
  ) 
  {
    super(_characterService, _activatedRoute, _userSettingsService, 'name');
  }
 
  override async ngOnInit()
  {
    await this._areaService.toFinishLoading();
    await this._characterService.toFinishLoading();
    await this._tierListService.toFinishLoading();  
    this.sortCollectibleRecord();    
    this.areas = this._areaService.models;    
    const listOptionCharactersString = this._activatedRoute.snapshot.queryParams['listOptionCharacters'];
    if (listOptionCharactersString) {
      this.getListoptionCharacters = JSON.parse(listOptionCharactersString);
    } else {
      this.getListoptionCharacters = []; // Valor padrão caso não exista
    }
   this.removeDuplicateIds();
    this.getClasses();    
    this.characterListIds = [];
    this.getCharactersIds(this.charactersList);

    this.charactersList.forEach((character, index) => {
      character.quantitySpDef = this.getSkillDefenses(character);
    });

    const tab = localStorage.getItem(
          `tab-OthersComponent${FILTER_SUFFIX_PATH}`        
    );
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
  

    this.description = `Showing ${this.charactersList.length} results`;   
  }

public async removeDuplicateIds() {
  const characters = this._battleInferiorService.models.map(model => model.character);
  const uniqueCharacters = [...new Set(characters)];
  const duplicateCharacters = characters.filter((character, index) => characters.indexOf(character) !== index);

  duplicateCharacters.forEach(character => {
    const id = this._battleInferiorService.models.find(model => model.character === character).id;
    this._battleInferiorService.svcToRemove(id);
  });
}

  getSkillDefenses(character: Character) { 
    this.defensesSkillList = this._elementalDefensesService.models.filter((x) => x.selectDrop === 'Defenses - Skill');
    const char = this._elementalInfoService.models.filter(status => status.character === character.id); 

    if (char.length > 0) {
      const count = char.filter(item => this.defensesSkillList.find(x => x?.defenses === item?.spDef)).length;
      this.listOrdernspDef.push({count:count > 0 ? count : '', character: character}); 
      return count > 0 ? count : '';
    } else {
      return '';
    }
  }


  sortByQuantitySpDef() {
    // Alterna a ordem com base no estado atual
    this.isDescendingSpDef = !this.isDescendingSpDef;
   return this.charactersList = this.charactersList.sort((a, b) => {
      return this.isDescendingSpDef ? b.quantitySpDef - a.quantitySpDef : a.quantitySpDef - b.quantitySpDef;
    });
  }
  
  getClasses()
  {
    for(let i = 0; i < this._classService.models.length; i++)
    this.classes.push(this._classService.models[i].name);

    this.rarityList = this._tierListService.getCollectibleRarity('Character Rarity'); 
    this.rarityList = this.rarityList.filter((rar) => rar.name !== "" && rar.name !== undefined)

    this.getAllCharacters(); 
    if (this.getListoptionCharacters.length > 0) {
      this.getCharacterSelectedList();
    }
  }

 getCharacterSelectedList() {
// atribui a lista que foi selecionada de volta
  let index = 0;

  while (index < this.getListoptionCharacters.length) {
    const item = this.getListoptionCharacters[index];

  // Verifica as condições e chama os métodos correspondentes
  if (item.hasOwnProperty('rarityFilter')) {
    this.filterRarity(item.rarityFilter);
    this.selectedRarity = item.rarityFilter; // Atualiza o valor selecionado
  }

  if (item.hasOwnProperty('typeFilter')) {
    this.filterType(item.typeFilter);
    this.selectedType = item.typeFilter;
  }

  if (item.hasOwnProperty('areaFilter')) {
    this.filterArea(item.areaFilter);
    this.selectedArea = item.areaFilter;
  }

  if (item.hasOwnProperty('classFilter')) {
    this.filterClass(item.classFilter);
    this.selectedClass = item.classFilter;
  }

  index++;
}

 }

  getAllCharacters()
  {
    for(let i = 0; i < this._characterService.models.length; i++)
      if(this.isCharacterTypeBossMinionSubboss(this._characterService.models[i]))
      {
        this.allCharacters.push(this._characterService.models[i]);
        this.charactersList.push(this._characterService.models[i]);
      } 
    this.filterCharacters();   
  }

filterCharacters() {
  this.charactersList = this.allCharacters;

  if(this.rarityFilter == "ALL" && this.classFilter == "ALL" && this.typeFilter == "ALL" && this.areaFilter == "ALL")
    {
     // this.getListoptionCharacters = [];
      this.charactersList = [];
      this.charactersList = this.allCharacters;
    }

  // Aplicar filtro por área, se não for 'ALL'
  if (this.areaFilter !== 'ALL') {
    this.charactersList = this.charactersList.filter(character => character.areaId === this.areaFilter);
  }

  // Aplicar filtro por classe, se não for 'ALL'
  if (this.classFilter !== 'ALL') {
    // Converter o nome da classe para o ID correspondente
    let classId = this.classFilter;
    for (let i = 0; i < this._classService.models.length; i++) {
      if (this._classService.models[i].name === this.classFilter) {
        classId = this._classService.models[i].id;
        break;
      }
    }
    this.charactersList = this.charactersList.filter(character => character.classId === classId);
  }

  // Aplicar filtro por raridade, se não for 'ALL'
  if (this.rarityFilter !== 'ALL') {
    this.charactersList = this.charactersList.filter(character => character.rarity === this.rarityFilter);
  }

  // Aplicar filtro por tipo, se não for 'ALL'
  if (this.typeFilter !== 'ALL') {
    if (this.typeFilter === "Boss") {
      this.charactersList = this.charactersList.filter(character => character.type === 3);
    } else if (this.typeFilter === "Minions Collectable") {
      this.charactersList = this.charactersList.filter(character => character.type === 2 && character.isCollectible);
    } else if (this.typeFilter === "Minions(All)") {
      this.charactersList = this.charactersList.filter(character => character.type === 2);
    } else if (this.typeFilter === "Subbosses") {
      this.charactersList = this.charactersList.filter(character => character.type === 4);
    } else if (this.typeFilter === "Collectible (Boss + Minion Collectible)") {
      this.charactersList = this.charactersList.filter(character => character.type === 3);     
    }
  }

  this.characterListIds = [];
  this.getCharactersIds(this.charactersList);
  this.description = `Showing ${this.charactersList.length} results`;
}

  getCharactersIds(charactersList) {
    charactersList.forEach((x) => this.characterListIds.push(x.id));
  }
  
  generalFilter(i): boolean
  {
    let result: boolean = false;
    if(this.classFilter == 'ALL' && this.rarityFilter == 'ALL')
      result = this.areaFilter == this.allCharacters[i].areaId;
    else if(this.areaFilter == 'ALL' && this.classFilter == 'ALL')
      result = this.allCharacters[i].rarity == this.rarityFilter;
    else if(this.areaFilter == 'ALL' && this.rarityFilter == 'ALL')
      result = this.allCharacters[i].classId == this.classFilter;

    else if(this.areaFilter == 'ALL')
    {
      result = this.allCharacters[i].classId == this.classFilter &&
      this.allCharacters[i].rarity == this.rarityFilter
    }
    else if(this.classFilter == 'ALL')
    {
      result = this.areaFilter == this.allCharacters[i].areaId && 
      this.allCharacters[i].rarity == this.rarityFilter
    }
    else if(this.rarityFilter == 'ALL')
    {
      result = this.areaFilter == this.allCharacters[i].areaId && 
      this.allCharacters[i].classId == this.classFilter 
    }
    else
      result = this.areaFilter == this.allCharacters[i].areaId && 
      this.allCharacters[i].classId == this.classFilter && 
      this.allCharacters[i].rarity == this.rarityFilter

    return result;
  }
  
  filterByRarity()
  {
    this.charactersList = [];
    for(let i = 0; i < this.allCharacters.length; i++)
      if(this.allCharacters[i].rarity == this.rarityFilter)
        this.charactersList.push(this.allCharacters[i]);
  }

  filterCharactersByArea()
  {
    this.validaCharacters = [];

    for(let i = 0; i < this.allCharacters.length; i++)
      if(this.areaFilter == this.allCharacters[i].areaId)
        this.validaCharacters.push(this.allCharacters[i]);

    this.charactersList = [];
    this.charactersList = this.validaCharacters;
    this.characterListIds = [];
    this.getCharactersIds(this.charactersList);
  }

  isCharacterTypeBossMinionSubboss = (character:Character): boolean => 
    character.type == 3 || character.type == 4 || character.type == 2;

  filterCharactersByClass()
  {
    this.charactersList = [];

    for(let i = 0; i < this._classService.models.length; i++)
      if(this._classService.models[i].name == this.classFilter)
      {
        this.classFilter = this._classService.models[i].id;
        break;
      }

    for(let i = 0; i < this.allCharacters.length; i++)
      if(this.allCharacters[i].classId == this.classFilter) this.charactersList.push(this.allCharacters[i]);

    this.characterListIds = [];
    this.getCharactersIds(this.charactersList);
  }

  filterCharactersByRarity()
  {
   
    this.listAllCharacters = this.contentCharacters();
    this.charactersList = []; 

    for (let index = 0; index < this.listAllCharacters.length; index++) {
      if (this.listAllCharacters[index]?.rarity?.includes(this.rarityFilter)) {
        this.charactersList.push(this.listAllCharacters[index]);
      }      
    }  
      this.characterListIds = [];
      this.getCharactersIds(this.charactersList);
      return this.charactersList;
  }

  filterCharactersByType()
  {
    this.charactersList = [];
    if(this.typeFilter === "Boss")
    {
      for(let i = 0; i < this.allCharacters.length; i++)
      {
        let character: Character = this._characterService.svcFindById(this.allCharacters[i].id);
        if(character.type == 3) this.charactersList.push(this.allCharacters[i]);
      }
    }
    else if(this.typeFilter === "Minions Collectable")
    {
      for(let i = 0; i < this.allCharacters.length; i++)
      {
        let character = this._characterService.svcFindById(this.allCharacters[i].id);
        if(character.type == 2 && character.isCollectible) this.charactersList.push(this.allCharacters[i]);
      }
    }
    else if(this.typeFilter === "Minions(All)")
    {
      for(let i = 0; i < this.allCharacters.length; i++)
      {
        let character = this._characterService.svcFindById(this.allCharacters[i].id);
        if(character.type == 2) this.charactersList.push(this.allCharacters[i]);
      }
    }
    else if(this.typeFilter === "Subbosses")
    {
      for(let i = 0; i < this.allCharacters.length; i++)
      {
        let character = this._characterService.svcFindById(this.allCharacters[i].id);
        if(character.type == 4) this.charactersList.push(this.allCharacters[i]);
      }
    }
    else if(this.typeFilter === "Collectible (Boss + Minion Collectible)")
    {
      for(let i = 0; i < this.allCharacters.length; i++)
      {
        let character = this._characterService.svcFindById(this.allCharacters[i].id);
        if((character.type == 2 && character.isCollectible) || character.type == 3) 
          this.charactersList.push(this.allCharacters[i]);
      }
    } 
    this.characterListIds = [];
    this.getCharactersIds(this.charactersList);  
  }

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  convertLstIdsToCharacterList()
  {
    this.charactersList = this._characterService.models.filter(char=> this.lstIds.includes(char.id));
  }

  changeSelectedRarity(character: Character, rarity: string)
  {
    character.rarity = rarity;
    character.rarityId = this.rarityList.find((x) => x.name == rarity).id;   
    this._characterService.svcToModify(character);
    this._characterService.toSave();
    this.lstAfterFetchList();
  }

  filterRarity(rarity: string)
  {    
    this.rarityFilter = rarity;
    const listSelect = {rarityFilter: this.rarityFilter};
    this.selectedRarity = this.rarityFilter; // Atualiza o valor selecionado
    this.listOptionSelectedCharacters.push(listSelect);
    this.filterCharacters();
  }

  filterType(type: string)
  {
    this.typeFilter = type;
    this.selectedType = this.typeFilter;
    this.listOptionSelectedCharacters.push({typeFilter: this.typeFilter});
    this.filterCharacters();
  }

  filterArea(id: string)
  {
    this.areaFilter = id;
    this.selectedArea = this.areaFilter;
    this.listOptionSelectedCharacters.push({areaFilter: this.areaFilter});
    this.filterCharacters();
  }

  filterClass(id: string)
  {
    this.classFilter = id;
    this.selectedClass = this.classFilter;
    this.listOptionSelectedCharacters.push({classFilter: this.classFilter});
    this.filterCharacters();
  }

  selectCollectible(character)
  {
    this._router.navigate(['collectibleRecord'], {queryParams: {collectibles:this.lstIds, character:character.id, charactersListIds: this.characterListIds, listOptionCharacters: JSON.stringify(this.listOptionSelectedCharacters)}, skipLocationChange: true});
  }

  checkElemental(character: Character):boolean
  {
    let char = this._elementalInfoService.models.filter(status => status.character === character.id);
    let value = false;
    for(let i = 0; i < char.length; i++)
    {
      if( char[i]?.spDef|| char[i]?.resistence|| char[i]?.weakeness|| char[i]?.spAtk|| char[i]?.ascensionOrder)
      {
        value = true;
        break;
      }
    }
    this.checker(character, 'status', value.toString());

    return value;
  }

  checkBattleInferior(character: Character):boolean {
    const battleInferior = this._battleInferiorService.models.filter((x) => x.character === character.id);
    let value = false;
    for(let i = 0; i < battleInferior.length; i++)
    {
      if( battleInferior[i]?.atk || battleInferior[i]?.atkLim || battleInferior[i]?.bl || battleInferior[i]?.def || battleInferior[i]?.hp)
      {
        value = true;
        break;
      }
    }
    this.checker(character, 'status', value.toString());
    return value;
  }
  
  checkIfPrimal(character: Character):boolean
  {
    let primalModifier = this.getPrimal(character);
    for(let j = 0; j < primalModifier?.hard?.primalModifier?.length; j++)
      if(primalModifier.hard.primalModifier[j].fieldValue != undefined)
        return true;
    
    return false;
  }

  checkIfSpecialSkill(character: Character):boolean
  {
    let specialSkill = this._specialSkillsService.models.filter((x) => x.idChacracter === character.id && x.listSpecialSkills.length > 0);
   
    if (specialSkill.length > 0) {
      return true;
    }
    return false;
  }

  getPrimal(character: Character)
  {
    for(let i = 0; i < this._primalService.models.length; i++)
      if(this._primalService.models[i].character == character.id)
        return this._primalService.models[i];
    return null;
  }

  checkIfBattle(character: Character):boolean
  {
    let char = this._battleService.models.filter( status => status.character === character.id)
    let value = false
    if(char.length === 0) value = false;    
    else
    {
      for(let i = 0; i < char[0].baseLevel.length; i++)
        if( char[0]?.hp[i]|| char[0]?.atk[i]|| char[0]?.def[i]|| char[0]?.atkLim[i])
        {
          value = true;
          break;
        }
    }
    this.checker(character, 'battle', value.toString());

    return value;
  }

  checkIfAilmentDefenses(character: Character):boolean {

    let value = false
     let ailDef = this._ailmentDefensesService.models.filter((ail) => ail.characterId === character.id);    

     if(ailDef.length === 0){
      value = false;
     } 
     else {
      for (let index = 0; index < ailDef.length; index++) {

        if(ailDef[index]?.ailmentDefensesList.length > 0) {
          value = true;
          break;
        } else {
          value = false;
          break;
        }      
      }     
    }

   return value;
  }

  //creates a list to help sort the characters
  checker(character: Character, property, value)
  {
    let c;
    let t = this.collectibleRecord.filter(col=> 
    {
      if(col.id === character.id && (character.type == 3 || character.isCollectible === true)) return true;      
      return null;
    })
    if(t.length === 0)
    {
      c = new CollectibleRecord(character.id);
      c[property] = value;
      this.collectibleRecord.push(c);
    }
    else
    {
      this.collectibleRecord.forEach(char=> 
      {
        if(char.id == character.id) char[property] = value;       
      })
    }   
  } 

  //Sort the list created on the checker method
  sortable()
  {
    let sortListOne = [];
    let sortListTwo = [];
    let sortListThree = [];
    let sortListFour = [];
    
    this.collectibleRecord.forEach(a =>
    {
      if(a.status == 'true' && a.primal == 'true'  && a.battle == 'true')
        sortListThree.push(a);
      else if
      ((a.status == 'true' && a.primal == 'false' && a.battle == 'false') || 
        (a.primal == 'true'  && a.status  == 'false' && a.battle == 'false') || 
        (a.battle == 'true' && a.status  == 'false' && a.primal == 'false'))
        sortListOne.push(a);

      else if
      (((a.status == 'true' || a.primal == 'true')  && a.battle == 'false') || ((a.battle == 'true' || a.primal == 'true') &&
        a.status == 'false') || ((a.status == 'true' || a.battle == 'true')  && a.primal == 'false'))
        sortListTwo.push(a);
      else
        sortListFour.push(a);
    })
      
    this.collectibleRecord = [];
    this.collectibleRecord = sortListFour.concat(sortListOne).concat(sortListTwo).concat(sortListThree);
  }

  // Variável de estado para controlar a direção da ordenação
  private isDescending = false; // Inicia como "maior para menor"
  sortCollectibleRecord()
  {
    this.sortable();
    this.sortCollectibleRecordOrder *= -1;
    if(this.sortCollectibleRecordOrder == 1) this.collectibleRecord.reverse();
      
    let ids = [];
    this.collectibleRecord.forEach(c=> ids.push(c.id));

    let last = [];
    this.lstIds.forEach(id=>
    {
      if(!ids.includes(id))        
        last.push(id);
    })
    let t = ids.concat(last);

    // Método para ordenar this.charactersList com base no número de verificações verdadeiras
    this.charactersList.sort((a, b) => {
      const calculateWeight = (character: any): number => {
        let weight = 0;

        if (this.checkIfBattle(character)) weight++;
        if (this.checkBattleInferior(character)) weight++;
        if (this.checkElemental(character)) weight++;
        if (this.checkIfAilmentDefenses(character)) weight++;
        if (this.checkIfPrimal(character)) weight++;
        if (this.checkIfSpecialSkill(character)) weight++;

        return weight;
      };

      const weightA = calculateWeight(a);
      const weightB = calculateWeight(b);

      // Alterna a ordenação com base na direção atual
      return this.isDescending ? weightB - weightA : weightA - weightB;
    });

    // Inverte a direção da ordenação para o próximo clique
    this.isDescending = !this.isDescending; 
   }

  contentCharacters() {
    const list = this.allCharacters;
    return list;
  }

  search(searchWord: string) {
    searchWord = searchWord.toLowerCase();
    this.charactersList = this.allCharacters;
  
    if (searchWord === '') {
      return this.filterCharacters();
    }
  
    this.charactersList = this.charactersList.filter(character => character.name.toLowerCase().includes(searchWord));
    this.description = `Showing ${this.charactersList.length} results`;
    return this.charactersList;
  }
  public switchToTab(tab: string) {
    this.activeTab = tab;
    this.isCtrEvMax = tab === 'ctr-ev-max';
    localStorage.setItem(
      `CollectibleRarityComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );
  }

  clickBtnKnoledgeHandler(event: boolean): void {
    if (event) {
      this.activeTab = 'keywords';
      this.isCtrEvMax = false;
      // Atualizar o armazenamento local para persistir o estado
      localStorage.setItem(`CollectibleRarityComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
    }
  }

}
