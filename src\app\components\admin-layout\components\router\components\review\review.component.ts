import { Component, OnInit } from '@angular/core';
import { Area, Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { TypeColorReviewPipe } from 'src/app/pipes/type-color-review.pipe';
import { DialogueService, DilemmaBoxService, DilemmaService, EventService, MarkerService, MissionService, OptionBoxService, OptionService, ReviewService, SpeechService, StoryBoxService, UserSettingsService } from 'src/app/services';
import { AreaService } from 'src/app/services/area.service';
import { DisplayCheckService } from 'src/app/services/display-check.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { Review } from 'src/lib/darkcloud';
import { Index, comparable, compareCodes } from 'src/lib/others';
import { Typing } from '../../../../../../lib/@pres-tier';
import { Location2Pipe } from '../../../../../../pipes/location2.pipe';
import { StoryProgressPipe } from '../../../../../../pipes/story-progress.pipe';
import { Router } from '@angular/router';

export interface IReviewWithErrors {
  id: string;
  dialogueId: string;
  typeName: string;
  typeColor: string;
  levelId: string;
  namePosition: any;
  type?: number;
  parametersWithErrors: string[];
}


@Component({
  selector: 'app-review',
  templateUrl: './review.component.html',
  styleUrls: ['./review.component.scss']
})

export class ReviewComponent implements OnInit {
  private _isViewingIgnored: boolean;
  public isLevelEventAmountZero: boolean;
  public get isViewingIgnored(): boolean {
    return this._isViewingIgnored;
  }
  public set isViewingIgnored(value: boolean) {
    this._isViewingIgnored = value;
    this.refresh();
  }

  reviewResults: Index<Review.Result>;
  storyProgressesAndOptionsWithErrorsIds: string[] = [];
  missionIdsWithErrors: string[] = [];
  levelIdsWithErrors: string[] = [];
  microloopIdsWithErrors: string[] = [];
  reviewWithErrors: IReviewWithErrors[] = [];
  sortNameOrder = +1;
  storyId: string[] = [];

  constructor(
    private _reviewService: ReviewService,
    private _router: Router,
    private _areaService: AreaService,
    private _missionService: MissionService,
    private readonly _userSettingsService: UserSettingsService,
    private _displayCheckService: DisplayCheckService,
    private _roadblockService: RoadBlockService,
    private _optionBoxService: OptionBoxService,
    private _storyboxService: StoryBoxService,
    private _markerService: MarkerService,
    private _eventService: EventService,
    private _dilemmaService: DilemmaService,
    private _speechService: SpeechService,
    private _dilemmaBoxService: DilemmaBoxService,
    private location2Pipe: Location2Pipe,
    private _storyProgressPipe: StoryProgressPipe,
    private _typeColorReviewPipe: TypeColorReviewPipe,
    private _optionService: OptionService,
    private _dialogueService: DialogueService,
    private _microloopService: MicroloopService,

  ) { }

  ngOnInit(): void {
    // this.filterEventsNotUsed();
    this._roadblockService.toFinishLoading();
    this.reviewResults = this._reviewService.reviewResults;
    this.refresh();
    this.getWithErrors();    
  }

  private async refresh() {
    this._reviewService.objIdsToReview['Speech'] = this._reviewService.objIdsToReview['Speech'].filter(x => {
      return this._displayCheckService.speechDisplayed(x);
    });

    this._reviewService.objIdsToReview['Event'] = this._reviewService.objIdsToReview['Event'].filter(x => {
      return this._displayCheckService.eventDisplayed(x);
    });

    this.levelIdsWithErrors = this._reviewService.objIdsToReview['Level'].filter
      (
        (id) => this.isViewingIgnored ? this._userSettingsService.getInformation(id)?.ignoreReview :
          !this._userSettingsService.getInformation(id)?.ignoreReview
      );

    this.microloopIdsWithErrors = this._reviewService.objIdsToReview['Microloop'].filter(x => {
      if (x.split('.')[0] == 'ML')
        return false;

      if (!this.isViewingIgnored) {
        if (!this._userSettingsService.getInformation(x)?.ignoreReview) {
          return true;
        }
        return false;
      }
      else {
        if (this._userSettingsService.getInformation(x)?.ignoreReview) {
          return true;
        }
        return false;
      }
    });

    this.storyProgressesAndOptionsWithErrorsIds = []
      .concat(this._reviewService.objIdsToReview['Option'])
      .concat(this._reviewService.objIdsToReview['Speech'])
      .filter((id) =>
        this.isViewingIgnored ? this._userSettingsService.getInformation(id)?.ignoreReview :
          !this._userSettingsService.getInformation(id)?.ignoreReview);
    this.filterMarkerNotUsed();


    this.missionIdsWithErrors = this._reviewService.objIdsToReview['Mission'].filter((id) =>
      this.isViewingIgnored ? this._userSettingsService.getInformation(id)?.ignoreReview :
        !this._userSettingsService.getInformation(id)?.ignoreReview
    );

    //Remove progress and options from the microloop
    this.storyProgressesAndOptionsWithErrorsIds = this.storyProgressesAndOptionsWithErrorsIds.filter(x => {
      return !x.includes("ML")
    });

    this.addEventWithObjectiveFieldEmpty();
    this.filterEventsNotUsed();

    // Sorts by area and level
    this.storyProgressesAndOptionsWithErrorsIds.sort((aId, bId) => {
      const pa = this._areaService.svcFindById(Area.getSubIdFrom(aId)).hierarchyCode;
      const pb = this._areaService.svcFindById(Area.getSubIdFrom(bId)).hierarchyCode;
      const c = compareCodes(pa, pb);
      if (c === 0) {
        return this.reviewResults[Level.getSubIdFrom(aId)]?.index > this.reviewResults[Level.getSubIdFrom(bId)]?.index ? 1 : -1;
      }
      else {
        return c > 0 ? 1 : -1;
      }
    });

    this.levelIdsWithErrors.sort((aId, bId) => {
      const pa = this._areaService.svcFindById(Area.getSubIdFrom(aId))?.hierarchyCode;
      const pb = this._areaService.svcFindById(Area.getSubIdFrom(bId))?.hierarchyCode;
      return compareCodes(pa, pb);
    });

    this.missionIdsWithErrors.sort((aId, bId) => {
      const pa = this._missionService.svcFindById(aId).name;
      const pb = this._missionService.svcFindById(bId).name;
      return comparable(pa) > comparable(pb) || pa === undefined ? 1 : -1;
    });

    this.filterRoadblockNotUsed();
    this.getDialogueEmptyField();
  } // En Refresh


  getDialogueEmptyField() {

    //Dilemma Box
    this._dilemmaBoxService.models.forEach((x) => {
      if (x.optionDilemmaIds.length === 0) {
        this.storyProgressesAndOptionsWithErrorsIds.push(x.id);
        this._reviewService.reviewResults[x.id] = { parametersWithErrors: [] };
        this._reviewService.reviewResults[x.id].parametersWithErrors = ['box'];
        this._reviewService.reviewResults[x.id].levelId = Level.getSubIdFrom(x.id);
        this._reviewService.reviewResults[x.id].dialogueId = Dialogue.getSubIdFrom(x.id, 'PT-BR');
      }
    });

    //Choice de Dilemma Box
    this._dilemmaService.models.forEach((dilemma) => {
      if (dilemma.message == undefined) {
        this.storyProgressesAndOptionsWithErrorsIds.push(dilemma.id);
        this.setReviewResults(dilemma.id);
      }
    });

    // Speech Dilemma Box
    this._speechService.models.forEach((answer) => {
      if (answer.id.includes("dlm") && !answer.message) {
        this.storyProgressesAndOptionsWithErrorsIds.push(answer.id);
        this.setReviewResults(answer.id);
      }
    });

    // Story
    this._storyboxService.models.forEach((story) => {
      if (story.storyProgressIds.length === 0) {
        this.storyProgressesAndOptionsWithErrorsIds.push(story.id);
        this._reviewService.reviewResults[story.id] = { parametersWithErrors: [] };
        this._reviewService.reviewResults[story.id].parametersWithErrors = ['box'];
        this._reviewService.reviewResults[story.id].levelId = Level.getSubIdFrom(story.id);
        this._reviewService.reviewResults[story.id].dialogueId = Dialogue.getSubIdFrom(story.id, 'PT-BR');
        this.storyId.push(story.id);
      }
    });

    //Choice e Investigation BOX
    this._optionBoxService.models.forEach((box) => {
      if (box.optionIds.length === 0) {
        this.storyProgressesAndOptionsWithErrorsIds.push(box.id);
        this._reviewService.reviewResults[box.id] = { parametersWithErrors: [] };
        this._reviewService.reviewResults[box.id].parametersWithErrors = ['box'];
        this._reviewService.reviewResults[box.id].levelId = Level.getSubIdFrom(box.id);
        this._reviewService.reviewResults[box.id].dialogueId = Dialogue.getSubIdFrom(box.id, 'PT-BR');
      }
    });

    // Choice Choice e Investigation BOX
    this._optionService.models.forEach((option) => {
      if (option.message == undefined) {
        this.storyProgressesAndOptionsWithErrorsIds.push(option.id);
        this.setReviewResults(option.id);

        this._optionBoxService.models.forEach((box) => {
          if (box.optionIds.includes(option.id)) {
            this._reviewService.reviewResults[option.id].type = box.type;
          }
        });
      }
    });


  }

  setReviewResults(id: string) {
    this._reviewService.reviewResults[id] = { parametersWithErrors: [] };
    this._reviewService.reviewResults[id].parametersWithErrors = ['message'];
    this._reviewService.reviewResults[id].levelId = Level.getSubIdFrom(id);
    this._reviewService.reviewResults[id].dialogueId = Dialogue.getSubIdFrom(id, 'PT-BR');

  }

  getWithErrors() {

    // Remove duplicatas do array this.storyProgressesAndOptionsWithErrorsIds
    this.storyProgressesAndOptionsWithErrorsIds = Array.from(new Set(this.storyProgressesAndOptionsWithErrorsIds));

    for (let index = 0; index < this.storyProgressesAndOptionsWithErrorsIds.length; index++) {
      const idErrors = this.storyProgressesAndOptionsWithErrorsIds[index];

      // Inicialize um objeto para essa posição do array caso ainda não exista
      if (!this.reviewWithErrors[index]) {
        this.reviewWithErrors[index] = {
          id: '',
          dialogueId: '',
          levelId: '',
          typeName: '',
          typeColor: '',
          namePosition: null,
          parametersWithErrors: []
        };
      }

      // Atribui os valores ao objeto na posição 'index' agora que está inicializado
      this.reviewWithErrors[index].id = idErrors;
      this.reviewWithErrors[index].dialogueId = this._reviewService.reviewResults[idErrors].dialogueId;
      this.reviewWithErrors[index].levelId = this._reviewService.reviewResults[idErrors].levelId;
      this.reviewWithErrors[index].namePosition = this.location2Pipe.transform(idErrors);
      this.reviewWithErrors[index].type = this._reviewService.reviewResults[idErrors].type;
      this.reviewWithErrors[index].typeName = Typing.typeNameReview(this._storyProgressPipe.transform(idErrors), this.reviewWithErrors[index].type);
      this.reviewWithErrors[index].typeColor = this._typeColorReviewPipe.transform(this.reviewWithErrors[index].typeName);

      // Adiciona os parâmetros com erro
      this._reviewService.reviewResults[idErrors].parametersWithErrors.forEach((x) => {
        this.reviewWithErrors[index].parametersWithErrors.push(x);
      });
    }

    //Verifica se existe algum parâmetro com erro
    this.reviewWithErrors.forEach((x) => {
      const dialogue = this._dialogueService.svcCloneById(x.dialogueId);
      if (!dialogue) {
        const microloop = this._microloopService.svcFindById(x.id);
        /*   const microloop = this._microloopService.svcCloneById(x.dialogueId);
           
              if (!microloop) {
                x.typeName =  'Orphan';
                x.typeColor = 'red';    
              } 
              */
      }
    });


    this.lineupOrderReviewLevels();

  }

  lineupOrderReviewLevels() {
    this.sortNameOrder *= +1;
    this.reviewWithErrors.sort((a, b) => {
      return this.sortNameOrder * a.namePosition.localeCompare(b.namePosition);
    });
  }

  //Mission event validated with empty field
  addEventWithObjectiveFieldEmpty() {
    for (let i = 0; i < this._eventService.models.length; i++) {
      if ((this.isCompleteobjectiveEventtype(i) || this.isFailobjectiveEventtype(i)) && this.isObjectiveidFieldEmpty(i)) {
        this.storyProgressesAndOptionsWithErrorsIds.push(this._eventService.models[i].id);
      }
    }
  }

  isCompleteobjectiveEventtype = (i: number): boolean => +this._eventService.models[i].type == 6;
  isFailobjectiveEventtype = (i: number): boolean => +this._eventService.models[i].type == 7;
  isObjectiveidFieldEmpty = (i: number): boolean => !this._eventService.models[i].objectiveId;

  filterMarkerNotUsed() {
    //Add the empty markers
    for (let i = 0; i < this._markerService.models.length; i++) {
      let markerId = this._markerService.models[i].id;
      let marker = this._markerService.svcFindById(markerId);

      /*  if(this.isBlockMarkerType(marker) && this.isMarkerUndefined(marker) || this.isUnlocklevelMarkerUndefined(marker) ||
           this.isUnlocklevelIdMarkerUndefined(marker) || this.isReleasedialogueMarkerUndefined(marker) || this.isMarkerRestartConditionOperatorUndefined(marker) ||
           this.isPinMarkerUndefined(marker) || this.isRestartUnlockCondition(marker) || this.isMarkerRestartTotalUndefined(marker) ||
           this.isReleasedialogueMarkerUndefined (marker) || this.isMarkerRestartAmountRequired(marker) ||
           this.isMarkerChoosedConditionUndefined || this.isBossMarkerUndefined(marker) ||this.isMarkerChoosedConditionAmountRequireConditionOperatordUndefined(marker)
          )
        {
         
        } */
      if (this.reviewResults[markerId].parametersWithErrors.length > 0) this.storyProgressesAndOptionsWithErrorsIds.push(markerId);

    }

  }
  //Validates Marker component fields
  /*
  isMarkerUndefined = (marker) => marker.type == undefined;
  isUnlocklevelMarkerUndefined = (marker) => marker.type == 2 && !marker.levelId;
  isUnlocklevelIdMarkerUndefined = (marker):boolean => marker.type == 2 && marker.levelId == undefined;
  isBlockMarkerType = (marker) => +marker.type != 4 || +marker.type != 5 || +marker.type != 12 || +marker.type != 11;
  isReleasedialogueMarkerUndefined = (marker) => +marker.type == 3 && !marker.levelId;
  isPinMarkerUndefined = (marker) => +marker.type == 7 && !marker.levelId;
  isBossMarkerUndefined = (marker) => +marker.type == 8 && !marker.characterId || +marker.type == 0;

  isRestartUnlockCondition = (marker) => +marker.type == 10 && !marker.unlockCondition;
  isMarkerRestartTotalUndefined = (marker) => +marker.type == 10 && marker.unlockCondition && marker.choosedCondition == undefined 
  && marker.amountRequired == undefined && marker.conditionOperator == undefined;
  isMarkerChoosedConditionUndefined = (marker) => +marker.type == 10 && marker.unlockCondition && marker.choosedCondition == undefined && 
  marker.conditionOperator && marker.amountRequired;
  isMarkerChoosedConditionAmountRequireConditionOperatordUndefined = (marker) => +marker.type == 10 && marker.unlockCondition && marker.choosedCondition 
  && marker.amountRequired == undefined && marker.conditionOperator == undefined;
  isMarkerRestartAmountRequired = (marker) => +marker.type == 10 && marker.unlockCondition && marker.choosedCondition 
  && marker.amountRequired == undefined && marker.conditionOperator;
  isMarkerRestartConditionOperatorUndefined = (marker) => +marker.type == 10 && marker.unlockCondition && marker.choosedCondition 
  && marker.amountRequired && marker.conditionOperator == undefined;
  */


  filterEventsNotUsed() {
    for (let i = 0; i < this._eventService.models.length; i++) {
      let eventId = this._eventService.models[i].id;
      let event = this._eventService.svcFindById(eventId);

      if (this.isRemoveSpecialItem(event) && this.isEventNotUndefined(event) || this.isPlayvideoEventUndefined(event) ||
        this.isTutorilEventUndefined(event) || this.isAssignmissionUndefined(event) || this.isCompleteobjectEventUndefined(event) ||
        this.isFailobjectiveEventUndefined(event) || this.isGiveitemEventUndefined(event) || this.isGiveitemEventAmountUndefined(event) ||
        this.isReceiveitemEventUndefined(event) || this.isReceiveitemEventAmountUndefined(event) || this.isTradeitemEventAmountUndefined(event) ||
        this.isTradeitemEventUndefined(event) || this.isMicroloopEventUndefined(event)) {
        this.storyProgressesAndOptionsWithErrorsIds.push(eventId);
      }
    }
  }
  //Validates the fields of the Add Item Event component in LEVELS
  isMicroloopEventUndefined = (event): boolean => +event.type == 11 && !event.microloopId;
  isTradeitemEventUndefined = (event): boolean => +event.type == 2 && !event.itemId;
  isReceiveitemEventUndefined = (event): boolean => +event.type == 0 && !event.itemId;
  isGiveitemEventUndefined = (event): boolean => +event.type == 1 && !event.itemId;
  isGiveitemEventAmountUndefined = (event): boolean => +event.type == 1 && (event.amount == undefined || event.amount == null);
  isReceiveitemEventAmountUndefined = (event): boolean => +event.type == 0 && (event.amount == undefined || event.amount == null);
  isTradeitemEventAmountUndefined = (event): boolean => +event.type == 2 && (event.amount == undefined || event.amount == null);
  isFailobjectiveEventUndefined = (event): boolean => +event.type == 7 && !event.missionId;
  isCompleteobjectEventUndefined = (event): boolean => +event.type == 6 && !event.missionId;
  isRemoveSpecialItem = (event): boolean => +event.type != 8 || +event.type != 12;
  isEventNotUndefined = (event): boolean => event.type == undefined;
  isPlayvideoEventUndefined = (event): boolean => +event.type == 4 && !event.videoId;
  isTutorilEventUndefined = (event): boolean => +event.type == 5 && !event.tutorialId;
  isAssignmissionUndefined = (event): boolean => (+event.type == 3 && !event.missionId);


  filterRoadblockNotUsed() {
    for (let i = 0; i < this._roadblockService.models.length; i++) {
      //For it type we have an empty field.
      if (this.isDefeatedbossRBUndefined(i) || this.isObtaineditemRBUndefined(i) || this.isTalkedcharacterRBUndefined(i) ||
        this.isCollectedclassRBUndefined(i) || this.isSpokeinRBUndefined(i) || this.isKarmicequilibriumRBUndefined(i)
      ) {
        this.setReviewResultsWithRoadblocks(this._roadblockService.models[i].id);
        if (this.isToAddIgnoredRoadblock(i, true) && this.removeUnremovedRoadblock(this._roadblockService.models[i])) {
          this.storyProgressesAndOptionsWithErrorsIds.push(this._roadblockService.models[i].id);
        }
        else if (this.isToAddIgnoredRoadblock(i) && this.removeUnremovedRoadblock(this._roadblockService.models[i])) {
          this.storyProgressesAndOptionsWithErrorsIds.push(this._roadblockService.models[i].id);
        }
      }
    }
  }

  isDefeatedbossRBUndefined = (i): boolean => !this._roadblockService.models[i].hard.characterId && +this._roadblockService.models[i].hard.type == 0;
  isObtaineditemRBUndefined = (i): boolean => !this._roadblockService.models[i].hard.itemId && +this._roadblockService.models[i].hard.type == 1;
  isTalkedcharacterRBUndefined = (i): boolean => !this._roadblockService.models[i].hard.characterId && +this._roadblockService.models[i].hard.type == 2;
  isCollectedclassRBUndefined = (i): boolean => !this._roadblockService.models[i].hard.klassId && +this._roadblockService.models[i].hard.type == 4;
  isSpokeinRBUndefined = (i): boolean => !this._roadblockService.models[i].hard.spokeElementId && +this._roadblockService.models[i].hard.type == 5;
  isKarmicequilibriumRBUndefined = (i): boolean => !this._roadblockService.models[i].hard.itemAmount && +this._roadblockService.models[i].hard.type == 6;

  removeUnremovedRoadblock(roadblock: RoadBlock): boolean {
    for (let i = 0; i < this._optionBoxService.models.length; i++) {
      if (this.isRBInsideOptionbox(roadblock, i)) {
        return true;
      }
      if (this.isLastOptionboxIndex(i)) {
        for (let j = 0; j < this._storyboxService.models.length; j++) {
          if (this.isRBInsideStorybox(roadblock, j)) {
            return true;
          }
          if (this.isLastStoryboxIndex(j)) {
            this._roadblockService.svcToRemove(roadblock.id);
            return false;
          }
        }
      }
    }
    return false;
  }

  isRBInsideOptionbox = (roadblock, i): boolean => roadblock.StoryBoxId == this._optionBoxService.models[i].id;
  isRBInsideStorybox = (roadblock, j): boolean => roadblock.StoryBoxId == this._storyboxService.models[j].id;
  isLastStoryboxIndex = (j): boolean => j == this._storyboxService.models.length - 1;
  isLastOptionboxIndex = (i): boolean => i == this._optionBoxService.models.length - 1;

  isToAddIgnoredRoadblock(i: number, add: boolean = false): boolean {
    return add ? !this._userSettingsService.getInformation(this._roadblockService.models[i].id)?.ignoreReview && !this.isViewingIgnored :
      this._userSettingsService.getInformation(this._roadblockService.models[i]?.id)?.ignoreReview && this.isViewingIgnored;
  }
  //Look the method name on documentation for more information.
  protected setReviewResultsWithRoadblocks(id: string) {
    this._reviewService.reviewResults[id] = { parametersWithErrors: [] };
    this._reviewService.reviewResults[id].parametersWithErrors = ['field'];
    this._reviewService.reviewResults[id].levelId = Level.getSubIdFrom(id);
    this._reviewService.reviewResults[id].dialogueId = Dialogue.getSubIdFrom(id, 'PT-BR');
  }

  accessMission(missionId: string) {
    this._router.navigate(['missions'], { fragment: missionId });
  }

  accessLevel(levelId: string) {
    this._router.navigate(['levels'], { fragment: levelId });
  }

  accessSPO(spId: string) {
    if (spId.includes('ML')) {
      let levelId = this._roadblockService.getLevelId(spId);
      let dialogueId = this._roadblockService.getDialogueId(spId);

      this._router.navigate(['microloops/' + levelId + '/dialogues/' + dialogueId], { fragment: spId });
    }
    else {
      this._router.navigate(
        [
          'levels/' + this.reviewResults[spId].levelId + '/dialogues/' + this.reviewResults[spId].dialogueId,
        ],
        { fragment: spId }
      );
    }
  }

  accessMicroloop(loopId: string) {
    let containerId = loopId.split('.')[0];
    this._router.navigate(
      [
        'microloopList/' + containerId + '/microloops/'
      ],
      { fragment: loopId }
    )
  }

  async ignoreReview(id: string, ignore: boolean) {
    this.addRoadblockInsideObjectInformation(id, ignore);
    await this._userSettingsService.updateInformation(id, 'ignoreReview', ignore);
    await this._userSettingsService.modify();
    this.refresh();
  }

  async addRoadblockInsideObjectInformation(id: string, ignore: boolean) {
    let keys = Object.keys(this._userSettingsService.data.objectInformations);
    if (keys.includes(id)) {
      this._userSettingsService.data.objectInformations[`${id}`] =
      {
        ignoreReview: ignore
      }
    }
    await this._userSettingsService.modify();
  }

}
