import { HttpClient } from '@angular/common/http';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Sound, TierList } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Language } from 'src/app/lib/@bus-tier/models/Language';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { DataHealer } from 'src/app/lib/DataHealer';
import { ServiceSettingsManager } from 'src/app/lib/ServiceSettingsManager';
import { PackInfo, ServiceSetting } from 'src/app/lib/interfaces';
import {
  AfflictionTableService,
  AilmentTableService,
  AnimationsSelectorService,
  AnswerDilemmaBoxService,
  AreaService,
  BattleInferiorService,
  BoostIdBlockservice,
  BoostTableService,
  CategoriesXStressStatesService,
  CategoryStatusEffectService,
  CharacterService,
  CharactersSelectorService,
  ClassService,
  ConditionService,
  ConfigThresholdService,
  DCGuideService,
  DCKnowledgeGuideService,
  DefensiveIdBlockservice,
  DefensiveTableService,
  DialogueService,
  AttributeDiceFrustrationService,
  DilemmaBoxService,
  DilemmaService,
  DispelIdBlockservice,
  DispelTableService,
  EffectService,
  EmotionService,
  EventService,
  AttributeCheckService,
  HealingIdBlockservice,
  HealingTableService,
  HybridIdBlockservice,
  HybridTableService,
  IndexStorageService,
  ItemService,
  ItemsSelectorService,
  KeywordService,
  KeywordsTagsService,
  KnowledgeService,
  LevelService,
  LevelUpgradeService,
  MahankaraBehaviorTableService,
  MahankaraCategoriesService,
  MahankaraConcatenationsService,
  MahankaraGroupingsService,
  MarkerService,
  MinionStatsService,
  MissionService,
  NegativeIdBlockservice,
  ObjectiveService,
  OptionBoxService,
  OptionService,
  ParticleService,
  QnAService,
  RepetitionStatusEffectService,
  ReviewService,
  RpgService,
  SceneryService,
  SoundService,
  SpecialSkillsService,
  SpeechService,
  StoryBoxService,
  SubContextKnowledgeService,
  SubContextService,
  ThemeService,
  TutorialService,
  UniqueCharactersByHCAndBLService,
  UserSettingsService,
  VideoService,
  WeaponService,
  WeaponUpgradeService,
  KnowledgeDiceFrustrationService,
  KnowledgeCheckService,
  TotalArchetypesService,
  CastsGoldenService,
  CastsSoulsService,
  CastsSigilosService,
  LevelHelperService,
  CTREVMAXService,
  CTRCollectibleService,
  LUKCollectibleService,
  INTCollectibleService,
  SPDCollectibleService,
  OpenAIEnvironmentService,
  OpenAIKeyGeneralService
} from 'src/app/services';
import { WeaponRarityService } from 'src/app/services/WeaponRarity.service';
import { AdamantiumMiningService } from 'src/app/services/adamantium-mining.service';
import { AdamantiumStorageService } from 'src/app/services/adamantium-storage.service';
import { AilmentService } from 'src/app/services/ailment.service';
import { AtributteService } from 'src/app/services/atributte.service';
import { BattleUpgradeService } from 'src/app/services/battle-upgrade.service';
import { BlueprintArchiveService } from 'src/app/services/blueprint-archive.service';
import { BonusService } from 'src/app/services/bonus.service';
import { ChestService } from 'src/app/services/chest.service';
import { CodeBlockDropService } from 'src/app/services/code-block-drop.service';
import { CollectibleRarityCodeBlocksService } from 'src/app/services/collectibleRarityCodeBlocks.service';
import { CollectibleRarityGoldService } from 'src/app/services/collectibleRarityGold.service';
import { CollectibleRaritySoulsService } from 'src/app/services/collectibleRaritySouls.service';
import { CommonWeaponsService } from 'src/app/services/commonWeaponsService';
import { CustomService } from 'src/app/services/custom.service';
import { DisplayCheckService } from 'src/app/services/display-check.service';
import { DropService } from 'src/app/services/drop.service';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { HellniumMiningService } from 'src/app/services/hellnium-mining.service';
import { HellniumStorageService } from 'src/app/services/hellnium-storage.service';
import { IngredientDropService } from 'src/app/services/ingredient-drop.service';
import { IngredientVarianceService } from 'src/app/services/ingredient-variance.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LaboratoryService } from 'src/app/services/laboratory.service';
import { LanguageService } from 'src/app/services/language.service';
import { MapsService } from 'src/app/services/maps.service';
import { MasteryService } from 'src/app/services/mastery.service';
import { MemoryModuleService } from 'src/app/services/memorymodule.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { MinigameService } from 'src/app/services/minigame.service';
import { ModifierService } from 'src/app/services/modifier.service';
import { NegativeTableService } from 'src/app/services/negative-table.service';
import { ParticleDropService } from 'src/app/services/particle-drop.service';
import { ParticleVarianceService } from 'src/app/services/particle-variance.service';
import { PassiveSkillService } from 'src/app/services/passiveSkill.service';
import { PowerUpService } from 'src/app/services/powerup.service';
import { PowerUpStatService } from 'src/app/services/powerupstat.service';
import { PrimalModifierService } from 'src/app/services/primal-modifier.service';
import { ProfanariumService } from 'src/app/services/profanarium.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SilicatosService } from 'src/app/services/silicatos.service';
import { SoulsGrinderService } from 'src/app/services/souls-grinder.service';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { SpecialWeaponServiceHC } from 'src/app/services/special-weaponHC.service';
import { StatusEffectService } from 'src/app/services/status-effect.service';
import { StatusInfoService } from 'src/app/services/status-info.service';
import { StatusService } from 'src/app/services/status.service';
import { StoryExpansionPkgService } from 'src/app/services/story-expansion-pkg.service';
import { TabService } from 'src/app/services/tab.service';
import { TagService } from 'src/app/services/tag.service';
import { TierService } from 'src/app/services/tier.service';
import { TitaniumMiningService } from 'src/app/services/titanium-mining.service';
import { TitaniumStorageService } from 'src/app/services/titanium-storage.service';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UpgradesService } from 'src/app/services/upgrades.service';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { Alert, Versioning } from 'src/lib/darkcloud';
import { Filing } from 'src/lib/darkcloud/angular/dsadmin';
import { VOICES_INDEX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/paths';
import { ModelService } from 'src/lib/darkcloud/angular/easy-mvc';
import { AilmentDefensesService } from '../../../../../../services/ailmentDefeneses.service';
import { ConditionTriggerService } from '../../../../../../services/conditionTrigger.service';
import { DurationService } from '../../../../../../services/duration.service';
import { LevelPointsService } from '../../../../../../services/levelPoints.service';
import { PassiveAllowedService } from '../../../../../../services/passiveAllowed.service';
import { RelicUsesService } from '../../../../../../services/relicUses.service';
import { UniqueCharactereService } from '../../../../../../services/uniqueCharacteres.service';
import { IconListComponent } from './components/icon-list/icon-list.component';
import { KeywordListComponent } from './components/keyword-list/keyword-list.component';
import { SituationalModifierService } from 'src/app/services/situational-modifier.service';
import { ArchetypeListService } from 'src/app/services/archetypesList.service';
import { AmplifiersService } from 'src/app/services/amplifiers';
import { LocationPipe } from 'src/app/pipes/location.pipe';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { ActivatedRoute, Router } from '@angular/router';

interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}


@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
})
export class SettingsComponent implements OnInit, OnDestroy {
  @ViewChild(IconListComponent) iconListComponent: IconListComponent;
  @ViewChild(KeywordListComponent) KeywordListComponent: KeywordListComponent;

  public fileTitle: string;
  public loadedTempDSA: Filing.DSA9;
  timeout: any;
  jsondsa9: any;
  public minutes = 0;
  public lastLoadedDataInfo: {
    date: Date;
    info: { title: string; entriesCount: number; description?: string }[];
  };
  public justLoadedDataInfo: {
    title: string;
    entriesCount: number;
    description?: string;
  }[];
  public tierList: TierList[] = [];
  tierJson: any[] = [];
  selectedElement: string = 'Character Rarity';
  selectCharacter = "Character Rarity";
  selectWeapon = "Weapon Rarity";
  selectCodeBlock = "Code Block Rarity"

  public custom: Custom;
  public itemClassList: ItemClass[] = [];
  localStorageSizeInKB: number;
  public infoLineQuant = 5;
  public infoColQuant = 5;

  showWarning: boolean = false;
  orphanStoryboxes = [];

  public localStorageSpace: number;
  public _dataHealer: DataHealer;

  showRoadblocks: boolean = false;
  emptyRoadblocks: RoadBlock[] = [];
  rarityList: TierList[] = [];
  listSpokePlaces: SpokePlaceHelper[] = [];

  private _serviceSettingsManager: ServiceSettingsManager;
  public get serviceSettings(): ServiceSetting[] {
    return this._serviceSettingsManager.settings;
  }

  constructor(
    private bonusService: BonusService,
    public ailmentService: AilmentService,
    public conditionTriggerService: ConditionTriggerService,
    private durationService: DurationService,
    public reviewService: ReviewService,
    public rpgService: RpgService,
    public dcGuideService: DCGuideService,
    public specialWeaponServiceHC: SpecialWeaponServiceHC,
    public commonWeaponsService: CommonWeaponsService,
    private ailmentDefensesService: AilmentDefensesService,
    public userSettingsService: UserSettingsService,
    public areaService: AreaService,
    public levelService: LevelService,
    public dialogueService: DialogueService,
    public characterService: CharacterService,
    public itemsSelectorService: ItemsSelectorService,
    public storyExpansionPkgService: StoryExpansionPkgService,
    public particleVarianceService: ParticleVarianceService,
    public ingredientVarianceService: IngredientVarianceService,
    public optionService: OptionService,
    public speechService: SpeechService,
    public eventService: EventService,
    public markerService: MarkerService,
    public storyboxService: StoryBoxService,
    public optionboxService: OptionBoxService,
    public atributteService: AtributteService,
    public elementalDefensesService: ElementalDefensesService,
    public relicUsesService: RelicUsesService,
    public classService: ClassService,
    public emotionService: EmotionService,
    public itemService: ItemService,
    public missionService: MissionService,
    public objectiveService: ObjectiveService,
    public videoService: VideoService,
    public themeService: ThemeService,
    public tutorialService: TutorialService,
    public sceneryService: SceneryService,
    public keywordService: KeywordService,
    public soundService: SoundService,
    public conditionService: ConditionService,
    public qnaService: QnAService,
    private _router: Router,
    private _route: ActivatedRoute,
    private _roadBlockService: RoadBlockService,
    private _displayCheckService: DisplayCheckService,
    private minigameService: MinigameService,
    private itemClassService: ItemClassService,
    public tagService: TagService,
    public tabService: TabService,
    public customService: CustomService,
    public levelUpgradeService: LevelUpgradeService,
    public weaponService: WeaponService,
    public weaponUpgradeService: WeaponUpgradeService,
    public particleService: ParticleService,
    public powerupService: PowerUpService,
    public powerupStatService: PowerUpStatService,
    public memoryModuleService: MemoryModuleService,
    public particleDropService: ParticleDropService,
    public ingredientDropService: IngredientDropService,
    public effectService: EffectService,
    public microloopService: MicroloopService,
    public microloopContainerService: MicroloopContainerService,
    public codeBlockDropService: CodeBlockDropService,
    public dropService: DropService,
    public specialWeaponService: SpecialWeaponService,
    public laboratoryService: LaboratoryService,
    public titaniumMiningService: TitaniumMiningService,
    public titaniumStorageService: TitaniumStorageService,
    public adamantiumMiningService: AdamantiumMiningService,
    public adamantiumStorageService: AdamantiumStorageService,
    public hellniumMiningService: HellniumMiningService,
    public hellniumStorageService: HellniumStorageService,
    public blueprintArchiveService: BlueprintArchiveService,
    public collectibleRarityGoldService: CollectibleRarityGoldService,
    public collectibleRaritySoulsService: CollectibleRaritySoulsService,
    public collectibleRarityCodeBlocksService: CollectibleRarityCodeBlocksService,
    public profanariumService: ProfanariumService,
    public soulsGrinderService: SoulsGrinderService,
    public statusService: StatusService,
    public masteryService: MasteryService,
    public primalModifierService: PrimalModifierService,
    public battleUpgradeService: BattleUpgradeService,
    public statusInfoService: StatusInfoService,
    public statusEffectService: StatusEffectService,
    public tierService: TierService,
    public modifierService: ModifierService,
    public chestService: ChestService,
    public upgradesService: UpgradesService,
    public silicatosService: SilicatosService,
    public mapsService: MapsService,
    public keywordTagsService: KeywordsTagsService,
    public weaponsRarityService: WeaponRarityService,
    public ItemsSelectorService: ItemsSelectorService,
    public charactersSelectorService: CharactersSelectorService,
    public animationsSelectorService: AnimationsSelectorService,
    public _characterService: CharacterService,
    public _characterSelectorService: CharactersSelectorService,
    public languageService: LanguageService,
    public translationService: TranslationService,
    private _indexStorageService: IndexStorageService,
    private _spinnerService: SpinnerService,
    private http: HttpClient,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _minionStatsService: MinionStatsService,
    public passiveAllowedService: PassiveAllowedService,
    private passiveSkillService: PassiveSkillService,
    public uniqueCharactereService: UniqueCharactereService,
    public levelPointsService: LevelPointsService,
    public battleInferiorService: BattleInferiorService,
    public dilemmaBoxService: DilemmaBoxService,
    public dilemmaService: DilemmaService,
    public answerDilemmaBoxService: AnswerDilemmaBoxService,
    public configThresholdService: ConfigThresholdService,
    public categoryStatusEffectService: CategoryStatusEffectService,
    public repetitionStatusEffectService: RepetitionStatusEffectService,
    public _translationCheckService: TranslationCheckService,
    public boostIdBlockservice: BoostIdBlockservice,
    public healingIdBlockservice: HealingIdBlockservice,
    public defensiveIdBlockservice: DefensiveIdBlockservice,
    public negativeIdBlockservice: NegativeIdBlockservice,
    public hybridIdBlockservice: HybridIdBlockservice,
    public dispelIdBlockservice: DispelIdBlockservice,
    public boostTableService: BoostTableService,
    public healingTableService: HealingTableService,
    public defensiveTableService: DefensiveTableService,
    public negativeTableService: NegativeTableService,
    public ailmentTableService: AilmentTableService,
    public dispelTableService: DispelTableService,
    public hybridTableService: HybridTableService,
    public afflictionTableService: AfflictionTableService,
    public specialSkillsService: SpecialSkillsService,
    public mahankaraBehaviorTableService: MahankaraBehaviorTableService,
    public mahankaraCategoriesService: MahankaraCategoriesService,
    public mahankaraGroupingsService: MahankaraGroupingsService,
    public mahankaraConcatenationsService: MahankaraConcatenationsService,
    public categoriesXStressStatesService: CategoriesXStressStatesService,
    public subContextService: SubContextService,
    public attributeCheckService: AttributeCheckService,
    public attributediceFrustrationService: AttributeDiceFrustrationService,
    public uniqueCharactersByHCAndBLService: UniqueCharactersByHCAndBLService,
    public knowledgeService: KnowledgeService,
    public subContextKnowledgeService: SubContextKnowledgeService,
    public situationalModifierService: SituationalModifierService,
    public dCKnowledgeGuideService: DCKnowledgeGuideService,
    public knowledgeDiceFrustrationService: KnowledgeDiceFrustrationService,
    public knowledgeCheckService: KnowledgeCheckService,
    public archetypeListService: ArchetypeListService,
    public _battleInferiorService: BattleInferiorService,
    public totalArchetypesService: TotalArchetypesService,
    public castsGoldenService: CastsGoldenService,
    public castsSoulsService: CastsSoulsService,
    public castsSigilosService: CastsSigilosService,
    public amplifiersService: AmplifiersService,
    public levelHelperService: LevelHelperService,
    public locationPipe: LocationPipe,
    public cTREVMAXService: CTREVMAXService,
    public cTRCollectibleService: CTRCollectibleService,
    public lUKCollectibleService: LUKCollectibleService,
    public iNTCollectibleService: INTCollectibleService,
    public sPDCollectibleService: SPDCollectibleService,
    public aiPromptService: AIPromptService,
    public openAIEnvironmentService: OpenAIEnvironmentService,
    public openAIKeyGeneralService: OpenAIKeyGeneralService,

  ) {
    let lang = localStorage.getItem('currentLanguageDSAdmin');
    if (!lang) lang = 'PTBR';

    this.setLanguage(lang);

    this._serviceSettingsManager = new ServiceSettingsManager(this);
    this.calculateLocalStorage();
    this._dataHealer = new DataHealer(this);
  }


  async ngOnInit() {
    this.levelHelperService.toFinishLoading();
    this.custom = await this.customService.svcGetInstance();
    this.itemClassList = this.itemClassService.models;
    this.SetDataInfo();
    this.minutes = parseInt(
      (new Date().getTime() - this.lastLoadedDataInfo.date.getTime()) / 60000 + '', 10);

    setInterval(() => {
      this.minutes = parseInt(
        (new Date().getTime() - this.lastLoadedDataInfo.date.getTime()) /
        60000 + '', 10);
    }, 60000);
 
    this.filterOrphanStoryBoxes();
    this.loadPlaces();
  }

  async loadPlaces() {

    this.listSpokePlaces = [];
    
    setTimeout(() => {
       this.dilemmaBoxService.models.forEach((x) => {
        const location = this.checkLocation([x.id]);
        if (x?.label) {
           this.listSpokePlaces.push(
            {
              elementId: x.id,
              component: '[Dilemma Box]',           
              label: location.length > 0 ? x.label : 'Orphan',
              text: '[Dilemma Box] ' + (location.length > 0 ? x.label : 'Orphan')
            }
           );
        }
      });
      
       this.answerDilemmaBoxService.models.forEach((x) => {
        const location = this.checkLocation([x.id]);
        if (x?.label) {
          this.listSpokePlaces.push(
            {
              elementId: x.id,
              component: '[DilemmaBox > Answer]',           
              label: location.length > 0 ? x.label : 'Orphan',
              text: '[DilemmaBox > Answer] ' + (location.length > 0 ? x.label : 'Orphan')
            }
          );
        }
      });
  
      this._storyBoxService.models.forEach((x) => {
       // const location = this.checkLocation([x.id]);
        if (x?.label) {
          this.listSpokePlaces.push(
            {
              elementId: x.id,
              component: '[StoryBox]',
              label: x.label,  
              text: '[StoryBox] ' + x.label
              /*
              label: location.length > 0 ? x.label : 'Orphan',  
              text: '[StoryBox] ' + (location.length > 0 ? x.label : 'Orphan')   
              */
            }
          );
        }
      });
  
     this.optionboxService.models.forEach((x) => {
        const location = this.checkLocation([x.id]);
        if (x?.label) {
          this.listSpokePlaces.push(
          {
            elementId: x.id,
            component: (x.type == 0 ? '[ChoiceBox]' : '[InvestigationBox]'),
            label: location.length > 0 ? x.label : 'Orphan',
            text: (x.type == 0 ? '[ChoiceBox] ' : '[InvestigationBox] ') + (location.length > 0 ? x.label : 'Orphan')
          }
          );
        }
      });
  
     this._optionService.models.forEach((x) => {
        let type = 0;
        const location = this.checkLocation([x.id]);
        for (let i = 0; i < this.optionboxService.models.length; i++) {
          if (this.optionboxService.models[i].optionIds.includes(x.id)) {
            if (x.type === undefined) {
              x.type = this.optionboxService.models[i].type;
            }
            type = this.optionboxService.models[i].type;
            break;
          }
        }
  
        if (x?.label) {
          this.listSpokePlaces.push(
            {
              elementId: x.answerBoxId,
              component: x.type == 0 ? '[ChoiceBox > Answer]' : '[InvestigationBox > Answer]',
              label: location.length > 0 ? x.label : 'Orphan',    
              text: type == 0 ? "[ChoiceBox > Answer] " + (location.length > 0 ? x.label : 'Orphan') : "[InvestigationBox > Answer] " + (location.length > 0 ? x.label : 'Orphan')
            }
          );     
        }
  
        if (x.type == 0) {
          if (x?.label) {
            this.listSpokePlaces.push(
            {
              elementId: x.answerBoxId,
              component: '[ChoiceBox > Answer]',
              label:  location.length > 0 ? x.label : 'Orphan', 
              text: "[ChoiceBox > Answer] " + (location.length > 0 ? x.label : 'Orphan')    
            });         
          }
          if (x?.labelAnswerPositive) {
            this.listSpokePlaces.push({
              elementId: x.answerBoxId,
              component: '[ChoiceBox > Answer]',
              label: location.length > 0 ? x.labelAnswerPositive : 'Orphan',
              text: '[ChoiceBox > Answer] ' + (location.length > 0 ? x.labelAnswerPositive : 'Orphan')
            });       
          }
  
          if (x?.labelAnswerNegative) {
            this.listSpokePlaces.push(
              {
                elementId: x.answerBoxNegativeId,
                component: '[ChoiceBox > Answer]',
                label: location.length > 0 ? x.labelAnswerNegative : 'Orphan',
                text: '[ChoiceBox > Answer] ' + (location.length > 0 ? x.labelAnswerNegative : 'Orphan')
              }
            );
          }
        } 
        else {
          if (x?.label) {
            this.listSpokePlaces.push(
            {
              elementId: x.answerBoxId,
              component: '[InvestigationBox > Answer]',
              label:  location.length > 0 ? x.label : 'Orphan',
              text: "[InvestigationBox > Answer] " + (location.length > 0 ? x.label : 'Orphan')
            }
          );         
          }
  
          if (x?.labelAnswerPositive) {
            this.listSpokePlaces.push(
              {
              elementId: x.answerBoxId,
              component: '[InvestigationBox > Answer]',
              label: location.length > 0 ? x.labelAnswerPositive : 'Orphan', 
              text: "[InvestigationBox > Answer] " + (location.length > 0 ? x.labelAnswerPositive : 'Orphan') 
            }
          );     
          }
  
          if (x?.labelAnswerNegative) {
            this.listSpokePlaces.push(
               {
              elementId: x.answerBoxNegativeId,
              component: '[InvestigationBox > Answer]',
              label: location.length > 0 ? x.labelAnswerNegative : 'Orphan', 
              text: "[InvestigationBox > Answer] " + (location.length > 0 ? x.labelAnswerNegative : 'Orphan')
            }
            );      
          }
        }
      });
  
      this.listSpokePlaces.forEach(spokePlace => {
   
        const existingLevelHelper = this.levelHelperService.models.find((model) => model.originalLabel === spokePlace.label);
       // this.levelHelperService.createNewLevelHelper(spokePlace);
     
        if (!existingLevelHelper || existingLevelHelper.text !== spokePlace.text) {
           this.levelHelperService.createNewLevelHelper(spokePlace);
          }            
     });
     this.checkElementIds();      
    }, 500);
   
  }

  checkLocation(location: string[]) {
    const existLocation = this.locationPipe.transform(location);
    return existLocation;
  }

  // Verifica se os ids da base de dados do levelHelperService existem nos métodos dos componentes, senão remove eles evitando orfãos.
checkElementIds() {
  const levelHelperServiceIds = this.levelHelperService.models.map(model => model.elementId);
  const unusedIds = levelHelperServiceIds.filter(id => {
    return !(
      this.dilemmaBoxService.models.some(model => model.id === id) ||
      this.answerDilemmaBoxService.models.some(model => model.id === id) ||
      this._storyBoxService.models.some(model => model.id === id) ||
      this.optionboxService.models.some(model => model.id === id) ||
      this._optionService.models.some(model => model.id === id || model.answerBoxId === id || model.answerBoxNegativeId === id)
    );
  });

  this.levelHelperService.models = this.levelHelperService.models.filter(model => !unusedIds.includes(model.elementId));
  this.levelHelperService.toSave();
 // this.loadPlaces();
}

  calculateLocalStorage() {
    this.checksExistNameRarity();
    this.localStorageSizeInKB = 0;
    for (const _x in localStorage) {
      if (!localStorage.hasOwnProperty(_x)) continue;

      const _xLen = (localStorage[_x].length + _x.length) * 2;
      this.localStorageSizeInKB += _xLen;
    }
  }

  isCharactersSelectorDifferentToCharactes() {
    if (
      this._characterSelectorService.models.length !=
      this._characterService.models.length
    ) {
      Alert.showError(
        'The amount of characters and the amount of separation characters are different!'
      );
    }
  }

  private SetDataInfo(reset?: boolean) {
    this.userSettingsService.lastLoadedDataInfo = reset
      ? {
        date: new Date(),
        info: this.GetDataInfoArray(),
      }
      : this.userSettingsService.lastLoadedDataInfo || {
        date: new Date(),
        info: this.GetDataInfoArray(),
      };
    this.lastLoadedDataInfo = this.userSettingsService.lastLoadedDataInfo;
    this.justLoadedDataInfo = this.GetDataInfoArray();

    //Método usado nos casos em que o id do option consta no spokeElementId do roadblock
    this._roadBlockService.models.forEach((roadblock) => {
      const matchingOption = this._optionService.models.find(
        (option) => option.id === roadblock?.spokeElementId
      );
      if (matchingOption) {
        roadblock.spokeElementId = matchingOption.answerBoxId;
        this._roadBlockService.svcToModify(roadblock);
      }
    });

    //Atualiza Places do Roadblock
      this.loadPlaces();   
  }

  private GetDataInfoArray(): PackInfo[] {
    const infoArr: PackInfo[] = [];
    infoArr[0] = {
      title: 'Inner-User Settings',
      entriesCount: this.userSettingsService.data.icons.length,
      description: `Contain icons and other object information (author notes and which levels have the dialogue
        enabled that doesn't necessarily have speakers)`,
    };

    this.serviceSettings.forEach((setting, i) => {
      infoArr[i + 1] = {
        title: `${setting.service.typeName} Package`,
        entriesCount: setting.service.models.length,
      };
    });
    return infoArr;
  }

  public async toPromptImportDSA(files) {
    const file = files.target.files[0];

    if (file) {
      const fileReader = new FileReader();
      let alert = Alert.showLoading(2100, 'Importing database');
      fileReader.onload = async (error) => {
        await this.ConvertAndLoadDSA(fileReader.result);
      };
      fileReader.readAsText(file);
    }
    this.isCharactersSelectorDifferentToCharactes();
    this.syncDependentPackages();
  }

  //For more info look the method name in the documentation
  async syncDependentPackages() {
    if (this._minionStatsService.models.length == 0) return;

    for (let i = 0; i < this._characterService.models.length; i++)
      for (let j = 0; j < this._minionStatsService.models.length; j++) {
        if (
          this._characterService.models[i].name ==
          this._minionStatsService.models[j].name
        ) {
          this._minionStatsService.models[j].area =
            this._characterService.models[i].areaId;
          this._minionStatsService.models[j].klass =
            this._characterService.models[i].classId;
          this._minionStatsService.models[j].hc = this.areaService.svcFindById(
            this._characterService.models[i].areaId
          )?.order;
          this._minionStatsService.svcToModify(
            this._minionStatsService.models[i]
          );
          this._minionStatsService.toSave();
        }
        //Create a new minion stats if it does not exist.
        else if (
          j == this._minionStatsService.models.length - 1 &&
          this._characterService.models[i].rarity &&
          this._characterService.models[i].type == 2
        ) {
          this._minionStatsService.svcPromptCreateNew(
            this._characterService.models[i].name,
            this._characterService.models[i].areaId,
            this.areaService.svcFindById(
              this._characterService.models[i].areaId
            )?.order,
            this._characterService.models[i].classId
          );
        }
      }
      this.removeDuplicateIds();
      this.getIdRraityCharacters(); 
  }

  public async removeDuplicateIds() {
    const characters = this._battleInferiorService.models.map(model => model.character);
    const uniqueCharacters = [...new Set(characters)];
    const duplicateCharacters = characters.filter((character, index) => characters.indexOf(character) !== index);

    duplicateCharacters.forEach(character => {
      const id = this._battleInferiorService.models.find(model => model.character === character).id;
      this._battleInferiorService.svcToRemove(id);
    });
}

  //Atribui o ID do Rarity ao Character correspondente
  //Corrigi id rarity se tiver errado no Character
  async getIdRraityCharacters() { 
    this.rarityList = this.tierService.getCollectibleRarity('Character Rarity'); 
     this.rarityList = this.rarityList.filter((rar) => rar.name !== "" && rar.name !== undefined)
  
    this._characterService.models.forEach((x) => {
       const rarity = this.rarityList.find((r) => r.name === x.rarity);
       if (rarity) {
         x.rarityId = rarity.id;
         this._characterService.svcToModify(x);
       }
     });    
   }

   
  public toPromptImportDSO(files) {
    const file = files[0];
    if (file) {
      const fileReader = new FileReader();
      fileReader.onload = async (error) => {
        this.LoadDSO(fileReader.result);
      };
      fileReader.readAsText(file);
    }
  }

  public async LoadDSO(json) {
    let dsa: Filing.DSA9;
    try {
      const file = JSON.parse(json);
      const dso9 = Filing.DSO9.New(file);
      if (file.fileFormat !== 'dso8') {
        throw Error('File format not supported');
      } else {
        if (!this.loadedTempDSA.isCompatible(dso9)) {
          throw Error(
            '<h6>Project version</h6>' +
            this.loadedTempDSA.getCompatibleVersion() +
            '<h6>=/=</h6>' +
            '<h6>Orthography file version</h6>' +
            dso9.getCompatibleVersion()
          );
        } else {
          const changed = this.loadedTempDSA.importOrthography(dso9);
          if (!changed) return;

          dsa = this.loadedTempDSA;
          if (dsa) {
            const view = await Alert.showConfirm(
              'View changed text log?',
              undefined,
              'Yes, view it.'
            );
            if (view) {
              Alert.showAlert('Text changed: ', changed, 'info');
            }
          }
        }
      }
    } catch (error) {
      dsa = undefined;
      Alert.showError(error);
    }

    try {
      await this.LoadData(dsa);
    } catch (error) {
      Alert.showError(error);
      throw error;
    }
  }

  private async ConvertAndLoadDSA(json): Promise<void> {
    let dsa: Filing.DSA9;
    try {
      this._translationCheckService.checkedAll = false;
      const file: unknown = JSON.parse(json);
      if (Filing.isCompatible(file)) {
        let converter: Filing.Converter<
          Versioning.SerializableFileable,
          Filing.DSA9
        >;
        if (Filing.isVersion9(file)) {
          converter = new Filing.DSA9JSONToDSA9(file);
          dsa = converter.convert();
        } else {
          converter = new Filing.DSA8JSONToDSA9(file);
          dsa = converter.convert();
        }
        await this.LoadData(dsa);
      } else {
        Alert.showError('File format not compatible');
      }
    } catch (error) {
      dsa = undefined;
      Alert.showError(error);
      throw error;
    }
  }


  public async toPromptExportData(v9Format: 'dsa' | 'dso') {
    await Alert.showLoading(200, 'Processing');
    const dateTime = new Date();
    const dsa9 = new Filing.DSA9({
      exportedDateTime:
        dateTime.toLocaleDateString() + '_' + dateTime.toLocaleTimeString(),
      bonusService: this.bonusService,
      userSettingsService: this.userSettingsService,
      dcGuideService: this.dcGuideService,
      ailmentService: this.ailmentService,
      ailmentDefensesService: this.ailmentDefensesService,
      commonWeaponsService: this.commonWeaponsService,
      conditionTriggerService: this.conditionTriggerService,
      durationService: this.durationService,
      specialWeaponServiceHC: this.specialWeaponServiceHC,
      uniqueCharactereService: this.uniqueCharactereService,
      levelPointsService: this.levelPointsService,
      relicUsesService: this.relicUsesService,
      areaService: this.areaService,
      levelService: this.levelService,
      dialogueService: this.dialogueService,
      storyboxService: this.storyboxService,
      optionboxService: this.optionboxService,
      optionService: this.optionService,
      speechService: this.speechService,
      eventService: this.eventService,
      markerService: this.markerService,
      characterService: this.characterService,
      itemsSelectorService: this.itemsSelectorService,
      storyExpansionPkgService: this.storyExpansionPkgService,
      particleVarianceService: this.particleVarianceService,
      ingredientVarianceService: this.ingredientVarianceService,
      classService: this.classService,
      emotionService: this.emotionService,
      itemService: this.itemService,
      missionService: this.missionService,
      objectiveService: this.objectiveService,
      videoService: this.videoService,
      themeService: this.themeService,
      tutorialService: this.tutorialService,
      sceneryService: this.sceneryService,
      keywordService: this.keywordService,
      soundService: this.soundService,
      conditionService: this.conditionService,
      qnaService: this.qnaService,
      roadBlockService: this._roadBlockService,
      minigameService: this.minigameService,
      itemClassService: this.itemClassService,
      tagService: this.tagService,
      tabService: this.tabService,
      microloopService: this.microloopService,
      microloopContainerService: this.microloopContainerService,
      translationService: this.translationService,
      languageService: this.languageService,
      customService: this.customService,
      levelUpgradeService: this.levelUpgradeService,
      weaponService: this.weaponService,
      weaponUpgradeService: this.weaponUpgradeService,
      particleService: this.particleService,
      powerupService: this.powerupService,
      powerupStatService: this.powerupStatService,
      memoryModuleService: this.memoryModuleService,
      particleDropService: this.particleDropService,
      ingredientDropService: this.ingredientDropService,
      effectService: this.effectService,
      codeBlockDropService: this.codeBlockDropService,
      dropService: this.dropService,
      specialWeaponService: this.specialWeaponService,
      laboratoryService: this.laboratoryService,
      titaniumMiningService: this.titaniumMiningService,
      titaniumStorageService: this.titaniumStorageService,
      adamantiumMiningService: this.adamantiumMiningService,
      adamantiumStorageService: this.adamantiumStorageService,
      hellniumMiningService: this.hellniumMiningService,
      hellniumStorageService: this.hellniumStorageService,
      collectibleRarityGoldService: this.collectibleRarityGoldService,
      collectibleRaritySoulsService: this.collectibleRaritySoulsService,
      collectibleRarityCodeBlocksService: this.collectibleRarityCodeBlocksService,
      blueprintArchiveService: this.blueprintArchiveService,
      profanariumService: this.profanariumService,
      soulsGrinderService: this.soulsGrinderService,
      statusService: this.statusService,
      masteryService: this.masteryService,
      primalModifierService: this.primalModifierService,
      battleUpgradeService: this.battleUpgradeService,
      statusInfoService: this.statusInfoService,
      statusEffectService: this.statusEffectService,
      tierService: this.tierService,
      atributte: this.atributteService,
      elementalDefenses: this.elementalDefensesService,
      modifierService: this.modifierService,
      chestService: this.chestService,
      minionStatsService: this._minionStatsService,
      upgradesService: this.upgradesService,
      silicatosService: this.silicatosService,
      mapsService: this.mapsService,
      keywordTagsService: this.keywordTagsService,
      weaponsRarityService: this.weaponsRarityService,
      charactersSelectorService: this.charactersSelectorService,
      animationsSelectorService: this.animationsSelectorService,
      passiveAllowedService: this.passiveAllowedService,
      passiveSkillService: this.passiveSkillService,
      battleInferiorService: this.battleInferiorService,
      dilemmaBoxService: this.dilemmaBoxService,
      dilemmaService: this.dilemmaService,
      answerDilemmaBoxService: this.answerDilemmaBoxService,
      configThresholdService: this.configThresholdService,
      categoryStatusEffectService: this.categoryStatusEffectService,
      repetitionStatusEffectService: this.repetitionStatusEffectService,
      boostIdBlockservice: this.boostIdBlockservice,
      healingIdBlockservice: this.healingIdBlockservice,
      defensiveIdBlockservice: this.defensiveIdBlockservice,
      negativeIdBlockservice: this.negativeIdBlockservice,
      hybridIdBlockservice: this.hybridIdBlockservice,
      dispelIdBlockservice: this.dispelIdBlockservice,
      boostTableService: this.boostTableService,
      healingTableService: this.healingTableService,
      defensiveTableService: this.defensiveTableService,
      negativeTableService: this.negativeTableService,
      ailmentTableService: this.ailmentTableService,
      dispelTableService: this.dispelTableService,
      hybridTableService: this.hybridTableService,
      afflictionTableService: this.afflictionTableService,
      specialSkillsService: this.specialSkillsService,
      mahankaraBehaviorTableService: this.mahankaraBehaviorTableService,
      mahankaraCategoriesService: this.mahankaraCategoriesService,
      mahankaraGroupingsService: this.mahankaraGroupingsService,
      mahankaraConcatenationsService: this.mahankaraConcatenationsService,
      categoriesXStressStatesService: this.categoriesXStressStatesService,
      subContextService: this.subContextService,
      attributeCheckService: this.attributeCheckService,
      attributediceFrustrationService: this.attributediceFrustrationService,
      uniqueCharactersByHCAndBLService: this.uniqueCharactersByHCAndBLService,
      knowledgeService: this.knowledgeService,
      subContextKnowledgeService: this.subContextKnowledgeService,
      situationalModifierService: this.situationalModifierService,
      dCKnowledgeGuideService: this.dCKnowledgeGuideService,
      knowledgeDiceFrustrationService: this.knowledgeDiceFrustrationService,
      knowledgeCheckService: this.knowledgeCheckService,
      archetypeListService: this.archetypeListService,
      totalArchetypesService: this.totalArchetypesService,
      castsGoldenService: this.castsGoldenService,
      castsSoulsService: this.castsSoulsService,
      castsSigilosService: this.castsSigilosService,
      amplifiersService: this.amplifiersService,
      levelHelperService: this.levelHelperService,
      cTREVMAXService: this.cTREVMAXService,
      cTRCollectibleService: this.cTRCollectibleService,
      lUKCollectibleService: this.lUKCollectibleService,
      iNTCollectibleService: this.iNTCollectibleService,
      sPDCollectibleService: this.sPDCollectibleService,  
      aiPromptService: this.aiPromptService,
      openAIEnvironmentService: this.openAIEnvironmentService,
      openAIKeyGeneralService: this.openAIKeyGeneralService,


    });

    switch (v9Format) {
      case 'dsa':
        this.downloadData(dsa9);
        break;
      case 'dso':
        this.downloadData(dsa9);
        this.downloadData(dsa9.exportOrthography());
    }
  }

  public async toPromptClearData() {
    const conf = await Alert.WARNING(
      'Are you sure?',
      'This will clear ALL current data from this tool\n Consider exporting a .JSON as backup before proceeding.',
      'Yes, clear everything',
      'warning'
    );
    if (conf) {
      await this.clearData();
    }
  }

  private async LoadData(dsa9: Filing.DSA9) {
    if (!dsa9) return;
    this.jsondsa9 = dsa9;
    this.deleteRemoveddataArray(dsa9);
    await this.clearData();

    //let alert = Alert.showLoading(2000, 'Importing database');
    await this.userSettingsService.toImportSettings(dsa9.userSettings);
    await Promise.all(
      this.serviceSettings.map(
        async (setting) =>
          await setting.service.svcImportPack(dsa9[setting.pkgName] as any)
      )
    );

    this.loadedTempDSA = dsa9;

    if (dsa9) {
      this.reviewService.checkImportedFile(true);
    } else {
      this.reviewService.checkImportedFile(false);
    }

    this.iconListComponent.renderList();

    for (const setting of this.serviceSettings) {
      setting.service.svcReviewAll();
    }

    await this.speechService.svcImportPack(dsa9.speechPkg);
    this.ailmentService.svcImportPack(dsa9.ailmentPkg);
    this.ailmentDefensesService.svcImportPack(dsa9.ailmentDefensesPkg);
    this.atributteService.svcImportPack(dsa9.atributtePkg);
    this.dcGuideService.svcImportPack(dsa9.dcGuidePkg);
    this.bonusService.svcImportPack(dsa9.bonusPkg);
    this.battleInferiorService.svcImportPack(dsa9.battleInferiorPkg);
    this.commonWeaponsService.svcImportPack(dsa9.commonWeaponsPkg);
    this.conditionTriggerService.svcImportPack(dsa9.conditionTriggerPkg);
    this.durationService.svcImportPack(dsa9.durationPkg);
    this.elementalDefensesService.svcImportPack(dsa9.elementalDefensesPkg);
    this._roadBlockService.svcImportPack(dsa9.roadBlockPkg);
    this.itemClassService.svcImportPack(dsa9.itemClassPkg);
    this.tagService.svcImportPack(dsa9.tagPkg);
    this.tabService.svcImportPack(dsa9.tabPkg);
    this.microloopService.svcImportPack(dsa9.microloopPkg);
    this.microloopContainerService.svcImportPack(dsa9.microloopContainerPkg);
    this.translationService.svcImportPack(dsa9.translationPkg);
    this.languageService.svcImportPack(dsa9.languagePkg);
    this.levelUpgradeService.svcImportPack(dsa9.levelUpgradePkg);
    this.customService.svcImportPack(dsa9.customPkg);
    this.weaponService.svcImportPack(dsa9.weaponPkg);
    this.weaponUpgradeService.svcImportPack(dsa9.weaponUpgradePkg);
    this.particleService.svcImportPack(dsa9.particlePkg);
    this.powerupService.svcImportPack(dsa9.powerUpPkg);
    this.powerupStatService.svcImportPack(dsa9.powerUpStatPkg);
    this.memoryModuleService.svcImportPack(dsa9.memoryModulePkg);
    this.particleDropService.svcImportPack(dsa9.particleDropPkg);
    this.ingredientDropService.svcImportPack(dsa9.ingredientDropPkg);
    this.keywordService.svcImportPack(dsa9.keywordPkg);
    this.tutorialService.svcImportPack(dsa9.tutorialPkg);
    this.minigameService.svcImportPack(dsa9.minigamePkg);
    this.videoService.svcImportPack(dsa9.videoPkg);
    this.effectService.svcImportPack(dsa9.effectPkg);
    this.codeBlockDropService.svcImportPack(dsa9.codeBlockDropPkg);
    this.dropService.svcImportPack(dsa9.dropPkg);
    this.specialWeaponService.svcImportPack(dsa9.specialWeaponPkg);
    this.laboratoryService.svcImportPack(dsa9.laboratoryPkg);
    this.titaniumMiningService.svcImportPack(dsa9.titaniumMiningPkg);
    this.titaniumStorageService.svcImportPack(dsa9.titaniumStoragePkg);
    this.adamantiumMiningService.svcImportPack(dsa9.adamantiumMiningPkg);
    this.adamantiumStorageService.svcImportPack(dsa9.adamantiumStoragePkg);
    this.hellniumMiningService.svcImportPack(dsa9.hellniumMiningPkg);
    this.hellniumStorageService.svcImportPack(dsa9.hellniumStoragePkg);
    this.blueprintArchiveService.svcImportPack(dsa9.blueprintArchivePkg);
    this.collectibleRarityCodeBlocksService.svcImportPack(dsa9.collectibleRarityCodeBlocksPkg);
    this.collectibleRarityGoldService.svcImportPack(dsa9.collectibleRarityGoldPkg);
    this.collectibleRaritySoulsService.svcImportPack(dsa9.collectibleRaritySoulsPkg);
    this.profanariumService.svcImportPack(dsa9.profanariumPkg);
    this.soulsGrinderService.svcImportPack(dsa9.soulsGrinderPkg);
    this.statusService.svcImportPack(dsa9.statusPkg);
    this.masteryService.svcImportPack(dsa9.masteryPkg);
    this.primalModifierService.svcImportPack(dsa9.primalModifierPkg);
    this.battleUpgradeService.svcImportPack(dsa9.battleUpgradePkg);
    this.statusInfoService.svcImportPack(dsa9.statusInfoPkg);
    this.statusEffectService.svcImportPack(dsa9.statusEffectPkg);
    this.tierService.svcImportPack(dsa9.tierListPkg);
    this.modifierService.svcImportPack(dsa9.modifierListPkg);
    this.chestService.svcImportPack(dsa9.chestListPkg);
    this._minionStatsService.svcImportPack(dsa9.minionStatsPkg);
    this.upgradesService.svcImportPack(dsa9.upgradesPkg);
    this.silicatosService.svcImportPack(dsa9.silicatosPkg);
    this.mapsService.svcImportPack(dsa9.mapsPkg);
    this.keywordTagsService.svcImportPack(dsa9.keywordsTagsPkg);
    this.weaponsRarityService.svcImportPack(dsa9.weaponsRarityPkg);
    this.passiveAllowedService.svcImportPack(dsa9.passiveAllowedPkg);
    this.passiveSkillService.svcImportPack(dsa9.passiveSkillPkg);
    this.relicUsesService.svcImportPack(dsa9.relicUsesPkg);
    this.dilemmaBoxService.svcImportPack(dsa9.dilemmaBoxPkg);
    this.dilemmaService.svcImportPack(dsa9.dilemmaPkg);
    this.answerDilemmaBoxService.svcImportPack(dsa9.answerDilemmaBoxPkg);
    this.configThresholdService.svcImportPack(dsa9.configThresholdPkg);
    this.categoryStatusEffectService.svcImportPack(dsa9.categoryStatusEffectPkg);
    this.repetitionStatusEffectService.svcImportPack(dsa9.repetitionStatusEffectPkg);
    this.boostIdBlockservice.svcImportPack(dsa9.boostIdBlockPkg);
    this.healingIdBlockservice.svcImportPack(dsa9.healingIdBlockPkg);
    this.defensiveIdBlockservice.svcImportPack(dsa9.defensiveIdBlockPkg);
    this.negativeIdBlockservice.svcImportPack(dsa9.negativeIdBlockPkg);
    this.hybridIdBlockservice.svcImportPack(dsa9.hybridIdBlockPkg);
    this.dispelIdBlockservice.svcImportPack(dsa9.dispelIdBlockPkg);
    this.boostTableService.svcImportPack(dsa9.boostTablePkg);
    this.healingTableService.svcImportPack(dsa9.healingTablePkg);
    this.defensiveTableService.svcImportPack(dsa9.defensiveTablePkg);
    this.negativeTableService.svcImportPack(dsa9.negativeTablePkg);
    this.ailmentTableService.svcImportPack(dsa9.ailmentTablePkg);
    this.dispelTableService.svcImportPack(dsa9.dispelTablePkg);
    this.hybridTableService.svcImportPack(dsa9.hybridTablePkg);
    this.afflictionTableService.svcImportPack(dsa9.afflictionTablePkg);
    this.specialSkillsService.svcImportPack(dsa9.specialSkillsPkg);
    this.mahankaraBehaviorTableService.svcImportPack(dsa9.mahankaraBehaviorTablePkg);
    this.mahankaraCategoriesService.svcImportPack(dsa9.mahankaraCategoriesPkg);
    this.mahankaraGroupingsService.svcImportPack(dsa9.mahankaraGroupingsPkg);
    this.mahankaraConcatenationsService.svcImportPack(dsa9.mahankaraConcatenationsPkg);
    this.categoriesXStressStatesService.svcImportPack(dsa9.categoriesXStressStatesPkg);
    this.subContextService.svcImportPack(dsa9.subContextPkg);
    this.attributeCheckService.svcImportPack(dsa9.attributeCheckPkg);
    this.attributediceFrustrationService.svcImportPack(dsa9.attributediceFrustrationPkg);
    this.uniqueCharactersByHCAndBLService.svcImportPack(dsa9.uniqueCharactersByHCAndBLPkg);
    this.knowledgeService.svcImportPack(dsa9.knowledgePkg);
    this.subContextKnowledgeService.svcImportPack(dsa9.subContextKnowledgePkg);
    this.situationalModifierService.svcImportPack(dsa9.situationalModifierPkg);
    this.dCKnowledgeGuideService.svcImportPack(dsa9.dCKnowledgeGuidePkg);
    this.knowledgeDiceFrustrationService.svcImportPack(dsa9.knowledgeDiceFrustrationPkg);
    this.knowledgeCheckService.svcImportPack(dsa9.knowledgeCheckPkg);
    this.archetypeListService.svcImportPack(dsa9.archetypeListPkg);
    this.totalArchetypesService.svcImportPack(dsa9.totalArchetypesPkg);
    this.castsGoldenService.svcImportPack(dsa9.castsGoldenPkg);
    this.castsSoulsService.svcImportPack(dsa9.castsSoulsPkg);
    this.castsSigilosService.svcImportPack(dsa9.castsSigilosPkg);
    this.amplifiersService.svcImportPack(dsa9.amplifiersPkg);
    this.levelHelperService.svcImportPack(dsa9.spokePlacePkg);
    this.cTREVMAXService.svcImportPack(dsa9.cTREVMAXPkg);
    this.cTRCollectibleService.svcImportPack(dsa9.cTRCollectiblePkg);
    this.lUKCollectibleService.svcImportPack(dsa9.lUKCollectiblePkg);
    this.iNTCollectibleService.svcImportPack(dsa9.iNTCollectiblePkg);
    this.sPDCollectibleService.svcImportPack(dsa9.sPDCollectiblePkg);
    this.aiPromptService.svcImportPack(dsa9.aiPromptPkg);
    this.openAIEnvironmentService.svcImportPack(dsa9.openAIEnvironmentPkg);
    this.openAIKeyGeneralService.svcImportPack(dsa9.openAIKeyGeneraltPkg);

    await this.itemsSelectorService.svcImportPack(dsa9.itemsSelectorPkg);
    await this.storyExpansionPkgService.svcImportPack(dsa9.storyExpansionPkg);
    await this.particleVarianceService.svcImportPack(dsa9.particleVariancePkg);
    await this.ingredientVarianceService.svcImportPack(dsa9.ingredientVariancePkg);
    await this.charactersSelectorService.svcImportPack(dsa9.charactersSelectorPkg);
    await this.animationsSelectorService.svcImportPack(dsa9.animationsSelectorPkg);
    await this.specialWeaponServiceHC.svcImportPack(dsa9.specialWeaponsHCPkg);
    this.uniqueCharactereService.svcImportPack(dsa9.uniqueCharacterePkg);
    this.levelPointsService.svcImportPack(dsa9.levelPointsPkg);

    await alert;
    this.reviewService.save();
    this.SetDataInfo(true);

    Alert.ShowSuccess(
      'Data imported',
      '<h6>Version</h6>' + dsa9.getCompatibleVersion(),
      1500,
      'top-end'
    );
    this.convertVarianceToSelectDrop(dsa9);
    this.calculateLocalStorage();
    this._translationCheckService.checkAll();
  }

  convertVarianceToSelectDrop(dsa9) {

    this.tierJson = dsa9['tierListPkg']['data'];
    this.tierService.models = this.tierService.models.filter(model => model.name?.trim() !== "");
    this.tierList = this.tierService.models.filter((x) => x.name != undefined);

    for (let index = 0; index < this.tierJson.length; index++) {
      this.tierList.filter((x) => {
        if (x.id === this.tierJson[index].id) {
          x.selectDrop = this.tierJson[index].variance;
          this.tierService.svcToModify(x);
        }
      });
    }

  }

  deleteRemoveddataArray(dsa9) {
    for (const key in dsa9) {
      if (dsa9[key]['removedData'] != undefined) {
        dsa9[key]['removedData'] = [];
      }
    }
  }

  isLevelInLevelPackage(id) {
    if (id.split('.L')[1]?.length == undefined || id.split('.D')[1]?.length > 0)
      return true; //If is not a level return.
    for (let i = 0; i < this.levelService.models.length; i++) {
      if (id == this.levelService.models[i].id) return true;
    }
    return false;
  }

  private async audioLoader() {
    await this.soundService.toSave();
    await this.speechService.toSave();

    this.soundService.models.forEach(async (sound) => {
      if (!sound.id.includes('.')) {
        await this.soundService.svcToRemove(sound.id);
        await this.soundService.toSave();
      }
    });
    this.soundService.models.forEach((sound) => {
      this._displayCheckService.soundDisplayed(sound.id);
    });
  }

  private downloadData(file: Filing.DSA9 | Filing.DSO9) {
    const anchorElement = document.createElement('a');
    const blob = new Blob([JSON.stringify(file, null, 2)], {
      type: 'text/plain',
    });
    anchorElement.href = window.URL.createObjectURL(blob);
    anchorElement.download = this.fileTitle + '.' + file.fileFormat;
    anchorElement.append('.dsa9');
    anchorElement.click();
    this.fileTitle = undefined; //title ds09
  }

  private async clearData() {
    this.reviewService.checkImportedFile(true);
    this.reviewService.reset();

    this.userSettingsService.toReset();
    await Promise.all(
      this.serviceSettings.map(async (setting) => {
        await setting.service.svcToReset();
      })
    );

    this.ailmentService.svcToReset();
    this.bonusService.svcToReset();
    this.battleInferiorService.svcToReset();
    this.dcGuideService.svcToReset();
    this.rpgService.reset();
    this.specialWeaponServiceHC.svcToReset();
    this.atributteService.svcToReset();
    this.ailmentDefensesService.svcToReset();
    this.commonWeaponsService.svcToReset();
    this.conditionTriggerService.svcToReset();
    this.durationService.svcToReset();
    this.elementalDefensesService.svcToReset();
    this._roadBlockService.svcToReset();
    this.minigameService.svcToReset();
    this.itemClassService.svcToReset();
    this.tagService.svcToReset();
    this.tabService.svcToReset();
    this.microloopService.svcToReset();
    this.microloopContainerService.svcToReset();
    this.translationService.svcToReset();
    this.languageService.svcToReset();
    this.levelUpgradeService.svcToReset();
    this.customService.svcToReset();
    this.weaponService.svcToReset();
    this.weaponUpgradeService.svcToReset();
    this.particleService.svcToReset();
    this.powerupService.svcToReset();
    this.powerupStatService.svcToReset();
    this.memoryModuleService.svcToReset();
    this.particleDropService.svcToReset();
    this.ingredientDropService.svcToReset();
    this.effectService.svcToReset();
    this.codeBlockDropService.svcToReset();
    this.dropService.svcToReset();
    this.specialWeaponService.svcToReset();
    this.laboratoryService.svcToReset();
    this.titaniumMiningService.svcToReset();
    this.titaniumStorageService.svcToReset();
    this.blueprintArchiveService.svcToReset();
    this.collectibleRarityCodeBlocksService.svcToReset();
    this.collectibleRarityGoldService.svcToReset();
    this.collectibleRaritySoulsService.svcToReset();
    this.profanariumService.svcToReset();
    this.soulsGrinderService.svcToReset();
    this.statusService.svcToReset();
    this.masteryService.svcToReset();
    this.primalModifierService.svcToReset();
    this.battleUpgradeService.svcToReset();
    this.statusInfoService.svcToReset();
    this.statusEffectService.svcToReset();
    this.tierService.svcToReset();
    this.modifierService.svcToReset();
    this.chestService.svcToReset();
    this._minionStatsService.svcToReset();
    this.upgradesService.svcToReset();
    this.silicatosService.svcToReset();
    this.mapsService.svcToReset();
    this.keywordTagsService.svcToReset();
    this.weaponsRarityService.svcToReset();
    this.itemsSelectorService.svcToReset();
    this.storyExpansionPkgService.svcToReset();
    this.particleVarianceService.svcToReset();
    this.ingredientVarianceService.svcToReset();
    this.charactersSelectorService.svcToReset();
    this.adamantiumMiningService.svcToReset();
    this.adamantiumStorageService.svcToReset();
    this.hellniumMiningService.svcToReset();
    this.hellniumStorageService.svcToReset();
    this.animationsSelectorService.svcToReset();
    this.passiveAllowedService.svcToReset();
    this.passiveSkillService.svcToReset();
    this.relicUsesService.svcToReset();
    this.uniqueCharactereService.svcToReset();
    this.levelPointsService.svcToReset();
    this.dilemmaBoxService.svcToReset();
    this.dilemmaService.svcToReset();
    this.answerDilemmaBoxService.svcToReset();
    this.configThresholdService.svcToReset();
    this.categoryStatusEffectService.svcToReset();
    this.repetitionStatusEffectService.svcToReset();
    this.boostIdBlockservice.svcToReset();
    this.healingIdBlockservice.svcToReset();
    this.defensiveIdBlockservice.svcToReset();
    this.negativeIdBlockservice.svcToReset();
    this.hybridIdBlockservice.svcToReset();
    this.dispelIdBlockservice.svcToReset();
    this.boostTableService.svcToReset();
    this.healingTableService.svcToReset();
    this.defensiveTableService.svcToReset();
    this.negativeTableService.svcToReset();
    this.ailmentTableService.svcToReset();
    this.dispelTableService.svcToReset();
    this.hybridTableService.svcToReset();
    this.afflictionTableService.svcToReset();
    this.specialSkillsService.svcToReset();
    this.mahankaraBehaviorTableService.svcToReset();
    this.mahankaraCategoriesService.svcToReset();
    this.mahankaraGroupingsService.svcToReset();
    this.mahankaraConcatenationsService.svcToReset();
    this.categoriesXStressStatesService.svcToReset();
    this.subContextService.svcToReset();
    this.attributeCheckService.svcToReset();
    this.attributediceFrustrationService.svcToReset();
    this.uniqueCharactersByHCAndBLService.svcToReset();
    this.knowledgeService.svcToReset();
    this.subContextKnowledgeService.svcToReset();
    this.situationalModifierService.svcToReset();
    this.dCKnowledgeGuideService.svcToReset();
    this.knowledgeDiceFrustrationService.svcToReset();
    this.knowledgeCheckService.svcToReset();
    this.archetypeListService.svcToReset();
    this.totalArchetypesService.svcToReset();
    this.castsGoldenService.svcToReset();
    this.castsSoulsService.svcToReset();
    this.castsSigilosService.svcToReset();
    this.amplifiersService.svcToReset();
    this.levelHelperService.svcToReset();
    this.cTREVMAXService.svcToReset();
    this.cTRCollectibleService.svcToReset();
    this.lUKCollectibleService.svcToReset();
    this.iNTCollectibleService.svcToReset();
    this.sPDCollectibleService.svcToReset();
    this.aiPromptService.svcToReset();
    this.openAIEnvironmentService.svcToReset();
    this.openAIKeyGeneralService.svcToReset();

    this.iconListComponent.renderList();
    this.SetDataInfo(true);

    this.loadedTempDSA = undefined;

    localStorage.clear();

    Alert.ShowSuccess('Data cleared', undefined, 1000, 'top-end');
    this.calculateLocalStorage();



    this.SetDataInfo(true);
    this._router
      .navigateByUrl('/', { skipLocationChange: true })
      .then(() => this._router.navigate([this._route.url]));
      
  }

  public async healData() {
    await this._dataHealer.toHeal();
  }

  importTranslationData(files) {
    this._spinnerService.setState(true, 60000);

    const file = files.target.files[0];
    if (file) {
      const fileReader = new FileReader(); //read a blob e/or File
      fileReader.onload = //onload is an event that is called when the reading process is successfull.
        async () => {
          this.ConvertAndLoadDST(fileReader.result);
        };
      fileReader.readAsText(file); //We get a string in the end
    }
  }

  async ConvertAndLoadDST(json) {
    const file = JSON.parse(json);
    let finished = false;
    let currentLanguage = IndexStorageService.activeLanguage;
    try {
      let size = file.translations.length;
      file.translations.forEach(async (element) => {
        size--;
        this.setLanguage(element.lang);

        let service = this.getServiceByTable(element.table);
        if (!service) return;
        let model = await service.svcFindById(element.id);
        if (model) {
          if (element.name) model.name = element.name;
          if (element.description) model.description = element.description;
          if (element.battleDescription)
            model.battleDescription = element.battleDescription;
          if (element.notes) model.notes = element.notes;
          if (element.message) model.message = element.message;
          if (element.skill) model.skill = element.skill;
          if (element.word) model.word = element.word;
          if (element.title) model.title = element.title;

          service.models[service.indexOfId(model.id)] = model;
          await service.toSave();
        }
        if (size <= 0 && !finished) {
          finished = true;
          this.setLanguage(currentLanguage);
          this._spinnerService.setState(false);
          window.location.reload();
        }
      });
    } catch {
      Alert.showError('Invalid File');
      throw Error('Inavlid file');
    }
    this._translationCheckService.checkAll();
  }

  getServiceByTable(table: string): ModelService {
    switch (table) {
      case 'AdamantiumMining':
        return this.adamantiumMiningService;
      case 'Ailment':
        return this.ailmentService;
      case 'AilmentDefenses':
        return this.ailmentDefensesService;
      case 'AdamantiumStorage':
        return this.adamantiumStorageService;
      case 'Area':
        return this.areaService;
      case 'BattleUpgrade':
        return this.battleUpgradeService;
      case 'BlueprintArchive':
        return this.blueprintArchiveService;
      case 'bonus':
        return this.bonusService;
      case 'conditionTigger':
        return this.conditionTriggerService;
      case 'duration':
        return this.durationService;
      case 'Character':
        return this.characterService;
      case 'ParticleVariance':
        return this.particleVarianceService;
      case 'IngredientVariance':
        return this.ingredientVarianceService;
      case 'StoryExpansionPkg':
        return this.storyExpansionPkgService;
      case 'Class':
        return this.classService;
      case 'CodeBlockDrop':
        return this.codeBlockDropService;
      case 'CollectibleRarityCodeBlocks':
        return this.collectibleRarityCodeBlocksService;
      case 'CollectibleRarityGold':
        return this.collectibleRarityGoldService;
      case 'CollectibleRaritySouls':
        return this.collectibleRaritySoulsService;
      case 'Condition':
        return this.conditionService;
      case 'Custom':
        return this.customService;
      case 'Dialogue':
        return this.dialogueService;
      case 'Drop':
        return this.dropService;
      case 'Effect':
        return this.effectService;
      case 'ElementalDefenses':
        return this.elementalDefensesService;
      case 'Emotion':
        return this.emotionService;
      case 'Event':
        return this.eventService;
      case 'HellniumMining':
        return this.hellniumMiningService;
      case 'HellniumStorage':
        return this.hellniumStorageService;
      case 'Item':
        return this.itemService;
      case 'ItemClass':
        return this.itemClassService;
      case 'Keyword':
        return this.keywordService;
      case 'Keyword':
        return this.keywordTagsService;
      case 'Laboratory':
        return this.laboratoryService;
      case 'Language':
        return this.languageService;
      case 'Level':
        return this.levelService;
      case 'LevelUpgrade':
        return this.levelUpgradeService;
      case 'Maps':
        return this.mapsService;
      case 'Marker':
        return this.markerService;
      case 'Mastery':
        return this.masteryService;
      case 'MemoryModule':
        return this.memoryModuleService;
      case 'ParticleDrop':
        return this.particleDropService;
      case 'IngredientDrop':
        return this.ingredientDropService;
      case 'Microloop':
        return this.microloopService;
      case 'Minigame':
        return this.minigameService;
      case 'Mission':
        return this.missionService;
      case 'Objective':
        return this.objectiveService;
      case 'Option':
        return this.optionService;
      case 'OptionBox':
        return this.optionboxService;
      case 'Particle':
        return this.particleService;
      case 'PowerUp':
        return this.powerupService;
      case 'PowerUpStat':
        return this.powerupStatService;
      case 'PrimalModifier':
        return this.primalModifierService;
      case 'Profanarium':
        return this.profanariumService;
      case 'QnA':
        return this.qnaService;
      case 'RoadBlock':
        return this._roadBlockService;
      case 'Scenery':
        return this.sceneryService;
      case 'Silicatos':
        return this.silicatosService;
      case 'SoulsGrinder':
        return this.soulsGrinderService;
      case 'Sound':
        return this.soundService;
      case 'Speech':
        return this.speechService;
      case 'Status':
        return this.statusService;
      case 'ChestList':
        return this.chestService;
      case 'SpecialWeapon':
        return this.specialWeaponService;
      case 'SpecialWeaponServiceHC':
        return this.specialWeaponServiceHC;
      case 'MinionStats':
        return this._minionStatsService;
      case 'Upgrades':
        return this.upgradesService;
      case 'Modifier':
        return this.modifierService;
      case 'StatusInfo':
        return this.statusInfoService;
      case 'StatusEffect':
        return this.statusEffectService;
      case 'Tier':
        return this.tierService;
      case 'StoryBox':
        return this.storyboxService;
      case 'Tab':
        return this.tabService;
      case 'Tag':
        return this.tagService;
      case 'Theme':
        return this.themeService;
      case 'TitaniumMining':
        return this.titaniumMiningService;
      case 'TitaniumStorage':
        return this.titaniumStorageService;
      case 'Translation':
        return this.translationService;
      case 'Tutorial':
        return this.tutorialService;
      case 'Video':
        return this.videoService;
      case 'Weapon':
        return this.weaponService;
      case 'Video':
        return this.videoService;
      case 'WeaponUpgrade':
        return this.weaponUpgradeService;
      case 'WeaponRarity':
        return this.weaponsRarityService;
      case 'ItemsSelector':
        return this.itemsSelectorService;
      case 'CharactersSelector':
        return this.charactersSelectorService;
      case 'AnimationsSelector':
        return this.animationsSelectorService;
      case 'PassiveAllowed':
        return this.passiveAllowedService;
      case 'PassiveSkill':
        return this.passiveSkillService;
      case 'TotalUniqueCharactere':
        return this.uniqueCharactereService;
      case 'TotalLevelPoints':
        return this.levelPointsService;
      case 'RelicUses':
        return this.relicUsesService;
      case 'DilemmaBox':
        return this.dilemmaBoxService;
      case 'Dilemma':
        return this.dilemmaService;
      case 'AnswerDilemmaBox':
        return this.answerDilemmaBoxService;
      case 'ConfigThreshold':
        return this.configThresholdService;
      case 'Category':
        return this.categoryStatusEffectService;
      case 'Repetition':
        return this.repetitionStatusEffectService;
      case 'BoostIdBlocks':
        return this.boostIdBlockservice;
      case 'HealingIdBlocks':
        return this.healingIdBlockservice;
      case 'DefensiveIdBlocks':
        return this.defensiveIdBlockservice;
      case 'NegativeIdBlocks':
        return this.negativeIdBlockservice;
      case 'HybridIdBlocks':
        return this.hybridIdBlockservice;
      case 'DispelIdBlocks':
        return this.dispelIdBlockservice;
      case 'BoostTable':
        return this.boostTableService;
      case 'HealingTable':
        return this.healingTableService;
      case 'DefensiveTable':
        return this.defensiveTableService;
      case 'NegativeTable':
        return this.negativeTableService;
      case 'AilmentTable':
        return this.ailmentTableService;
      case 'DispelTable':
        return this.dispelTableService;
      case 'HybridTable':
        return this.hybridTableService;
      case 'AfflictionTable':
        return this.afflictionTableService;
      case 'SpecialSkills':
        return this.specialSkillsService;
      case 'MahankaraBehaviorTable':
        return this.mahankaraBehaviorTableService;
      case 'MahankaraCategories':
        return this.mahankaraCategoriesService
      case 'MahankaraGroupings':
        return this.mahankaraGroupingsService;
      case 'MahankaraConcatenations':
        return this.mahankaraConcatenationsService;
      case 'CategoriesXStressStates':
        return this.categoriesXStressStatesService;
      case 'SubContext':
        return this.subContextService;
      case 'FailureLevels':
        return this.attributeCheckService;
      case 'AttributeCheck':
        return this.attributediceFrustrationService;
      case 'AttributeDiceFrustration':
        return this.uniqueCharactersByHCAndBLService;
      case 'Knowledge':
        return this.knowledgeService;
      case 'SubContextKnowledge':
        return this.subContextKnowledgeService;
      case 'SituationalModifier':
        return this.situationalModifierService;
      case 'DCKnowledgeGuide':
        return this.dCKnowledgeGuideService;
      case 'KnowledgeDiceFrustration':
        return this.knowledgeDiceFrustrationService;
      case 'KnowledgeCheck':
        return this.knowledgeCheckService;
      case 'ArchetypeList':
        return this.archetypeListService;
      case 'TotalArchetypes':
        return this.totalArchetypesService;
      case 'CastsGolden':
        return this.castsGoldenService;
      case 'CastsSouls':
        return this.castsSoulsService;
      case 'CastsSigilos':
        return this.castsSigilosService;
      case 'Amplifiers':
        return this.amplifiersService;
      case 'SpokePlace':
        return this.levelHelperService;
      case 'CtrEvMax':
          return this.cTREVMAXService;
      case 'CTRCollectible':
         return this.cTRCollectibleService;
     case 'LUKCollectible':
        return this.lUKCollectibleService;
     case 'INTCollectible':
        return this.iNTCollectibleService;
    case 'SPDCollectible':
     return this.sPDCollectibleService;
    case 'AIPrompt':
     return this.aiPromptService;
    case 'OpenAIEnvironment':
      return this.openAIEnvironmentService;
    case 'OpenAIKeyGeneral':
      return this.openAIKeyGeneralService;
    }
    return null;
  }

  setLanguage(lang: string) {
    if (IndexStorageService.activeLanguage == lang) return;
    IndexStorageService.activeLanguage = lang;
    this.languageService.activeLanguage = new Language(
      0,
      lang,
      this.userSettingsService
    );
    this._indexStorageService.setOnLanguageChange(lang);
  }

  async clearTranslationData() {
    await this.translationService.clear();
  }

  exportTranslationData() {
    this.translationService.exportAppOrtography();
  }

  GeneratePathOrder() {
    this.areaService.processPaths();
    Alert.ShowSuccess('Path order generated!');
  }

  ngOnDestroy() {
    clearInterval(this.timeout);
  }

  public async toAnalyzeAssetsFolder() {
    try {
      await this.http
        .get(VOICES_INDEX_PATH, { responseType: 'text' })
        .subscribe(async (data) => {
          let index: number[] = [];
          index = await Object.assign(index, JSON.parse(data)?.index);

          index.forEach(async (i) => {
            if (await this.soundService.svcFindById(Sound.generateId(i)))
              return;
            await this.soundService.srvAdd(
              new Sound(i, this.userSettingsService)
            );
          });

          setTimeout(() => {
            this.audioLoader();
          }, 2000);
        });
    } catch (e) {
      Alert.showError(
        e,
        'This is a server ERROR. Please take a screen shot of this error and show to the developer!'
      );
      console.error('Errorrrr', e);
    }
  }

  async fixSpeechAudioID(oldAudioId: string, newAudioId: string) {
    let t = this.speechService.models.filter(
      (spc) => spc.audioId == oldAudioId
    );
    t.forEach((speech) => {
      speech.audioId = newAudioId;
      this.speechService.models[this.speechService.indexOfId(speech.id)] =
        speech;
    });
  }


  navigateToRoadblock(id: string) {
    let levelId: string = id.split('.')[0] + '.' + id.split('.')[1];
    let dialogueId: string =
      id.split('.')[0] + '.' + id.split('.')[1] + '.' + id.split('.')[2];
    this._router.navigate(['levels/' + levelId + '/dialogues/' + dialogueId], {
      fragment: id,
    });
  }

  filterOrphanStoryBoxes() {
    let nullItem = [];
    this.dialogueService.models.forEach((dial) => {
      if (dial.id) this.levelService.svcCloneById(dial?.id);
      else {
        nullItem.push(dial);
      }
    });

    let serv = [];
    for (let dial = 0; dial < this.dialogueService.models.length; dial++) {
      for (let level = 0; level < this.levelService.models.length; level++) {
        if (
          this.dialogueService.models[dial].id !==
          this.levelService.models[level].id
        ) {
          serv.push(this.dialogueService.models[dial]);
        }
      }
    }

    for (let i = 0; i < this._optionService.models.length; i++) {
      for (let j = 0; j < this._storyBoxService.models.length; j++) {
        if (
          this._optionService.models[i].answerBoxId ==
          this._storyBoxService.models[j].id
        ) {
          break;
        }

        if (j == this._storyBoxService.models.length - 1) {
          this.orphanStoryboxes.push(this._optionService.models[i]);
        }
      }
    }

    if (this.orphanStoryboxes.length > 0) this.showWarning = true;
  }

  closeOrphanPopup() {
    this.showWarning = false;
  }

  navigateToOrphan(id: string) {
    let levelId: string = id.split('.')[0] + '.' + id.split('.')[1];
    let dialogueId: string =
      id.split('.')[0] + '.' + id.split('.')[1] + '.' + id.split('.')[2];
    this._router.navigate(['levels/' + levelId + '/dialogues/' + dialogueId], {
      fragment: id,
    });
  }
           
  checksExistNameRarity() {

    this.tierList = this.tierService.models.filter((x) => x.name != "" && x.name != undefined);

    if (this.tierService.models.length > 0) {
      this.tierService.models.forEach((tirs) => {
        if (tirs.selectDrop === this.selectCharacter) {
          this._characterService.models.filter((x) => {
            if (x.rarity !== undefined && x.rarity !== tirs.name && x.rarityId !== tirs.id) this.tierService.svcCreateRarity(this.selectCharacter, x?.rarity);
            else return;
          });
        }
        else if (tirs.selectDrop === this.selectWeapon) {
          this.weaponsRarityService.models.filter((w) => {
            if (w.name !== undefined && w?.name !== tirs?.name && w.rarityId !== tirs.id) this.tierService.svcCreateRarity(this.selectWeapon, w?.name);
            else return;
          });
        }
      });
    } else {
      this._characterService.models.filter((x) => {
        this.tierService.svcCreateRarity(this.selectCharacter, x?.rarity);
      });
      this.weaponsRarityService.models.filter((w) => {
        this.tierService.svcCreateRarity(this.selectWeapon, w?.name);
      });
    }
    this.AssociateId();
  }

  AssociateId() {
    for (let index = 0; index < this.tierList.length; index++) {
      this._characterService.models.filter((char) => {
        if (char.rarity === this.tierList[index].name && char.rarityId === undefined) {
          char.rarityId = this.tierList[index].id;
          this._characterService.svcToModify(char);
        }
      });
      this.weaponsRarityService.models.filter((wea) => {
        if (wea.name === this.tierList[index].name && wea.rarityId === undefined) {
          wea.rarityId = this.tierList[index].id
          this.weaponsRarityService.svcToModify(wea);
        };
      });
    }
  }


}
