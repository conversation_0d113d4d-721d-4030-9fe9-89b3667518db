<div class="row">
    <div class="col-md-12">
        <div class="report-table-wrapper">
            <ng-container *ngIf="investigation.length > 0">
            <table class="table report-table rable-striped">
                <thead class="sticky" style="top: 1px;">
                    <tr>
                        <th>Index</th>
                        <th class="th-clickable" (click)="sortArrayByType(investigation)">Type</th>
                        <th class="th-clickable" (click)="sortArrayByLocation(investigation)">Location</th>
                        <th class="th-clickable" (click)="sortArrayByChoice(investigation)">investigation</th>
                        <th class="th-clickable" (click)="sortArrayByDifficult(investigation)">Difficult Class</th>
                    </tr>
                </thead>

                <tbody>                  
                        <ng-container *ngFor="let dif of investigation; let i = index">
                            <tr [title]="dif?.id" class="tr-clickable" (click)="access(dif.id)">
                                <td class="ind"><span>{{i}}</span></td>
                                <td style="width: 10%;">Difficult Class - Knowledge</td>
                                <td style="width: 30% !important;">
                                    <span style="height: 10px; width: 10px; border-radius: 50%; display: inline-block;"
                                        class="dot">
                                    </span>                         
                                    {{([dif?.id] | location).length > 0 ? ([dif?.id] | location) : 'Ophan'}}
                                </td>
                                <td style="width: 30%">{{dif?.message}}</td>
                                <td style="width: 10%">{{dif?.resultDC }}</td>
                            </tr>
                        </ng-container>
                 </tbody>
            </table>
        </ng-container>

            <ng-container *ngIf="investigation.length === 0">
                <div style="text-align: center;">
                    <h4>Empty Difficult Class - Knowledge List</h4>
                </div>
            </ng-container>
        </div>
    </div>
</div>