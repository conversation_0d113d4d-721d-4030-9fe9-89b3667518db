
<div class="card list-header"
style="height: 70px; margin: 30px; margin-bottom: 0px;">
  <div class="header">
    <button class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('class-selection')">
      1 - Item Class
    </button>
    <button class="{{activeTab === 'memorymodule-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('memorymodule-selection')">
      2 - Memory Modules
    </button>
  </div>
</div>

<app-memorymodule-class-selection *ngIf="activeTab === 'class-selection'"> </app-memorymodule-class-selection>
<app-memorymodule-information *ngIf="activeTab === 'memorymodule-selection'" (itemSelected)="switchToTab('item-record')"> </app-memorymodule-information>
