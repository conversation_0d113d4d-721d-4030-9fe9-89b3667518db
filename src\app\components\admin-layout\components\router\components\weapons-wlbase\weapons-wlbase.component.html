<div class="main-content">
    <div class="container-fluid">
      <!--Header-->
      <div class="list-header-row update">
        <div class="card">
    
            <div class="card-header-content" style="position: absolute; top: 10%;">
                <h3 class="title">{{ cardTitle }}</h3> 
                <p style="width:60vw;"class="category">{{ description}}</p>             
              </div>     

          <div style="display: flex; align-items: end; justify-content: end;">
            <div style="margin-right: 15px; margin-bottom: 16px;">
                <button (click)="switchToTab('commonWeapons')" 
                class="{{activeTab === 'commonWeapons' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
                COMMON WEAPONS
              </button>
              <button (click)="switchToTab('specialWeapons')"
                class="{{activeTab === 'specialWeapons' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
                SPECIAL WEAPONS
              </button>
            </div>           
          </div>
        </div>
    </div>
</div>

<app-common-weapons *ngIf="activeTab === 'commonWeapons'"></app-common-weapons>
<app-special-weapons *ngIf="activeTab === 'specialWeapons'"></app-special-weapons>
