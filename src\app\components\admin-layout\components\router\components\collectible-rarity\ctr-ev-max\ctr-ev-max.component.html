<div class="main-content">
    <div class="container-fluid">
        <!--Header-->
        <div class="list-header-row update">
            <div class="card">
                <div class="card-header-content">
                    <h3 class="title">{{ cardTitle }}</h3>
                </div>
                <div class="btn-MAX">
                    <button style="margin-left: 2px;" class="btn btn-fill" (click)="btnClickContext()">Back Character
                        List</button>
                </div>
            </div>
        </div>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th class="trBC" style="width: 1%;">Index</th>
                    <th class="trBC" style="width: 4%;">Name</th>
                    <th class="trBC" style="width: 6%;">Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="gray">{{1}}</td>
                    <td>{{'CTR-MAX'}}</td>
                    <td>
                        <input style="width: 100%;" class="background-input-table-color form-control form-short "
                            placeholder=" " type="number" #ctr_max (change)="addValue('CTR-MAX', ctr_max.value)"
                            [value]="listValueCtrEv_Max?.ctr_max" />
                    </td>
                </tr>
                <tr>
                    <td class="gray">{{2}}</td>
                    <td>{{'EV-MAX'}}</td>
                    <td>
                        <input style="width: 100%;" class="background-input-table-color form-control form-short "
                            placeholder=" " type="number" #ev_max (change)="addValue('EV-MAX', ev_max.value)"
                            [value]="listValueCtrEv_Max?.ev_max" />
                    </td>
                </tr>

            </tbody>
        </table>
    </div>
</div>