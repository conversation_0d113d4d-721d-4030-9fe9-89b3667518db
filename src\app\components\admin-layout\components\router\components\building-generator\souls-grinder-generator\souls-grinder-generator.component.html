
<div class="card list-header"
     style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header">        
        <button style="position:relative; float:right"
                class="{{activeTab2 === 'storageC' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('storageC')">
            Storage C
        </button>
        <button style="position:relative; float:right"
                class="{{activeTab2 === 'storageB' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('storageB')">
             Storage B
        </button>
        <button style="position:relative; float:right"
                class="{{activeTab2 === 'storageA' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('storageA')">
            Storage A
        </button>
        <button style="position:relative; float:right"
                class="{{activeTab2 === 'sgrinder' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('sgrinder')">
             Souls Grinder
        </button>
    </div>
</div>

<app-souls-grinder-storageA *ngIf="activeTab2 === 'storageA'"></app-souls-grinder-storageA>

<app-souls-grinder *ngIf="activeTab2 === 'sgrinder'"></app-souls-grinder>
<app-souls-grinder-storageB *ngIf="activeTab2 === 'storageB'"></app-souls-grinder-storageB>
<app-souls-grinder-storageC *ngIf="activeTab2 === 'storageC'"></app-souls-grinder-storageC>