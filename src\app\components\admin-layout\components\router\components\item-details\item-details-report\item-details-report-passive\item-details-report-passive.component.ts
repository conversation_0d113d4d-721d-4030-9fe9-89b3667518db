import { ChangeDetector<PERSON><PERSON>, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Item, Weapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, WeaponService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { Bonus } from '../../../../../../../../lib/@bus-tier/models/Bonus';
import { ConditionTrigger } from '../../../../../../../../lib/@bus-tier/models/ConditionTrigger';
import { ItemClass } from '../../../../../../../../lib/@bus-tier/models/ItemClass';
import { PassiveAllowed } from '../../../../../../../../lib/@bus-tier/models/PassiveAllowed';
import { PassiveSkill } from '../../../../../../../../lib/@bus-tier/models/PassiveSkill';
import { BonusService } from '../../../../../../../../services/bonus.service';
import { ConditionTriggerService } from '../../../../../../../../services/conditionTrigger.service';
import { ItemClassService } from '../../../../../../../../services/item-class.service';
import { PassiveAllowedService } from '../../../../../../../../services/passiveAllowed.service';
import { PassiveSkillService } from '../../../../../../../../services/passiveSkill.service';

export interface IDescription {
  idItem?: number;
  mergeDescription?: string;
  descriptionBonus?: string;
  idBonus?: string;
  descriptionCondition?: string;
  idCondition?: string;
  databaseId?: string;
  idPassiveAllowed?: string;
}

@Component({
  selector: 'app-item-details-report-passive',
  templateUrl: './item-details-report-passive.component.html',
  styleUrls: ['./item-details-report-passive.component.scss'],
})
export class ItemDetailsReportPassiveComponent implements OnInit, OnDestroy{

  constructor(
    private _customService: CustomService,
    private _itemService: ItemService,
    private passiveAllowedService: PassiveAllowedService,
    private bonusService : BonusService,
    private conditionTriggerService: ConditionTriggerService,
    private _weaponService: WeaponService,
    private _itemClassService: ItemClassService,
    private _passiveSkill: PassiveSkillService,
    private ref: ChangeDetectorRef,

  ) { }

  custom: Custom;
  weapon: Weapon;
  itemCommonWeapons: ItemClass;
  itemSpecialWeapons: ItemClass;
  passiveAllowed: PassiveAllowed[] = [];
  listCondition: ConditionTrigger[] = [];
  listBonus: Bonus[] = [];
  passiveList = {
    idNameWeapon: '',
    nameWeapon: '',
    codeType: '',
    descriptions: [
      {
        idPassiveAllowed: '',
        idItem: null,
        descriptionBonus: '',
        idBonus: '',
        descriptionCondition: '',
        idCondition: '',
        mergeDescription: '',
        databaseId:'',
      }
    ] as IDescription[]
  };
  contextPassiveList: any;
  currentCharacter : Item;
  passiveDB: PassiveSkill[];
  itemsDB: PassiveSkill[];
  selectTextCommon: string = ''; // Para armas comuns
  selectTextSpecial: string[] = []; // Para armas especiais (agora uma lista)
  selectedPassive: IDescription | null = null;
  addTextCommon: IDescription | null = null; // Mantém o item selecionado para armas comuns
  addTextSpecial: IDescription[] = []; // Mantém os itens selecionados para armas especiais
  isIdCommonWeapon = false;
  isIdEspecialWeapon = false;
  isCommonDisabled = false;
  isWeaponsCommonsSpecial = true;
  timeout: any; 

  @Output() selectedClassessIdChange = new EventEmitter<string[]>();
  @Input() weaponId = ''


  async ngOnInit(): Promise<void> {
    this.custom = await this._customService.svcGetInstance();
    this.weapon = this._weaponService.models.find(w => w.itemId === this.custom.selectedWeaponId); 
    this.currentCharacter = this._itemService.svcFindById(this.weapon.itemId); 
    this.getWeaponsTypes();
    
    await this.bonusService.toFinishLoading();
    this.listBonus = this.bonusService.models;  
    await this.passiveAllowedService.toFinishLoading();
    this.passiveAllowed = this.passiveAllowedService.models;  
    await this.conditionTriggerService.toFinishLoading();
    this.listCondition = this.conditionTriggerService.models; 

    this.checkItemsPassive(); 
  }

//Quando navega pelas no icone para frente ou pra trás é enviado o ID da arma.
  async getWeaponsTypes() { 
      if(this.currentCharacter.codeType === "IC3") { //Armas Comuns       
        this.isIdCommonWeapon = true;
        this.isIdEspecialWeapon = false;
        this.isWeaponsCommonsSpecial = true;
      } else  if(this.currentCharacter.codeType === "IC20") { // Armas Especiais        
        this.isIdEspecialWeapon = true;
        this.isIdCommonWeapon = false;
        this.isWeaponsCommonsSpecial = true;
      } else {
        this.isWeaponsCommonsSpecial = false;
      }
  }


  compareIds() {
    this.passiveList.idNameWeapon = this.currentCharacter.id;
    this.passiveList.nameWeapon = this.currentCharacter.name;

    this.passiveList.descriptions = [];
  
    this.passiveAllowed.forEach(passive => {
      let descriptionBonus = '';
      let idBonus = '';
      let descriptionCondition = '';
      let idCondition = '';
      let idPassiveAllowed = '';

      const bonusItem = this.listBonus.find(bonus => bonus.idValue === passive.idValue1);
      if (bonusItem) {
        idBonus = bonusItem.id;
        descriptionBonus = bonusItem.description;
        idPassiveAllowed = passive.idValue1;
      }

      const conditionItem = this.listCondition.find(condition => condition.idValue === passive.idValue2);
      if (conditionItem) {
        idCondition = conditionItem.id;
        descriptionCondition = conditionItem.description;
      }

      this.passiveList.descriptions.push({ 
        idPassiveAllowed,  
        idBonus,
        descriptionBonus,
        idCondition,
        descriptionCondition,
        mergeDescription: `${descriptionBonus} 🠪 ${descriptionCondition}`.trim()
      });
    });

    this.passiveList.descriptions = this.passiveList.descriptions.filter(description => {
      return description.idBonus || description.descriptionBonus || description.idCondition || description.descriptionCondition;
    });

    for (let index = 0; index < this.passiveList.descriptions.length; index++) {
      this.passiveList.descriptions[index].idItem = index;      
    }   
  }


  async checkItemsPassive() {
 /*
    this._passiveSkill.models = [];
    this._passiveSkill.toSave();
*/
    this.compareIds();//Lista de Bonus e Condições
    //Verfica se o id da Arma contém na base de dados
    await this._passiveSkill.toFinishLoading();    
    this.passiveDB = this._passiveSkill.models.filter((x) => x.idNameWeapon === this.currentCharacter.id && x.descriptions.length > 0);
    this.itemsDB = this._passiveSkill.models;// pega todos os itens que contém na base de dados

    //Remove a armas da lista de seleção se existir.
     if (this.passiveDB.length > 0) {

      for (let index = 0; index < this.passiveDB.length; index++) {
        if (this.passiveDB[0].codeType === "IC3") {
          // Arma Comum
          this.passiveList.descriptions = this.passiveList?.descriptions.filter(
            comum => !this.passiveDB[0].descriptions.some(x => comum.idItem === x.idItem));
            this.isIdCommonWeapon = true;        
        }
        else if (this.passiveDB[0].codeType === "IC20") {
          this.passiveList.descriptions = this.passiveList?.descriptions.filter(
            especial => !this.passiveDB[0].descriptions.some(x => especial.idPassiveAllowed === x.idPassiveAllowed)
          );
        }       
      } 
      // Remove os itens que contém na base de dados da lista de seleção.
      if (this.itemsDB.length > 0) { 
        for (let i = 0; i < this.itemsDB.length; i++) {
          const item = this.itemsDB[i];
          if (item.codeType === "IC3" || item.codeType === "IC20") {               
            this.passiveList.descriptions = this.passiveList?.descriptions.filter(
              p =>  !item.descriptions.some(x => p.idItem === x?.idItem));        
          }    
        }
      }   

      } else {
              // Remove os itens que contém na base de dados da lista de seleção.
              if (this.itemsDB.length > 0) { 
                this.compareIds();
                for (let i = 0; i < this.itemsDB.length; i++) {
                  const item = this.itemsDB[i];
                  if (item.codeType === "IC3" || item.codeType === "IC20") {               
                    this.passiveList.descriptions = this.passiveList?.descriptions.filter(
                      p =>  !item.descriptions.some(x => p.idItem === x?.idItem));       
                  }    
                }
              }   
      }
   

    if(this.passiveDB.length === 0 || this.passiveDB[0].descriptions.length === 0) {
      this.selectTextCommon = '';
      this.isCommonDisabled = false;
    } else if (this.passiveDB[0].descriptions.length > 0){
      this.isCommonDisabled = true;
    }
    this.ref.detectChanges();
  }
  //Remove armas comuns da lista de seleção
  removePassiveFromPassiveList(item) {
      if(item.descriptions.length > 0) {
        this.passiveList.descriptions = this.passiveList?.descriptions.filter(p => p.idItem !== item.descriptions[0].idItem);   
    } 
  }
  

  selectItem(event: Event) {
    const selectElement = event.target as HTMLSelectElement;
    const selectedIndexes = Array.from(selectElement.selectedOptions).map(option => parseInt(option.value, 10));
    const idWeapon = this.currentCharacter.id;
    const nameWeapon = this.currentCharacter.name;

    selectedIndexes.forEach(index => {
      const selectedItem = this.passiveList.descriptions[index];

      if (this.isIdCommonWeapon) {
        if (!this.selectTextCommon) {         
          this.selectTextCommon = selectedItem.mergeDescription;
          this.passiveList.descriptions = this.passiveList.descriptions.filter(item => item.idItem !== selectedItem.idItem);

          if(this.passiveDB.length > 0) {
            this.passiveDB.filter((x) =>  x.descriptions.push(selectedItem) );
            this._passiveSkill.svcToModify(this.passiveDB[0]); 
            this._passiveSkill.toSave();
          } else {
            this._passiveSkill.createNewPasseiveSkill(selectedItem, idWeapon, nameWeapon, "IC3");
          }         
        }
      } else if (this.isIdEspecialWeapon) {

        if (!this.selectTextSpecial.includes(selectedItem.mergeDescription)) {
          this.selectTextSpecial.push(selectedItem.mergeDescription);
          this.addTextSpecial.push(selectedItem);
          
          const codeType = "IC20";    
          if(this.passiveDB.length > 0) {
            this.passiveDB.filter((x) => this.addTextSpecial.forEach((item) => x.descriptions.push(item)) );
            this._passiveSkill.svcToModify(this.passiveDB[0]); 
            this._passiveSkill.toSave();
            this.selectTextSpecial = [];
            this.addTextSpecial = [];
          }
           else {
            this.addTextSpecial.forEach(item => {
              if(!item.databaseId) {
                this._passiveSkill.createNewPasseiveSkill(item, idWeapon, nameWeapon, codeType);
                this.selectTextSpecial = [];
                this.addTextSpecial = [];
              }        
            });
           }

          this.passiveList.descriptions = this.passiveList.descriptions.filter(passive => passive.idPassiveAllowed !== selectedItem.idPassiveAllowed);
        }
      }
  
      this.removePassive(selectedItem.idItem);
    });
  
    this.checkItemsPassive();
  }

  
  removePassive(idItem: number) {
    this.passiveList.descriptions = this.passiveList.descriptions.filter(passive => passive.idItem !== idItem);
  }

  returnToPassiveList(item: IDescription | string) {

    if (this.passiveDB.length > 0) {
      const passiveDescriptions = this.passiveDB[0].descriptions;
  
      // Verificar se o item é uma string (caso de armas especiais)
      if (typeof item === 'string') {
        const index = this.selectTextSpecial.indexOf(item);
        if (index > -1) {
          this.selectTextSpecial.splice(index, 1);
          const originalItem = this.addTextSpecial.find(i => i.mergeDescription === item);
  
          // Encontrar o item original pelo mergeDescription e devolver ao passiveList
          if (originalItem) {
            // Remover o item do array this.passiveDB[0].descriptions
            this.passiveDB[0].descriptions = passiveDescriptions.filter(desc => desc.idItem !== originalItem.idItem);           

            // Adicionar o item de volta ao passiveList
            this.passiveList.descriptions.push(originalItem);
            // Remove do banco de dados
            this._passiveSkill.svcToRemove(this.passiveDB[0].id);
           // this._passiveSkill.svcToModify(this.passiveDB[0]);
            this._passiveSkill.toSave();
  
            // Atualizar a lista de itens especiais
            this.addTextSpecial = this.addTextSpecial.filter(i => i.mergeDescription !== item);
          }
        }
      
      } else {
        // Caso de armas comuns
        this.selectTextCommon = '';
        this.addTextCommon = null;
  
        // Remover o item do array this.passiveDB[0].descriptions comparando o idItem
        this.passiveDB[0].descriptions = passiveDescriptions.filter(desc => desc.idItem !== item.idItem);
        this.isCommonDisabled = false;
        // Adicionar o item de volta ao passiveList
        this.passiveList.descriptions.push(item);  
        this._passiveSkill.svcToModify(this.passiveDB[0]);
        this._passiveSkill.toSave();
      }  

      this.compareIds();
      this.checkItemsPassive();
    }
  }
  
    //chega novo ID de armas ao navegar pelos botões
    reset(character) {
      this.weaponId = character;  
      this._weaponService.toFinishLoading();
      this.weapon = this._weaponService.models.find(w => w.itemId === this.weaponId);
      this.currentCharacter = this._itemService.svcFindById(this.weapon?.itemId);
    
      if (this.currentCharacter) {
        this.getWeaponsTypes();
        this.checkItemsPassive();
      } else {  
        this.isWeaponsCommonsSpecial = false;
      }
    }

  ngOnDestroy() {
    clearInterval(this.timeout);
  }
  
}
