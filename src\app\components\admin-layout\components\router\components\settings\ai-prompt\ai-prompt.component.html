<div class="list-header-row update">
  <div class="card">
    <app-header-with-buttons [cardTitle]="'AI Prompt'"
    [cardDescription]="'Information about the prompts that are used in AI'"
    [isBackButtonEnabled]="true" (cardBackButtonClick)="redirectToSettings()"
    [rightButtonTemplates]="[addAIPromptTemplate]"></app-header-with-buttons>
  </div>
</div>

<div class="main-content">
  <div class="card horizontal-scroll">
    <table class="table table-list card">
     <thead style="z-index: 0 !important;">
        <tr>
          <th style="width: 70px;">Index</th>
          <th style="width: 210px;">Type</th>
          <ng-container *ngIf="this.listEnvironments.length > 0">
            <th>Environment Name</th> 
          </ng-container>         
          <th>PROMPT</th>                
          <th>Description</th>
          <th style="width: 250px;">NOTES</th>
          <th style="width: 100px;">Action</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let prompt of prompts; let i = index">
          <tr>
            <td class="td-id gray" style="width: 70px;">
             {{i + 1}}
            </td>
            <td style="width: 210px;">
              <select class="dropdown filter-dropdown limited typeSelect" #InputType
                (change)="selectType(InputType.value, prompt)" [(ngModel)]="prompt.selectType">
                <option value="" selected>Select type</option>   
                <option *ngFor="let type of types" value="{{ type }}">{{ type }}</option>
            </select>
            </td>
            <ng-container *ngIf="this.listEnvironments.length > 0">
              <td style="width: 210px;">
                <select class="dropdown filter-dropdown limited typeSelect" #Environment
                  (change)="selectEnvironment(Environment.value, prompt)" [(ngModel)]="prompt.nameEnvironmentAi">
                  <option value="" selected>Select Environment</option>          
                  <option *ngFor="let environment of listEnvironments" value="{{ environment.name }}">{{ environment.name }}</option>
                </select>
                </td> 
            </ng-container> 
            <td>
              <textarea 
                #promptPrompt 
                type="text" 
                value="{{ prompt.prompt || '' }}"
                placeholder="Prompt..." 
                (change)="this.changePrompt(prompt, promptPrompt.value, 'prompt')" 
                class="form-control">
                {{prompt.prompt}}
              </textarea>
            </td> 
            <td>
              <textarea 
                #promptDescription 
                type="text" 
                value="{{ prompt.description|| '' }}"
                placeholder="Description..." 
                (change)="this.changePrompt(prompt, promptDescription.value, 'description')" 
                class="form-control">
                {{prompt.description}}
              </textarea>
            </td>
            <td>
              <textarea 
                #promptNotes 
                type="text" 
                value="{{ prompt.notes || '' }}"
                placeholder="Notes..." 
                (change)="this.changePrompt(prompt, promptNotes.value, 'notes')" 
                class="form-control">
                {{prompt.notes}}
              </textarea>
            </td>
            <td class="td-actions">
              <button class="btn btn-danger btn-fill btn-remove" (click)="deletePrompt(prompt)">
                <i class="pe-7s-close"></i>
              </button>            
            </td>   
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div> 
</div>
