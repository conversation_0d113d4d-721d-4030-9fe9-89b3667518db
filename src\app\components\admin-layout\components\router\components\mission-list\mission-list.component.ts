import { Component } from '@angular/core';
import { Area, Mission } from 'src/app/lib/@bus-tier/models';
import { AreaService } from 'src/app/services/area.service';
import { MissionService } from 'src/app/services/mission.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { PopupService } from 'src/app/services/popup.service';
import { Alert, Popup } from 'src/lib/darkcloud';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { Index, comparable } from 'src/lib/others';
import { ReviewService } from 'src/app/services/review.service';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ObjectiveService } from 'src/app/services/objective.service';
import { EventService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-mission-list',
  templateUrl: './mission-list.component.html',
  styleUrls: ['./mission-list.component.scss']
})
export class MissionListComponent extends TranslatableListComponent<Mission> {
  protected override lstFilterParameters: EasyMVC.Filter[] = [{ name: 'areaId' }];
  public missionIdToggle: string;
  public preloadedAreas: Area[] = [];
  public areaNames: Index<string>;
  public missions: Mission[] = [];

  noObjectives: Mission[] = [];
  noAssigned: Mission[] = [];
  multipleAssigned: Mission[] = [];
  singleAssigned: Mission[] = [];

  noMissionAssigned: Mission[] = [];
  multipleMissionAssigned: Mission[] = [];
  singleMissionAssigned: Mission[] = [];
  invertObjectives: number = 1;
  invertMissions: number = 1;
  description: string = '';
  caseSensitive: boolean = false;
  accentSensitive: boolean = false;

  constructor(
    _userSettingsService: UserSettingsService,
    private _areaService: AreaService,
    _activatedRoute: ActivatedRoute,
    private _missionService: MissionService,
    private _objectiveService: ObjectiveService,
    private _popupService: PopupService,
    private _reviewService: ReviewService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _eventService: EventService,

  ) {
    super(_missionService, _activatedRoute, _userSettingsService, 'id', _translationService, _languageService);
  }

  public readonly addMissionTemplate: Button.Templateable = {
    title: 'Add a new instance to the list',
    onClick: this.addMission.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };


  override lstInit() {
    this._missionService.toFinishLoading();
   //this.inicializedFieldsReviewed();
    this.preloadAreas();
    this.missionIdToggle = this._activatedRoute.snapshot.fragment;
    this.missions = this._missionService.models;

    this.description = `Showing ${this.missions.length} results`

  }

  inicializedFieldsReviewed() {
    this._missionService.models.forEach((character) => {
      character.isReviewedName = !!character.isReviewedName;
      character.isReviewedDescription = !!character.isReviewedDescription;
      this._missionService.svcToModify(character);
    });
  }
  

  preloadAreas(): void {
    const areaIds: string[] = [];
    this.areaNames = {};
    this._missionService.models.forEach((mission) => {
      const areaId = mission.areaId;
      if (!areaIds.includes(areaId)) {
        areaIds.push(areaId);
      }
    });
    this.preloadedAreas = this._areaService.svcFilterByIds(areaIds);
  }

  protected override filterItem(mission: Mission) {
    return (
      (this.lstFilterValue['areaId'] as string) === 'ALL' ||
      mission.areaId === (this.lstFilterValue['areaId'] as string)
    );
  }

  async addMission() {
    let addMmission;

    try {
      addMmission = await this._missionService.svcPromptCreateNew();
    }
    catch (e) {
      Alert.showError("This Atributte already exists!");
      return
    }
    if (!addMmission) return;

    this.lstResetHighlights();
    this.HighlightElement(addMmission.id, 110, true);
    await this._missionService.srvAdd(addMmission);

    if (this.missions.includes(addMmission)) return;
    else this.missions.push(addMmission);
  }

  override lstAfterInitFetchList() {
    this.areaNames = {};
    this._areaService.models.forEach((area) => {
      this.areaNames[area.id] = area.name;
    });
  }

  public xpChanged(mission: Mission, value: string) {
    let amount = 0;
    mission.objectiveIds.forEach(objectiveId => {
      let objective = this._objectiveService.svcFindById(objectiveId);
      if (objective.xp) {
        amount += +objective.xp;
      }
    })
    mission.xp = amount;
    this._missionService.svcToModify(mission);
  }

  sortAscending = true;

  sortListById() {
    this.missions.sort((a, b) => {
      // Extrai o número do id, removendo a letra "M" e convertendo para número
      const idA = parseInt(a.id.replace("M", ""), 10);
      const idB = parseInt(b.id.replace("M", ""), 10);

      if (this.sortAscending) {
        return idA - idB; // Ordena de forma crescente
      } else {
        return idB - idA; // Ordena de forma decrescente
      }
    });
    this.sortAscending = !this.sortAscending;
  }

  protected override specialSort(parameter: Sorting.Parameter) {
    switch (parameter) {
      case 'assigned':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? this._reviewService.reviewResults[a.id].assignedAt.length >
            this._reviewService.reviewResults[b.id].assignedAt.length ? 1 : -1 : this._reviewService.reviewResults[a.id].assignedAt.length <
              this._reviewService.reviewResults[b.id].assignedAt.length ? 1 : -1);
        break;
      case 'area':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? comparable(this.areaNames[a.areaId]) > comparable(this.areaNames[b.areaId])
            ? 1 : -1 : comparable(this.areaNames[a.areaId]) < comparable(this.areaNames[b.areaId]) ? 1 : -1);
        break;
      case 'objectives':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? this._reviewService.reviewResults[a.id].objectivesCompleted &&
            !this._reviewService.reviewResults[b.id].objectivesCompleted ? 1 : -1 : !this._reviewService.reviewResults[a.id].objectivesCompleted &&
              this._reviewService.reviewResults[b.id].objectivesCompleted ? 1 : -1);
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  public async toPromptChangeMissionArea(mission: Mission) {
    const selectedAreaButton = await this._popupService.fire<Area, Area>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          hideButton:
          {
            value: this._areaService.svcFindById(Area.getSubIdFrom(mission.id)),
          },
        }
      )
    );
    if (!selectedAreaButton) return;

    mission.areaId = selectedAreaButton.value?.id;
    await this._missionService.svcToModify(mission);
  }

  public async selectXP(mission: Mission) {
    await Alert.showNumberField('Choose xp amount').then(async (e) => {
      if (e.isDismissed) return;

      mission.xp = e.value;
      if (mission.xp <= 0) mission.xp = undefined;

      await this._missionService.svcToModify(mission);
    });
  }

  public downloadMissionOrtography(mission: Mission) {
    this._translationService.getMissionOrtography(mission, true);
  }

  public isMissionTranslated(mission: Mission) {
    return this._translationService.checkTranslation(mission.id, 'EN-US');
  }

  sortObjectives() {
    this.noAssigned = [];
    this.noObjectives = [];
    this.singleAssigned = [];
    this.multipleAssigned = [];
    let canBreak: boolean = false;
    let goToSingleMultiple: boolean = false;

    ////////////////////////// No Objectives ///////////////////////////////
    for (let i = 0; i < this._missionService.models.length; i++) {
      goToSingleMultiple = false;

      if (this._missionService.models[i]?.objectiveIds?.length == 0 ||
        this._missionService.models[i]?.objectiveIds == undefined) {
        this.noObjectives.push(this._missionService.models[i]);
      }
      else {
        ///////////////////////////// Not Assigned //////////////////////////////
        let canContinue: boolean = false;
        let counter: number = 0;
        //Verify if at least one objective does not have reference in events
        for (let k = 0; k < this._missionService.models[i]?.objectiveIds?.length; k++) {
          for (let j = 0; j < this._eventService.models.length; j++) {
            if (this._missionService.models[i]?.objectiveIds[k] == this._eventService.models[j]?.objectiveId) {
              canContinue = true;
              counter = counter + 1;
              break;
            }
            //If at least one objective does not have a reference in the events, its is marked as: hasNotAssigned.
            else if (j == this._eventService.models.length - 1) {
              this.noAssigned.push(this._missionService.models[i]);
              canBreak = true;
            }
          }
          if (canContinue) {
            canContinue = false;
            continue;
          }
          if (canBreak) {
            canBreak = false;
            break;
          }
        }
        if (counter == this._missionService.models[i]?.objectiveIds?.length) goToSingleMultiple = true;
        ////////////////////////// Single and Multiple ///////////////////////////////////
        if (goToSingleMultiple) {
          let isRepeated: boolean = false;
          let locations = [];

          for (let k = 0; k < this._missionService.models[i]?.objectiveIds?.length; k++) {
            for (let j = 0; j < this._eventService.models.length; j++) {
              if (this._missionService.models[i]?.objectiveIds[k] === this._eventService.models[j]?.objectiveId) {
                if (!locations.some(obj => obj?.objectiveId === this._missionService.models[i]?.objectiveIds[k])) {
                  locations.push(this._eventService.models[j]);
                }
                else
                  isRepeated = true;
              }
            }
          }

          if (isRepeated) this.multipleAssigned.push(this._missionService.models[i]);
          else this.singleAssigned.push(this._missionService.models[i]);
        }
      }
    }

    ////////////////////////////// Result Concatenation /////////////////////////////////////
    this.missions = [];
    this.invertObjectives = this.invertObjectives + 1;
    if (this.invertObjectives % 4 == 1)
      this.missions = this.noAssigned.concat(this.noObjectives).concat(this.singleAssigned).concat(this.multipleAssigned);
    else if (this.invertObjectives % 4 == 2)
      this.missions = this.noObjectives.concat(this.singleAssigned).concat(this.multipleAssigned).concat(this.noAssigned);
    else if (this.invertObjectives % 4 == 3)
      this.missions = this.singleAssigned.concat(this.multipleAssigned).concat(this.noAssigned).concat(this.noObjectives);
    else if (this.invertObjectives % 4 == 0)
      this.missions = this.multipleAssigned.concat(this.noAssigned).concat(this.noObjectives).concat(this.singleAssigned);
  }

  sortMissions() {
    this.singleMissionAssigned = [];
    this.multipleMissionAssigned = [];
    this.noMissionAssigned = [];

    let goToSingleMultiple: boolean = false;

    for (let j = 0; j < this._missionService.models.length; j++) {
      goToSingleMultiple = false;
      /////////////////////////////// NoAssigned /////////////////////////////////
      for (let i = 0; i < this._eventService.models.length; i++) {
        //Just the assign mission is counted as mission, the others are objectives and fail
        if (this._eventService.models[i].missionId == this._missionService.models[j].id && this._eventService.models[i].type == 3) {
          goToSingleMultiple = true;
          break;
        }
        else if (i == this._eventService.models.length - 1) {
          this.noMissionAssigned.push(this._missionService.models[j]);
        }
      }
      ////////////////////////// Single and Multiple ///////////////////////////////////
      if (goToSingleMultiple) {
        let aux = [];
        for (let i = 0; i < this._eventService.models.length; i++) {
          if (this._eventService.models[i].missionId == this._missionService.models[j]?.id &&
            !aux.some(obj => obj === this._eventService.models[i].id) &&
            //Just the assign mission is counted as mission, the others are objectives and fail
            this._eventService.models[i].type == 3) {
            aux.push(this._eventService.models[i].id);
          }
        }

        if (aux.length == 1)
          this.singleMissionAssigned.push(this._missionService.models[j]);
        else
          this.multipleMissionAssigned.push(this._missionService.models[j]);
      }
    }
    ////////////////////////////// Result Concatenation /////////////////////////////////////
    this.missions = [];
    this.invertMissions = this.invertMissions + 1;
    if (this.invertMissions % 3 == 1)
      this.missions = this.noMissionAssigned.concat(this.singleMissionAssigned).concat(this.multipleMissionAssigned);
    else if (this.invertMissions % 3 == 2)
      this.missions = this.singleMissionAssigned.concat(this.multipleMissionAssigned).concat(this.noMissionAssigned);
    else if (this.invertMissions % 3 == 0)
      this.missions = this.multipleMissionAssigned.concat(this.noMissionAssigned).concat(this.singleMissionAssigned);
  }
  /* 
    search(term:string) 
    {
      let aux : Mission[] = this.missions;
      this.missions = [];
      let information:string = '';
  
      for(let i = 0; i < aux.length; i++)
      {
        information = this._userSettingsService.getInformation(aux[i].id)?.authorNotes;
        if(aux[i]?.description?.includes(term) || aux[i]?.name?.includes(term) || information?.includes(term))
        {
          this.missions.push(aux[i]);
        }
      }
  
      if(term == '') this.missions = this._missionService.models;
  
      this.description = `Showing ${this.missions.length} results`
    } */

  search(term: string): void {
    this.missions = [];
    this.lstSearchTerm = term;

    for (const model of this._missionService.models)
      if (this.itemMatchesQuery(model, term)) this.missions.push(model);

    if (!term) this.missions = this._missionService.models;

    this.description = `Showing ${this.missions.length} results`;
  }

  itemMatchesQuery(model: Mission, query: string): boolean {
    let nameMatch: boolean = false;
    let descriptionMatch: boolean = false;

    const normalizedQuery = this.accentSensitive ? query.normalize('NFKD').replace(/[\u0300-\u036f]/g, '') : query;
    const name = this.caseSensitive ? model.name : model.name;

    if (this.accentSensitive) {
      if (this.caseSensitive) {
        let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        nameMatch = localName.includes(normalizedQuery);
      }
      else {
        let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        nameMatch = localName.toLowerCase().includes(normalizedQuery.toLowerCase());
      }
    }
    else if (this.caseSensitive) {
      let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
      nameMatch = localName.includes(normalizedQuery);
    }
    else nameMatch = name.toLowerCase().trim().includes(normalizedQuery.toLowerCase().trim());

    const description = this.caseSensitive ? model?.description : model?.description?.toLowerCase();
    if (this.accentSensitive) {
      if (this.caseSensitive) {
        let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        descriptionMatch = localDescription.includes(normalizedQuery);
      }
      else {
        let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        descriptionMatch = localDescription.toLowerCase().includes(normalizedQuery.toLowerCase());
      }
    }
    else if (this.caseSensitive) {
      let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
      descriptionMatch = localDescription.includes(normalizedQuery);
    }
    else descriptionMatch = description?.includes(normalizedQuery);

    return nameMatch || descriptionMatch;
  }


  async removeMision(mission: Mission) {
    const confirm: boolean = await Alert.showRemoveAlert(mission.name);

    if (confirm) {
      this._missionService.svcToRemove(mission.id);
      return this.lstInit();
    }
    return false;
  }

  searchFilterOptions(event) {
    this.caseSensitive = event.caseSensitive;
    this.accentSensitive = event.accentSensitive;
  }

  changeName(mission: Mission, fieldName, value:string) {
    mission.isReviewedName = false;
    mission.revisionCounterNameAI = 0;
    this.lstOnChange(mission, fieldName, value);
  }

  changeDescription(mission: Mission, fieldName, value:string) {
    mission.isReviewedDescription = false;
    mission.revisionCounterDescriptionAI = 0;
    this.lstOnChange(mission, fieldName, value);
  }

}
