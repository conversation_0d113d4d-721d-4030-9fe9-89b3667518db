<!-- Multiple roadblocks display with AND/OR conditions -->
<div class="roadblock-info-container" *ngIf="roadblocks && roadblocks.length > 0; else singleRoadblock">
  <div class="roadblock-info-box">
    <div class="roadblock-multiple-header">Roadblocks ({{ roadblocks.length }})</div>
    <div class="roadblock-item" *ngFor="let info of roadblockInfos; let i = index">
      <!-- Roadblock type with lock icon indicator -->
      <div class="roadblock-type">
        <div *ngIf="shouldShowLockIcon(info.roadblock)"
             class="roadblock-lock-container"
             [style.background-color]="getRoadblockColor(info.roadblock)">
          <i class="pe-7s-lock roadblock-lock-icon" [style.color]="getLockIconColor(info.roadblock)"></i>
        </div>
        {{ info.typeName }}
      </div>
      <div class="roadblock-details">{{ info.displayText }}</div>
      <div class="roadblock-id">{{ info.roadblock.ID }}</div>

      <!-- Separator with AND/OR condition between roadblocks -->
      <div class="roadblock-separator-container" *ngIf="i < roadblockInfos.length - 1">
        <div class="roadblock-separator"></div>
        <div class="roadblock-and-or-condition" *ngIf="roadblockInfos.length > 1">
          <div class="and-or-text"
               [class.and-active]="andOrCondition === 'AND'"
               [class.or-active]="andOrCondition === 'OR'">
            {{ andOrCondition || 'OR' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Single roadblock display template -->
<ng-template #singleRoadblock>
  <div class="roadblock-info-container" *ngIf="roadblock">
    <div class="roadblock-info-box">
      <!-- Single roadblock type with lock icon indicator -->
      <div class="roadblock-type">
        <div *ngIf="shouldShowLockIcon(roadblock)"
             class="roadblock-lock-container"
             [style.background-color]="getRoadblockColor(roadblock)">
          <i class="pe-7s-lock roadblock-lock-icon" [style.color]="getLockIconColor(roadblock)"></i>
        </div>
        {{ roadblockTypeName }}
      </div>
      <div class="roadblock-details">{{ getDisplayText() }}</div>
      <div class="roadblock-id">{{ roadblock.ID }}</div>
    </div>
  </div>
</ng-template>
