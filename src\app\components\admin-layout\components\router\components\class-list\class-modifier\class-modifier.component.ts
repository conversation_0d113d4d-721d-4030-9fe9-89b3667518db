import { ChangeDetectorRef, Component, ElementRef, QueryList, ViewChildren } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Class, Item } from 'src/app/lib/@bus-tier/models';
import { Atributte } from 'src/app/lib/@bus-tier/models/Atributte';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ClassService, ItemService } from 'src/app/services';
import { AtributteService } from 'src/app/services/atributte.service';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';

@Component({
  selector: 'app-class-modifier',
  templateUrl: './class-modifier.component.html',
  styleUrls: ['./class-modifier.component.scss']
})
export class ClassModifierComponent extends SortableListComponent<Class> {

    atributteList: Atributte[] = [];
    classes: Class[] = [];
    @ViewChildren('InputClassModifier') inputs: QueryList<ElementRef>;
    description: string;
    itemClasses: ItemClass[] = [];
    listDateItems: Item[] = [];
    activeLanguage = 'PTBR';
    conditionFields: string[] = ['Name','Description'];


  constructor(
    private spinnerService: SpinnerService,
    private _atributteService: AtributteService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _classService: ClassService,
    private ref: ChangeDetectorRef,
    private _itemService: ItemService,
    private customService: CustomService,
    private _itemClassService: ItemClassService,
  ) {
    super(_classService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable =
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  }

  public override async ngOnInit() {

    await this._atributteService.toFinishLoading();
    await this._classService.toFinishLoading();
    this.atributteList = this._atributteService.models;
    this.classes = this._classService.models
    this.itemClasses = this._itemClassService.models;
 
    this.itemClasses.forEach((rel) => {
      if(rel.id === "IC25") {// id das Relíquias
        this.listDateItems = this._itemService.models.filter((op)=> rel.itemIds.find((x)=> x == op.id))
      }
    })
    this.description = `Showing ${this.classes.length} results`;
  }

  public GateXPItemClassChanged(value: any) 
  {
    //Emitir mensagem se a selelão é diferente de Relíquias
    if(value.target.value !== 'IC25')
    Alert.showError("Can only select Relics.")!
  }

  changeClassAmount(value, classList: any, index: number) {
    classList.amountClass[index] = value == '' ? undefined : +value;
    this._classService.svcToModify(classList);
    this._classService.toSave();
  }


  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if (lines.length === 0) {
        this.displayErrors([`The copied content is empty.`]);
        this.spinnerService.setState(false);
        return;
    }

    const excelHeaders = lines[0].split(/\t/).map(header => header.trim());
    const expectedColumnsCount = this.atributteList.length + 2;
    const classErrors: string[] = []; 
    const nameErrors: string[] = []; 

    for (let index = 0; index < this.classes.length; index++) {
      this.classes[index].amountClass = [];
      this.classes[index].descriptionItem = '';
      this.classes[index].nameItem = '';
    }

    // Verifica se o número de colunas no Excel é o mesmo que o esperado
    if (excelHeaders.length !== expectedColumnsCount) {
        this.displayErrors([`The copied data has ${excelHeaders.length} columns, but ${expectedColumnsCount} were expected.`]);
        this.spinnerService.setState(false);
        return;
    }

    lines.slice(0).forEach((line, rowIndex) => {
        const cols = line.split(/\t/).map(col => col.trim());
        const className = cols[0];
        const lastColumnName = cols[cols.length - 1];

        // Verifica se o nome da classe existe no array this.classes
        const classObj = this.classes.find(c => c.name === className);
        if (!classObj) {
            classErrors.push(className);
        } else {
            // Inicializa o amountClass se necessário
            classObj.amountClass = classObj.amountClass || [];

            cols.slice(1, this.atributteList.length + 1).forEach((colValue, colIndex) => {
                if (colValue === "") {
                    classObj.amountClass[colIndex] = undefined; 
                } else if (!isNaN(parseFloat(colValue))) {
                    classObj.amountClass[colIndex] = parseFloat(colValue);         
                }
            });

            // Verifica se o nome da última coluna existe no array this.listDateItems
            const dateItem = this.listDateItems.find(item => item.name === lastColumnName);
            if (dateItem) {      
                classObj.descriptionItem = dateItem.description;
                classObj.nameItem = lastColumnName;
            } else {
                nameErrors.push(lastColumnName);
            }
        }
    });

    if (classErrors.length > 0) {
      this.displayErrors([`Class names not found in the system: ${classErrors.join(', ')}`]);
    }

    if (nameErrors.length > 0) {
      this.displayErrors([`Content from the 'name' column not found: ${nameErrors.join(', ')}`]);
    }

    if (classErrors.length === 0 && nameErrors.length === 0) {
        this._classService.models = this.classes;
        this._classService.toSave();
        Alert.ShowSuccess('Class Modifier imported successfully!');     
        this.lstFetchLists();
        this.ref.detectChanges();
        this.lstInit();
    }

    this.spinnerService.setState(false);
}


displayErrors(errors: string[]): boolean {
    if (errors.length) {
        this.spinnerService.setState(false);
        Alert.showError(`${errors.join('\n')}`);
        return true;
    }
    return false;
}


}
