/* Pre-roll Choice Popup - Gives players control over risk vs. certainty */

/* Full-screen overlay for the choice popup */
.dice-choice-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8); /* Semi-transparent dark background */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99998; /* Lower than dice overlay but above dialogue content */
  animation: fadeIn 0.3s ease-in;
}

/* Main content container with dark gradient styling */
.dice-choice-content {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(60, 60, 60, 0.95));
  border: 3px solid #888;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  color: white;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.9);
  animation: slideIn 0.5s ease-out;
  min-width: 400px;
  max-width: 500px;
}

/* Header section with icon and explanatory text */
.dice-choice-header {
  margin-bottom: 30px;
}

/* Large dice emoji icon */
.dice-choice-icon {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

/* Main title text */
.dice-choice-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 10px 0;
  color: #fff;
}

/* Subtitle explaining the choice */
.dice-choice-subtitle {
  font-size: 16px;
  color: #ccc;
  margin: 0;
}

/* Container for the three choice buttons */
.dice-choice-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

/* Individual choice button styling with hover effects */
.dice-choice-button {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border: 2px solid transparent;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;

  /* Hover effect: lift button and add shadow */
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }

  /* Positive choice: Green theme for guaranteed success */
  &.positive {
    border-color: #4CAF50;

    &:hover {
      background: rgba(76, 175, 80, 0.2);
      border-color: #66BB6A;
    }
  }

  /* Negative choice: Red theme for guaranteed failure */
  &.negative {
    border-color: #F44336;

    &:hover {
      background: rgba(244, 67, 54, 0.2);
      border-color: #EF5350;
    }
  }

  /* Roll dice choice: Orange theme for random outcome */
  &.roll-dice {
    border-color: #FF9800;

    &:hover {
      background: rgba(255, 152, 0, 0.2);
      border-color: #FFB74D;
    }
  }
}

/* Icon section of each button */
.button-icon {
  font-size: 24px;
  margin-right: 15px;
  min-width: 30px;
}

/* Text section of each button */
.button-text {
  text-align: left;
  flex: 1;
}

.button-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 4px;
}

.button-description {
  font-size: 14px;
  color: #ccc;
}



@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to { 
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}
