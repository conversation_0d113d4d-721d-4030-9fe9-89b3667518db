<div class="card list-header"
style="height: 70px; margin: 30px; margin-bottom: 0px;">
  <div class="header">
    <button class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('class-selection')">
      1 - Item Class
    </button>
    <button class="{{activeTab === 'particle-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('particle-selection')">
      2 - Particle Selection
    </button>
  </div>
</div>

<app-particle-class-selection *ngIf="activeTab === 'class-selection'"> </app-particle-class-selection>
<app-particle-item-selection *ngIf="activeTab === 'particle-selection'" (itemSelected)="switchToTab('item-record')"> </app-particle-item-selection>
