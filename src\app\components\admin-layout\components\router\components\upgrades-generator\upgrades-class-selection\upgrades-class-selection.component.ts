import { Component, OnInit } from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';

@Component({
  selector: 'app-upgrades-class-selection',
  templateUrl: './upgrades-class-selection.component.html',
  styleUrls: ['./upgrades-class-selection.component.scss'],
})
export class UpgradesClassSelectionComponent implements OnInit 
{

  itemClasses: ItemClass[];
  custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _customService: CustomService,
  ) {

  }

  async ngOnInit(): Promise<void>
  {
    this.itemClasses = this._itemClassService.models;
    this.custom = await this._customService.svcGetInstance();
    if(!this.custom.upgradesClassItem)
    {
      this.custom.upgradesClassItem = [];
    }
  }

  async onItemClassSelected(itemClass: ItemClass)
  {
    this._customService.toggleItemClass(itemClass, 'upgradesClassItem');
    this.custom = await this._customService.svcGetInstance();
  }

}
