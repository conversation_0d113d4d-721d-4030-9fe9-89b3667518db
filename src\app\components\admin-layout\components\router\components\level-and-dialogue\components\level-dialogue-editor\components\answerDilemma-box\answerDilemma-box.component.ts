import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Area, Box, Character, Dialogue, DilemmaBox, Item, Level, Mission, SpokePlace, StoryProgress } from 'src/app/lib/@bus-tier/models';
import { Dilemma } from 'src/app/lib/@bus-tier/models/Dilemma';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';
import { AreaService, DialogueService, ItemService, LevelService, OptionService, ReviewService } from 'src/app/services';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SpeechService } from 'src/app/services/speech.service';
import { Alert } from 'src/lib/darkcloud';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { EventType, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { AnswerDilemmaBox } from '../../../../../../../../../../lib/@bus-tier/models/AnswerDilemmaBox';
import { AnswerDilemmaBoxService } from '../../../../../../../../../../services/answer-dilemmaBox.service';
import { LevelHelperService } from '../../../../../../../../../../services/level-helper.service';
import { StoryBoxService } from './../../../../../../../../../../services/story-box.service';

interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}

@Component({
  selector: 'app-answerDilemma-box',
  templateUrl: './answerDilemma-box.component.html',
  styleUrls: ['./answerDilemma-box.component.scss']
})

export class AnswerBoxDilemmaComponent implements OnInit, AfterViewInit {
  @Input() option: Dilemma;
  @Input() answerBox: AnswerDilemmaBox;
  @Input() preloadedSpeakers: Character[];
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() optionBox: DilemmaBox;
  @Input() language: string;
  @Output() refresh: EventEmitter<void> = new EventEmitter();
  @Output() updateOption: EventEmitter<Dilemma> = new EventEmitter();


  timeout
  toMoveStoryProgressFunc: (marker: any) => void;
  toRemoveStoryProgressFunc: (marker: any) => void;
  toRemoveProcessConditionFunc: (roadblock: any) => void;
  answerDilBox: AnswerDilemmaBox;
  roadBlockId: string;
  myRoadblock: RoadBlock;
  selectedAnswerDilBox: string[] = []
  public roadblock: RoadBlock;
  isUsedRoad = false;

  trackByIndex(index: number, box: Box): any {
    return index;
  }

  public itemList: Item[] = [];
  roadBlocksUseds: RoadBlock[] = []
  hasLabelText: boolean = false;
  countingRoadblocks: number = 0;
  usedRoadBlocks = [];
  usedOnLevels = [];
  positive: string;
  isTextValid = true;
  listSpokePlace: SpokePlace[] = [];

  constructor(
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    private _optionService: OptionService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _change: ChangeDetectorRef,
    private _roadblockService: RoadBlockService,
    private _itemService: ItemService,
    private _levelHelperService: LevelHelperService,
    private _dialogueService: DialogueService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _reviewService: ReviewService,

  ) {
    this.toMoveStoryProgressFunc = this.toMoveStoryProgress.bind(this);
    this.toRemoveStoryProgressFunc = this.toRemoveStoryProgress.bind(this);
    this.toRemoveProcessConditionFunc = this.toRemoveRoadblock.bind(this);
  }

  async ngOnInit() {
    this._answerDilemmaBoxService.toFinishLoading();
    this.answerDilBox = this._answerDilemmaBoxService.svcFindById(this.option.idDilemmaBox);
    this.listSpokePlace = this._levelHelperService.models;
    await this._roadblockService.toFinishLoading();
    this.itemList = this._itemService.models;
    this.roadblock = this._roadblockService.filterByStoryBoxId(this.answerDilBox?.id);

    if (this.option.AndOrCondition == undefined) {
      this.option['AndOrCondition'] = 'OR';
    }
    this.roadblocksForKeyInformation();
    this.getRoadblockInThisBox();
  }


async roadblocksForKeyInformation() {
  const roadblocks = this._roadblockService.models.filter((roadblock) => {
    return roadblock.spokeElementId === this.answerDilBox?.id;
  });

  for (const roadblock of roadblocks) {
    const dialogueIds = this._dialogueService.models.filter((dialogue) => {
      return (
        roadblock.StoryBoxId &&
        !dialogue.id.includes('ML') &&
        roadblock.StoryBoxId.includes(dialogue.id)
      );
    });

    for (const dialogue of dialogueIds) {
      if (dialogue.boxIds.length > 0) {
        const areaId = Area.getSubIdFrom(dialogue.id);
        const area = this._areaService.svcFindById(areaId);
        if (!area) return;
  
        const levelId = Level.getSubIdFrom(dialogue.id);
        const level = this._levelService.svcFindById(levelId);
        const dialogueId = Dialogue.getSubIdFrom(dialogue.id, 'PT-BR');
        const dialogueData = this._dialogueService.svcFindById(dialogueId);
        const hierarchyCode = area.hierarchyCode;
        const levelIndex = this._reviewService.reviewResults[levelId]?.index;
        const type = GameTypes.dialogueTypeDisplay[+dialogueData.type];
  
        if (levelIndex && level?.name) {
          this.usedOnLevels.push(`[${hierarchyCode}] ${levelIndex} "${level.name}" (${type})`);
          this.usedRoadBlocks.push(roadblock);
        }
      }
    }
  }
}

  isUsedRoadblock(answerDilBox: AnswerDilemmaBox): boolean {
    // Retorna true se encontrar um elemento em usedRoadBlocks com o mesmo spokeElementId que o id do answerBox
    const isUsed = this.usedRoadBlocks.some(rb => rb.spokeElementId === answerDilBox.id);

    if (isUsed) {
      this.isUsedRoad = true;
    } else {
      this.isUsedRoad = false;
    }
    return isUsed;
  }


  countRoadblocks() {
    this.countingRoadblocks = 0;
    this.roadBlocksUseds.forEach(rb => {
      if (this.answerDilBox?.id.includes(rb?.StoryBoxId) && rb?.StoryBoxId != this.optionBox.id) {
        this.countingRoadblocks++;
      }
    })
  }

  async changeLabel(event: Event, answerDilBox: AnswerDilemmaBox) {
    const inputElement = event.target as HTMLInputElement;
    const text = inputElement.value.trim();

   const existingLabels = {};
    this.listSpokePlace.forEach((spoke) => {
      existingLabels[spoke.originalLabel] = true;
    });

    const spoke = this.listSpokePlace.find(
      (spoke) => spoke.elementId === answerDilBox.id
    );

    if (text === '') {
      inputElement.value = '<<Label for progress condition>>';
      answerDilBox.label = undefined;     
      this._answerDilemmaBoxService.svcToModify(answerDilBox);
      this._answerDilemmaBoxService.toSave();

      if (spoke) {  
        this.removeBDLabelPlaces(answerDilBox.id, spoke.id)
       }

       this.refresh.emit();  
    } 
    else {
      if (existingLabels[text]) {
      // Label já existe na base de dados do Places 
       Alert.showError('This Label ALREADY Exists!!', '');

        if(answerDilBox.label === undefined) {
            inputElement.value = '<<Label for progress condition>>';
            this._change.detectChanges();
          } else {
           inputElement.value = answerDilBox.label;
        }
        this.refresh.emit();  
      } 
      else {
        // Atualiza label
        if (spoke) {
          spoke.originalLabel = text;
          spoke.text = '[DilemmaBox > Answer] ' + text;
          this._levelHelperService.svcToModify(spoke);
        } 
        else {
          // Adiciona label
          const helper: SpokePlaceHelper = {
            elementId: answerDilBox.id,
            label: text,
            component: '[DilemmaBox > Answer]',
            text: '[DilemmaBox > Answer] ' + text,
          };
          this._levelHelperService.createNewLevelHelper(helper);
        }
        inputElement.value = text;
        answerDilBox.label = text;
        this._answerDilemmaBoxService.svcToModify(answerDilBox);
      }
    }

  }

  async removeBDLabelPlaces(idAnswerBox: string, idPlaces: string) { 
    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === idAnswerBox) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });

    await this._levelHelperService.svcToRemove(idPlaces);   
  }

  public async toAddSpeech() {
    try {
      const speech = await this._speechService.svcPromptCreateNew(this.answerBox?.id);
      this._speechService.srvAdd(speech);

      await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, speech.id);
      this._change.detectChanges();
    }
    catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddEvent() {
    try {
      const event = await this._eventService.svcPromptCreateNew(this.answerDilBox.id);
      await this._eventService.srvAdd(event);
      await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, event.id);
      this._change.detectChanges();
    }
    catch (error) {
      Alert.showError(error);
    }
  }
  public async toAddMissionEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerDilBox.id);
    event.type = EventType.ASSIGN_MISSION;
    await this._eventService.srvAdd(event);
    await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, event.id);
    this._change.detectChanges();
  }

  public async toAddBossEvent() {
    try {
      const marker = await this._markerService.svcPromptCreateNew(this.answerDilBox?.id);
      marker.type = 0;//Pass this type here to make it be of boss event.
      await this._markerService.srvAdd(marker);
      await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, marker.id);
      this._change.detectChanges();
    }
    catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddItemEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerDilBox.id);
    event.type = EventType.GIVE_ITEM;
    await this._eventService.srvAdd(event);
    await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, event.id);
    this._change.detectChanges();
  }

  public async toAddCinematicEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerDilBox.id);
    event.type = EventType.PLAY_VIDEO;
    await this._eventService.srvAdd(event);
    await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, event.id);
    this._change.detectChanges();
  }

  public async toAddLoopEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerDilBox.id);
    event.type = EventType.REFUSE_PAYMENT;
    await this._eventService.srvAdd(event);
    await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, event.id);
    this._change.detectChanges();
  }

  public async toAddMarker() {
    try {
      const marker = await this._markerService.svcPromptCreateNew(this.answerDilBox.id);
      await this._markerService.srvAdd(marker);
      await this._answerDilemmaBoxService.addStoryProgress(this.answerDilBox, marker.id);
      this._change.detectChanges();
    }
    catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddRoadblock() {
    let roadblock = await this._roadblockService.svcPromptCreateNew(this.answerDilBox.id, RoadBlockType.OBTAINED_ITEM);
    this.roadBlockId = roadblock.id;
    await this._roadblockService.srvAdd(roadblock);
    this.roadBlocksUseds.push(roadblock)
    this._change.detectChanges();
    this.ngOnInit()
    this.refresh.emit();
  }

  async toRemoveRoadblock(roadblock: RoadBlock) {
    if (await Alert.showRemoveAlert(Typing.typeName(roadblock))) {
      this.roadBlockId = undefined;
      this._roadblockService.svcToRemove(roadblock.id);
      this.roadBlocksUseds = this.roadBlocksUseds.filter(rb => rb.id != roadblock.id);
      this._change.detectChanges();
      // this.refresh.emit();
      this.ngOnInit();
    }

  }

  public async toMoveStoryProgress(sp: StoryProgress, transpose: number) {
    if (this.answerDilBox.storyProgressIds.length <= 1) return;

    const oldIndex = this.answerDilBox.storyProgressIds.indexOf(sp.id);
    const newIndex = oldIndex + transpose;
    if (!this.answerDilBox.storyProgressIds[newIndex]) return;

    this.answerDilBox.storyProgressIds[oldIndex] =
      this.answerDilBox.storyProgressIds[newIndex];
    this.answerDilBox.storyProgressIds[newIndex] = sp.id;
    await this._answerDilemmaBoxService.svcToModify(this.answerDilBox);
  }

  async ngAfterViewInit() {
    await this._roadblockService.toFinishLoading();

    let roadblock = this._roadblockService.filterByStoryBoxId(this.answerDilBox?.id);
    this.roadblock = roadblock

    if (roadblock)
      this.roadBlockId = roadblock.ID;

    this._answerDilemmaBoxService.models.forEach(sb => {

      if (sb.label != undefined)
        this.selectedAnswerDilBox.push(sb.label)
    })

    this._optionService.models.forEach(sb => {
      if (sb.label != undefined)
        this.selectedAnswerDilBox.push(sb.label)
    })
  }

  getRoadblockInThisBox() {
    this.roadBlocksUseds = [];
    for (let i = 0; i < this._roadblockService.models.length; i++) {
      if (this._roadblockService.models[i]?.StoryBoxId?.trim() == this.answerDilBox?.id?.trim()) {
        this.roadBlocksUseds.push(this._roadblockService.models[i]);
      }
    }

    this.countRoadblocks();
  }

  public async toRemoveStoryProgress(sp: StoryProgress) {
    if (await Alert.showRemoveAlert(Typing.typeName(sp))) {
      await this._speechService.svcToRemove(sp.id);
      await this._eventService.svcToRemove(sp.id);
      await this._markerService.svcToRemove(sp.id);
      await this._answerDilemmaBoxService.toRemoveStoryProgress(this.answerDilBox.id, sp.id);
    }
    this._change.detectChanges();
    this.ngOnInit();
  }

  ngOnDestroy(): void {
    clearTimeout(this.timeout)
  }

  async chooseAndOrCondition(event) {
    //AND = FALSE, OR = TRUE.
    if (event.target.checked == false)
      this.answerDilBox['AndOrCondition'] = 'AND';
    else
      this.answerDilBox['AndOrCondition'] = 'OR';

    //await this._optionService.svcToModify(this.option);
    await this._answerDilemmaBoxService.toSave();
  }
}
